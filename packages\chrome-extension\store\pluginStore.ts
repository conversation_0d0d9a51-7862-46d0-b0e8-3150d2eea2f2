import type { PluginData } from '@/hooks/usePluginInstall'
import { BaseStore } from './baseStore'

export interface PluginInfo {
  name: string
  source: string
  code: string
}

const STORAGE_KEY = 'f2c:pluginStore'

export class PluginStore extends BaseStore {
  constructor() {
    super()
    this.loadFromStorage()
  }

  plugins: Record<string, PluginInfo> = {}
  activePluginSource: string | null = null

  private loadFromStorage() {
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        const data = JSON.parse(stored)
        this.plugins = data.plugins || {}
        this.activePluginSource = data.activePluginSource || null
      }
    } catch (e) {
      console.warn('Failed to load plugin store from localStorage:', e)
    }
  }

  private saveToStorage() {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify({
        plugins: this.plugins,
        activePluginSource: this.activePluginSource
      }))
    } catch (e) {
      console.warn('Failed to save plugin store to localStorage:', e)
    }
  }

  addPlugin(pluginData: PluginData) {
    this.plugins[pluginData.source] = {
      name: pluginData.pluginName,
      source: pluginData.source,
      code: pluginData.code
    }
    this.saveToStorage()
  }

  updatePlugin(pluginData: PluginData) {
    this.plugins[pluginData.source] = {
      name: pluginData.pluginName,
      source: pluginData.source,
      code: pluginData.code
    }
    this.saveToStorage()
  }

  removePlugin(source: string) {
    delete this.plugins[source]
    if (this.activePluginSource === source) {
      this.activePluginSource = null
    }
    this.saveToStorage()
  }

  setActivePlugin(source: string | null) {
    this.activePluginSource = source
    this.saveToStorage()
  }

  clearAllPlugins() {
    this.plugins = {}
    this.activePluginSource = null
    this.saveToStorage()
  }

  // Getters
  getActivePlugin(): PluginInfo | null {
    return this.activePluginSource ? this.plugins[this.activePluginSource] || null : null
  }

  getPlugin(source: string): PluginInfo | null {
    return this.plugins[source] || null
  }

  getAllPlugins(): PluginInfo[] {
    return Object.values(this.plugins)
  }
}

const pluginStore = new PluginStore()
export default pluginStore
