import Log from 'src/figma/utils/Log'
import {convertToCode} from '..'

async function main() {
  Log.info('开始执行...')
  let json = {}
  try {
    Log.info('发起请求...')
    const controller = new AbortController()
    const timeout = setTimeout(() => controller.abort(), 5000) // 5秒超时

    const response = await fetch(
      'http://localhost:3000/api/nodes?fileKey=paNLMeCtnaEPGr6QjtcaTz&nodeIds=2001:58&format=html&personal_token=figd_rg2T-YfN4vDWwS3ahRBpAlYmF5kI-8qv6vgPaJ1t',
      {
        signal: controller.signal,
      },
    ).finally(() => {
      clearTimeout(timeout)
    })

    Log.info('请求状态:', response.status)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    Log.info('开始解析响应...')
    const text = await response.text()
    Log.info('响应内容:', text)
    json = JSON.parse(text)
    Log.info('解析完成')
  } catch (error: any) {
    if (error.name === 'AbortError') {
      Log.info('请求超时')
    } else if (error instanceof TypeError) {
      Log.info('网络错误，请检查服务是否启动:', error.message)
    } else {
      Log.info('发生错误:', error)
    }
    process.exit(1)
  }
  const document = (Object.values((json as any).nodes)[0] as any)?.document
  if (!document) {
    Log.info('document 为空')
    process.exit(1)
  }

  const {files} = await convertToCode([document])
  Log.info('转换完成:', files)
}

main()
  .catch(error => {
    Log.info('未捕获的错误:', error)
    // process.exit(1)
  })
  .finally(() => {
    debugger
  })
