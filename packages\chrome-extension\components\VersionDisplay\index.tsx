import React, { useState, useEffect } from 'react'
import { extensionMessageService } from '@/lib/extension-message-service'

const VersionDisplay: React.FC = () => {
  const [version, setVersion] = useState<string>('加载中...')

  useEffect(() => {
    const loadVersion = async () => {
      try {
        const currentVersion = await extensionMessageService.getCurrentVersion()
        setVersion(currentVersion)
      } catch (error) {
        console.warn('获取版本失败:', error)
        setVersion('未知')
      }
    }

    loadVersion()
  }, [])

  return (
    <div className="text-xs text-gray-400 text-center">
      v{version}
    </div>
  )
}

export default VersionDisplay
