<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Figma OAuth 授权成功</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    .container {
      background-color: #f5f5f5;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    h1 {
      color: #333;
      border-bottom: 1px solid #ddd;
      padding-bottom: 10px;
    }
    .token-info {
      background-color: #fff;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      margin: 15px 0;
      word-break: break-all;
    }
    .token-label {
      font-weight: bold;
      margin-bottom: 5px;
      color: #555;
    }
    .token-value {
      font-family: monospace;
      background-color: #f8f8f8;
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #eee;
    }
    .copy-btn {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 8px 12px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 14px;
      margin: 4px 2px;
      cursor: pointer;
      border-radius: 4px;
    }
    .success-icon {
      color: #4CAF50;
      font-size: 48px;
      text-align: center;
      margin-bottom: 20px;
    }
    .ext-btn {
      background-color: #2196F3;
      color: white;
      border: none;
      padding: 10px 15px;
      text-align: center;
      text-decoration: none;
      display: block;
      font-size: 16px;
      margin: 10px auto 20px;
      cursor: pointer;
      border-radius: 4px;
      width: 300px;
    }
  </style>
</head>
<body>
  <!-- 添加发送消息到Chrome扩展的按钮 -->
  <button class="ext-btn" onclick="sendMessageToExt()">发送消息到Chrome扩展</button>
  
  <!-- 添加发送消息到父窗口的按钮 -->
  <button class="ext-btn" onclick="sendMessageToParent()" id="parent-msg-btn" style="display: none;">发送数据到父窗口</button>
  
  <div class="container">
    <div class="success-icon">✓</div>
    <h1>Figma OAuth 授权成功</h1>
    <p>您已成功授权应用访问您的 Figma 账户。以下是您的访问令牌信息：</p>
    
    <div class="token-info">
      <div class="token-label">访问令牌 (Access Token):</div>
      <div class="token-value" id="access-token">{{ accessToken }}</div>
      <button class="copy-btn" onclick="copyToClipboard('access-token')">复制</button>
    </div>
    
    <div class="token-info">
      <div class="token-label">令牌类型 (Token Type):</div>
      <div class="token-value">{{ tokenType }}</div>
    </div>
    
    <div class="token-info">
      <div class="token-label">过期时间 (Expires In):</div>
      <div class="token-value" id="expires-in">{{ expiresIn }} 秒</div>
    </div>
    
    <div class="token-info">
      <div class="token-label">刷新令牌 (Refresh Token):</div>
      <div class="token-value" id="refresh-token">{{ refreshToken }}</div>
      <button class="copy-btn" onclick="copyToClipboard('refresh-token')">复制</button>
    </div>
    
    <p>请保存这些信息，特别是访问令牌，以便在应用中使用。</p>
    <p>您现在可以关闭此页面并返回应用。</p>
  </div>

  <script>
    function copyToClipboard(elementId) {
      const element = document.getElementById(elementId);
      const text = element.textContent;
      
      navigator.clipboard.writeText(text).then(() => {
        alert('已复制到剪贴板');
      }).catch(err => {
        console.error('复制失败:', err);
        alert('复制失败，请手动复制');
      });
    }
    
    // 检测是否在iframe中
    function isInIframe() {
      try {
        return window.self !== window.top;
      } catch (e) {
        return true;
      }
    }
    
    // 从cookies中读取指定名称的值
    function getCookie(name) {
      const value = `; ${document.cookie}`;
      const parts = value.split(`; ${name}=`);
      if (parts.length === 2) return parts.pop().split(';').shift();
      return null;
    }
    
    // 将认证信息写入localStorage
    function saveAuthToLocalStorage() {
      const yyuid = getCookie('yyuid');
      if (yyuid) {
        // 从URL参数获取认证信息
        const urlParams = new URLSearchParams(window.location.search);
        
        // 优先使用模板变量，没有值时才使用URL参数
        const templateAccessToken = '{{ accessToken }}';
        const templateTokenType = '{{ tokenType }}';
        const templateExpiresIn = '{{ expiresIn }}';
        
        // 根据URL判断使用哪种方式获取值
        let accessToken, tokenType, expiresIn;
        if (window.location.href.includes('astro.yy.com/oauth/callback')) {
          // 如果URL包含astro.yy.com/oauth/callback，优先使用URL参数
          accessToken = urlParams.get('accessToken')
          tokenType = urlParams.get('tokenType')
          expiresIn = urlParams.get('expiresIn')
        } else {
          // 否则优先使用模板变量
          accessToken = templateAccessToken
          tokenType = templateTokenType
          expiresIn = templateExpiresIn
        }
        
        // 检查当前URL是否不包含astro.yy.com/oauth/callback
        if (!window.location.href.includes('astro.yy.com/oauth/callback')) {
          // 如果有这三个参数，则跳转到astro.yy.com/oauth/callback并带上参数
          if (accessToken && tokenType && expiresIn) {
            const redirectUrl = new URL('https://astro.yy.com/oauth/callback');
            redirectUrl.searchParams.set('accessToken', accessToken);
            redirectUrl.searchParams.set('tokenType', tokenType);
            redirectUrl.searchParams.set('expiresIn', expiresIn);
            
            // 添加当前URL的code参数
            const code = urlParams.get('code');
            if (code) {
              redirectUrl.searchParams.set('code', code);
            }
            
            console.log('跳转到astro.yy.com/oauth/callback:', redirectUrl.toString());
            window.location.href = redirectUrl.toString();
            return; // 跳转后不再执行后续代码
          }
        }
        
        // 检查是否同时存在这3个参数
        if (accessToken && tokenType && expiresIn) {
          // 将expiresIn从秒数转换为实际到期时间戳
          const expirationTimestamp = Date.now() + (parseInt(expiresIn) * 1000);
          // 写入localStorage
          localStorage.setItem(`astro-local-figmaAuthTime-${yyuid}`, expirationTimestamp.toString());
          localStorage.setItem(`astro-local-figmaAccessToken-${yyuid}`, accessToken);
          localStorage.setItem(`astro-local-figmaIsAuth-${yyuid}`, 'true');
          
          // 同时将这3个值以对象字符串的形式写到剪切板
          const authData = {
            isAuth: 'true',
            authTime: expirationTimestamp.toString(),
            accessToken: accessToken
          };
          
          navigator.clipboard.writeText(JSON.stringify(authData)).then(() => {
            console.log('认证数据已复制到剪切板:', authData);
          }).catch(err => {
            console.error('复制到剪切板失败:', err);
          });
          
          console.log('认证信息已保存到localStorage:', {
            yyuid: yyuid,
            tokenType: tokenType,
            expiresIn: expiresIn,
            accessToken: accessToken
          });
        } else {
          console.log('URL参数中缺少必要的认证信息 (accessToken, tokenType, expiresIn)，跳过localStorage保存');
        }
      } else {
        console.log('未找到yyuid cookie，跳过localStorage保存');
      }
    }
    
    // 页面加载时检测iframe环境并保存认证信息
    window.addEventListener('load', function() {
      if (isInIframe()) {
        document.getElementById('parent-msg-btn').style.display = 'block';
      }
      
      // 检查是否为astro.yy.com/oauth/callback页面
      if (window.location.href.includes('astro.yy.com/oauth/callback')) {
        // 隐藏除了成功标题外的所有UI元素
        const elementsToHide = [
          '.ext-btn',
          '.token-info',
          'p'
        ];
        
        elementsToHide.forEach(selector => {
          const elements = document.querySelectorAll(selector);
          elements.forEach(element => {
            element.style.display = 'none';
          });
        });
        
        // 只保留成功图标和标题
        document.querySelector('h1').textContent = 'Figma OAuth 授权成功';
      }
      
      // 保存认证信息到localStorage
      saveAuthToLocalStorage();
    });
    
    // 收集表单数据的函数
    function collectFormData() {
      const accessToken = document.getElementById('access-token').textContent.trim();
      const refreshToken = document.getElementById('refresh-token').textContent.trim();
      const urlParams = new URLSearchParams(window.location.search);
      
      return {
        accessToken: accessToken,
        refreshToken: refreshToken,
        tokenType: '{{ tokenType }}',
        expiresIn: '{{ expiresIn }}',
        code: urlParams.get('code') || '',
        timestamp: new Date().toISOString(),
        source: 'figma-oauth-success'
      };
    }
    
    // 发送消息到父窗口的函数
    function sendMessageToParent() {
      if (!isInIframe()) {
        alert('当前页面不在iframe中，无法发送消息到父窗口');
        return;
      }
      
      const formData = collectFormData();
      
      try {
        // 发送消息到父窗口
        window.parent.postMessage({
          type: 'FIGMA_OAUTH_SUCCESS',
          data: formData
        }, '*');
        
        alert('数据已发送到父窗口');
        console.log('发送到父窗口的数据:', formData);
      } catch (error) {
        console.error('发送消息到父窗口失败:', error);
        alert('发送消息失败: ' + error.message);
      }
    }
    
    // 添加发送消息到Chrome扩展的函数
    function sendMessageToExt() {
      const editorExtensionId = 'fgdfmlfpnekfpjnoconiflhfpnigoecn';
      const accessToken = document.getElementById('access-token').textContent;
      const urlParams = new URLSearchParams(window.location.search);
      const code = urlParams.get('code') || '';
      
      if(chrome && chrome.runtime) {
        chrome.runtime.sendMessage(
          editorExtensionId, {
            code: code,
            token: accessToken
          },
          (res) => {
            console.log('收到来自Chrome扩展的响应:', res);
          }
        );
        alert('消息已发送到Chrome扩展');
      } else {
        alert('无法访问Chrome扩展API，请确保您正在使用Chrome浏览器');
      }
    }
  </script>
</body>
</html>