{"name": "@baidu/f2c-dsl-adapter", "version": "0.0.2", "description": "", "main": "dist/index.mjs", "maintainers": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "type": "commonjs", "files": ["dist", "src", "tsup.config.ts"], "exports": {".": {"import": "./dist/index.mjs"}}, "scripts": {"dev": "tsup --watch --config tsup.config.ts", "build": "tsup --config tsup.config.ts", "tsup": "tsup", "release": "npm run build && npx empkg publish --unpkg", "test": "bun build src/__test/test.ts --outfile=dist/test.js --target=node --drop=console && TEST=1 node --experimental-fetch dist/test.js"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/lodash": "^4.14.202", "@types/node": "^20.10.1", "fs-extra": "^11.2.0", "tsup": "^8.0.1", "typescript": "^5.3.2"}, "eslintConfig": {"root": true, "ignorePatterns": ["dist"], "extends": ["@empjs/eslint-config-react"]}, "dependencies": {"@baidu/f2c-dsl-react": "^0.2.32", "@baidu/f2c-plugin-base": "^0.0.23", "@figma/rest-api-spec": "^0.24.0", "lodash": "^4.17.21", "nanoid": "^5.0.4"}}