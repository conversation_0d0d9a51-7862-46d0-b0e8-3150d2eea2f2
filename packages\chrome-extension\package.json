{"name": "f2c", "description": "manifest.json description", "private": true, "version": "1.4.0", "type": "module", "scripts": {"dev": "wxt --mode development", "dev:firefox": "wxt -b firefox --mode development", "build": "wxt build --mode production", "build:firefox": "wxt build -b firefox --mode production", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "compile": "tsc --noEmit", "postinstall": "wxt prepare", "version:current": "node scripts/version-manager.cjs current", "version:patch": "node scripts/version-manager.cjs patch", "version:minor": "node scripts/version-manager.cjs minor", "version:major": "node scripts/version-manager.cjs major", "version:sync": "node scripts/version-manager.cjs sync"}, "dependencies": {"@figma-plugin/helpers": "^0.15.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-popover": "^1.1.10", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.0", "@reactuses/core": "^6.0.1", "@tailwindcss/vite": "^4.0.13", "@types/react-router-dom": "^5.3.3", "base64-js": "^1.5.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jszip": "^3.10.1", "lucide-react": "^0.482.0", "motion": "^12.7.4", "next-themes": "^0.4.6", "overlayscrollbars": "^2.11.4", "overlayscrollbars-react": "^0.5.6", "p-wait-for": "^5.0.2", "prismjs": "^1.30.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.1", "react-inlinesvg": "^4.2.0", "react-router-dom": "^7.6.3", "sonner": "^2.0.5", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.13", "tailwindcss-animate": "^1.0.7", "valtio": "^2.1.4", "zod": "^3.24.3"}, "devDependencies": {"@figma/plugin-typings": "^1.109.0", "@types/chrome": "^0.0.280", "@types/prismjs": "^1.26.5", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.2", "@wxt-dev/module-react": "^1.1.2", "typescript": "^5.6.3", "wxt": "^0.19.29"}}