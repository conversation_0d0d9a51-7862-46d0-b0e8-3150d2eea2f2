import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js'
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js'
import { z } from 'zod'
import { FigmaApiService } from './services/figmaApi'

const server = new McpServer({
  name: 'F2C MCP',
  version: '0.0.1',
})

server.tool(
  'figma_to_html',
  '将 Figma 文件中的节点转换为 HTML 内容',
  {
    personalToken: z.string().describe('您的 Figma 个人访问令牌'),
    fileKey: z.string().describe('Figma 文件的 fileKey'),
    nodeId: z.string().describe('Figma 文件的 nodeId'),
  },
  async ({ personalToken, fileKey, nodeId }) => {
    //https://www.figma.com/design/QHetJn44JMLGuVsuqqUgBW/Untitled?node-id=461-1497&t=yT7CmxBCsnL5q7Ls-0
    try {
      const figmaApiService = new FigmaApiService(personalToken)

      // 添加调试日志
      console.log(`[调试] 使用 fileKey='${fileKey}', nodeId='${nodeId}'`)

      if (!fileKey || !nodeId) {
        // Add error log before throwing
        console.error(`解析失败: fileKey='${fileKey}', nodeId='${nodeId}'`)
        throw new Error('fileKey 或 node-id 不能为空')
      }

      // 直接调用 Figma API 获取 HTML
      const htmlContent = await figmaApiService.getNodeHtml(fileKey, nodeId)

      // 返回 HTML 内容
      return {
        content: [{ type: 'text', text: htmlContent }],
      };
    } catch (error: any) {
      console.error('处理 figmaToHtml 请求时出错:', error)
      // 返回错误信息给客户端
      return {
        content: [{ type: 'text', text: `错误: ${error.message}` }],
      }
    }
  }
)

// 启动服务器并连接到 stdio 传输
async function startServer() {
  console.log('启动 Figma-to-HTML MCP 服务器...')
  const transport = new StdioServerTransport()
  await server.connect(transport)
  console.log('MCP 服务器已连接到 stdio')
}

startServer().catch(error => {
  console.error('启动 MCP 服务器失败:', error)
  process.exit(1)
})
