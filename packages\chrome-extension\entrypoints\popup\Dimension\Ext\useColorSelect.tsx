import {useRef, useState} from 'react'
import {ColorOptions, DemisionColor} from '@/lib/constants'
import {type IColorObj, rgbaToHsba, rgbaToHsla, toHexString, toRgbaString} from '@/lib/color-utils'
import Select from '@/components/ui/select'

const UseColorSelect = () => {
  const [colorFomat, setColorFormar] = useState<DemisionColor>(DemisionColor.HEX)
  const colorFomatRef = useRef<(colorObj: IColorObj) => string>((value)=>'')
  const UI = (
    <div className="extColor flex justify-between items-center pt-1 pb-1 text-[12px] pl-[5px]">
      <span className="text-[#7f7f7f] mr-4">{'色值表示方式'}</span>
      <div className="flex justify-between max-w-[218px] grow">
        <Select
          options={ColorOptions}
          variant="filled"
          // className="border-0 w-full"
          value={colorFomat}
          onChange={value => {
            setColorFormar(value as DemisionColor)
          }}
        />
      </div>
    </div>
  )
  switch (colorFomat) {
    case DemisionColor.HEX:
      colorFomatRef.current = toHexString
      break
    case DemisionColor.RGBA:
      colorFomatRef.current = toRgbaString
      break
    case DemisionColor.HSLA:
      colorFomatRef.current = rgbaToHsla
      break
    case DemisionColor.HSBA:
      colorFomatRef.current = rgbaToHsba
      break
  }
  return {ColorSelect: UI, colorFormat: colorFomatRef.current, color: colorFomat}
}
export default UseColorSelect
