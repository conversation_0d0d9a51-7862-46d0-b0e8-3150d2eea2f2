#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 获取项目根目录
const projectRoot = path.resolve(__dirname, '..');
const packageJsonPath = path.join(projectRoot, 'package.json');
const wxtConfigPath = path.join(projectRoot, 'wxt.config.ts');

/**
 * 读取package.json文件
 */
function readPackageJson() {
  try {
    const content = fs.readFileSync(packageJsonPath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error('读取package.json失败:', error.message);
    process.exit(1);
  }
}

/**
 * 写入package.json文件
 */
function writePackageJson(packageData) {
  try {
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageData, null, 2) + '\n');
    console.log('✅ package.json 更新成功');
  } catch (error) {
    console.error('写入package.json失败:', error.message);
    process.exit(1);
  }
}

/**
 * 读取wxt.config.ts文件
 */
function readWxtConfig() {
  try {
    return fs.readFileSync(wxtConfigPath, 'utf8');
  } catch (error) {
    console.error('读取wxt.config.ts失败:', error.message);
    process.exit(1);
  }
}

/**
 * 更新wxt.config.ts中的版本号
 */
function updateWxtConfigVersion(newVersion) {
  try {
    let content = readWxtConfig();
    
    // 使用正则表达式替换manifest中的version
    const versionRegex = /(manifest:\s*{[\s\S]*?version:\s*['"`])([^'"`]+)(['"`])/;
    const match = content.match(versionRegex);
    
    if (match) {
      content = content.replace(versionRegex, `$1${newVersion}$3`);
      fs.writeFileSync(wxtConfigPath, content);
      console.log('✅ wxt.config.ts 更新成功');
    } else {
      console.warn('⚠️  在wxt.config.ts中未找到version字段');
    }
  } catch (error) {
    console.error('更新wxt.config.ts失败:', error.message);
    process.exit(1);
  }
}

/**
 * 解析版本号
 */
function parseVersion(version) {
  const parts = version.split('.').map(Number);
  if (parts.length !== 3 || parts.some(isNaN)) {
    throw new Error(`无效的版本号格式: ${version}`);
  }
  return {
    major: parts[0],
    minor: parts[1],
    patch: parts[2]
  };
}

/**
 * 格式化版本号
 */
function formatVersion(major, minor, patch) {
  return `${major}.${minor}.${patch}`;
}

/**
 * 增加版本号
 */
function incrementVersion(currentVersion, type) {
  const { major, minor, patch } = parseVersion(currentVersion);
  
  switch (type) {
    case 'major':
      return formatVersion(major + 1, 0, 0);
    case 'minor':
      return formatVersion(major, minor + 1, 0);
    case 'patch':
      return formatVersion(major, minor, patch + 1);
    default:
      throw new Error(`无效的版本类型: ${type}. 支持的类型: major, minor, patch`);
  }
}

/**
 * 同步版本号到所有配置文件
 */
function syncVersion(newVersion) {
  // 更新package.json
  const packageData = readPackageJson();
  packageData.version = newVersion;
  writePackageJson(packageData);
  
  // 更新wxt.config.ts
  updateWxtConfigVersion(newVersion);
  
  console.log(`🎉 版本号已同步更新为: ${newVersion}`);
}

/**
 * 获取当前版本号
 */
function getCurrentVersion() {
  const packageData = readPackageJson();
  return packageData.version;
}

/**
 * 主函数
 */
function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  if (!command) {
    console.log('版本管理工具使用说明:');
    console.log('  node scripts/version-manager.js current          # 显示当前版本');
    console.log('  node scripts/version-manager.js patch            # 增加补丁版本 (x.x.X)');
    console.log('  node scripts/version-manager.js minor            # 增加次要版本 (x.X.0)');
    console.log('  node scripts/version-manager.js major            # 增加主要版本 (X.0.0)');
    console.log('  node scripts/version-manager.js sync <version>   # 同步指定版本到所有配置文件');
    return;
  }
  
  try {
    switch (command) {
      case 'current':
        console.log(`当前版本: ${getCurrentVersion()}`);
        break;
        
      case 'patch':
      case 'minor':
      case 'major':
        const currentVersion = getCurrentVersion();
        const newVersion = incrementVersion(currentVersion, command);
        syncVersion(newVersion);
        break;
        
      case 'sync':
        const targetVersion = args[1];
        if (!targetVersion) {
          console.error('错误: 请提供要同步的版本号');
          process.exit(1);
        }
        // 验证版本号格式
        parseVersion(targetVersion);
        syncVersion(targetVersion);
        break;
        
      default:
        console.error(`未知命令: ${command}`);
        process.exit(1);
    }
  } catch (error) {
    console.error('错误:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { getCurrentVersion, syncVersion, incrementVersion };
