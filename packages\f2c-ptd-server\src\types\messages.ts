// 消息类型定义
export interface BaseMessage {
  type: string;
  timestamp?: number;
  id?: string;
}

// 频道相关消息
export interface JoinChannelMessage extends BaseMessage {
  type: "join";
  channel: string;
  userId?: string;
}

// 普通聊天消息
export interface ChatMessage extends BaseMessage {
  type: "message";
  message: string;
  channel: string;
  userId?: string;
}

// MCP 命令消息
export interface MCPCommandMessage extends BaseMessage {
  type: "message";
  message: {
    command: string;
    args?: any;
    requestId?: string;
  };
  channel: string;
  userId?: string;
}

// 系统消息
export interface SystemMessage extends BaseMessage {
  type: "system";
  message: string;
  level?: "info" | "warning" | "error";
}

// 错误消息
export interface ErrorMessage extends BaseMessage {
  type: "error";
  error: string;
  code?: string;
  details?: any;
}

// 响应消息
export interface ResponseMessage extends BaseMessage {
  type: string;
  // requestId: string;
  message?: any;
  channel: string;
}

// 联合类型
export type SocketMessage =
  | JoinChannelMessage
  | ChatMessage
  | MCPCommandMessage
  | SystemMessage
  | ErrorMessage
  | ResponseMessage;

// 消息验证函数
export function isValidMessage(data: any): data is SocketMessage {
  return data && typeof data === "object" && typeof data.type === "string";
}

export function isJoinMessage(data: any): data is JoinChannelMessage {
  return (
    isValidMessage(data) &&
    data.type === "join" &&
    typeof data.channel === "string"
  );
}

export function isChatMessage(data: any): data is ChatMessage {
  return isValidMessage(data) && data.type === "message";
}

export function isMCPCommandMessage(data: any): data is MCPCommandMessage {
  return (
    isValidMessage(data) &&
    data.type === "message" &&
    typeof data.message === "object" &&
    data.message !== null &&
    typeof data.message.command === "string" &&
    typeof data.channel === "string"
  );
}

export function isSystemMessage(data: any): data is SystemMessage {
  return (
    isValidMessage(data) &&
    data.type === "system" &&
    typeof data.message === "string"
  );
}
