import {ReactDSL} from '@baidu/f2c-dsl-react/dist/index'
import type {File, Option} from '@baidu/f2c-plugin-base'
import {initAdapters} from '../figma'
import type {JsonAdapter} from '../figma/jsonAdapter'
import {RestApiAdapter} from '../figma/restApiAdapter'

export const generateCodingFiles = async (node: JsonAdapter, option: Option): Promise<File[]> => {
  const dsl = new ReactDSL(node, option, {
    initAdapters,
    FigmaNodeAdapter: RestApiAdapter,
  })
  const files = await dsl.generate()
  return files
}
