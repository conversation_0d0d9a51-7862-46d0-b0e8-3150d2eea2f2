import { BaseStore } from './baseStore'

// 视图类型枚举
export enum ViewType {
  CODE = 'code',
  DESIGN = 'design'
}

// UI Store 类
export class UIStore extends BaseStore {
  constructor() {
    super()
  }

  // 状态属性
  currentView: ViewType = ViewType.CODE
  isPanelExpanded: boolean = true
  isSidebarVisible: boolean = true
  isLoading: boolean = false
  error: string | null = null

  // 视图切换方法
  switchView(view: ViewType) {
    this.currentView = view
  }

  switchToCodeView() {
    this.currentView = ViewType.CODE
  }

  switchToDesignView() {
    this.currentView = ViewType.DESIGN
  }

  // 面板控制方法
  togglePanel() {
    this.isPanelExpanded = !this.isPanelExpanded
  }

  expandPanel() {
    this.isPanelExpanded = true
  }

  collapsePanel() {
    this.isPanelExpanded = false
  }

  // 侧边栏控制方法
  toggleSidebar() {
    this.isSidebarVisible = !this.isSidebarVisible
  }

  showSidebar() {
    this.isSidebarVisible = true
  }

  hideSidebar() {
    this.isSidebarVisible = false
  }

  // 加载状态方法
  setLoading(loading: boolean) {
    this.isLoading = loading
  }

  // 错误处理方法
  setError(error: string | null) {
    this.error = error
  }

  clearError() {
    this.error = null
  }

  // 重置状态方法
  resetUI() {
    this.currentView = ViewType.CODE
    this.isPanelExpanded = true
    this.isSidebarVisible = true
    this.isLoading = false
    this.error = null
  }
}

// 创建并导出store实例
export const uiStore = new UIStore()

