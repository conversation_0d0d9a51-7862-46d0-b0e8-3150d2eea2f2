import type {<PERSON>son<PERSON>dapter} from '../jsonAdapter'
import {RestApiAdapter} from '../restApiAdapter'
import {PostionalRelationship} from '../type'
import {isEmpty} from '../utils/utils'

const overlapAnnotation = 'checkedForOverlap'
export const absolutePositioningAnnotation = 'absolutePositioning'

// 如果有节点相交则组成一个组
// export const groupNodesByOverlap = (nodes: JsonAdapter[]): JsonAdapter[] => {
//   if (nodes.length < 2) {
//     return nodes
//   }

//   const skippable = new Set<string>()
//   const processed: JsonAdapter[] = []

//   // console.log('before groupNodesByOverlap', nodes);

//   for (let i = 0; i < nodes.length; i++) {
//     const currentNode = nodes[i]

//     if (currentNode.hasAnnotation(overlapAnnotation)) {
//       return nodes
//     }

//     if (skippable.has(currentNode.getId())) {
//       continue
//     }

//     const overlappingNodes = findOverlappingNodes(currentNode, nodes, new Set<string>())

//     if (isEmpty(overlappingNodes)) {
//       processed.push(currentNode)
//       continue
//     }

//     overlappingNodes.forEach(overlappingNode => {
//       overlappingNode.addAnnotations(overlapAnnotation, true)
//       skippable.add(overlappingNode.getId())
//     })

//     const newGroupNode = new RestApiAdapter(null, overlappingNodes)
//     newGroupNode.addAnnotations(absolutePositioningAnnotation, true)
//     processed.push(newGroupNode)
//     currentNode.addAnnotations(overlapAnnotation, true)
//   }

//   // console.log('after groupNodesByOverlap', processed);

//   return processed
// }

// 找到targetNodes中所有与startingNode以及其重叠节点重叠的节点
export const findOverlappingNodes = (
  startingNode: JsonAdapter,
  targetNodes: JsonAdapter[],
  currentPath: Set<string>,
): JsonAdapter[] => {
  for (let i = 0; i < targetNodes.length; i++) {
    const targetNode = targetNodes[i]

    if (currentPath.has(targetNode.getId())) {
      continue
    }

    const overlappingNodes = []

    if (startingNode.getPositionalRelationship(targetNode) === PostionalRelationship.OVERLAP) {
      overlappingNodes.push(targetNode)

      if (!currentPath.has(startingNode.getId())) {
        overlappingNodes.push(startingNode)
        currentPath.add(startingNode.getId())
      }
      currentPath.add(targetNode.getId())
    }

    let completePath = [...overlappingNodes]
    for (const overlappingNode of overlappingNodes) {
      const result = findOverlappingNodes(overlappingNode, targetNodes, currentPath)
      completePath = completePath.concat(...result)
    }

    if (completePath.length !== 0) {
      return completePath
    }
  }

  return []
}
