{"name": "f2c-mcp-bun", "module": "src/index.ts", "type": "module", "private": true, "scripts": {"dev": "bun run src/index.ts --watch", "start": "bun run src/index.ts"}, "dependencies": {"axios": "^1.6.7", "@modelcontextprotocol/sdk": "1.10.2", "zod": "^3.22.4"}, "devDependencies": {"@types/bun": "latest"}, "peerDependencies": {"typescript": "^5"}, "mcp": {"id": "f2c-mcp-bun", "entrypoint": "src/index.ts"}}