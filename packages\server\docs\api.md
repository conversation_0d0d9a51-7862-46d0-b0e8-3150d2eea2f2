# Figma API 服务文档

本文档介绍了Figma REST API封装服务的使用方法。所有API的域名前缀为：`https://f2c-figma-api.yy.com/api`。

## 认证
所有需要访问Figma数据的API端点都支持通过以下两种方式传递认证令牌：

1. **OAuth Access Token**: 通过URL查询参数 `access_token` 传递。
2. **Personal Access Token**: 通过URL查询参数 `personal_token` 传递。

**示例**:
`https://f2c-figma-api.yy.com/api/file?fileKey=YOUR_FILE_KEY&personal_token=YOUR_PERSONAL_TOKEN`
或者
`https://f2c-figma-api.yy.com/api/file?fileKey=YOUR_FILE_KEY&access_token=YOUR_OAUTH_ACCESS_TOKEN`

如果未提供有效的令牌或令牌权限不足，API可能会返回认证错误。

## API 详情
### 1. 获取文件信息 (`/file`)
获取指定Figma文件的元数据和基本信息。

* **URL**: `/file`
* **方法**: `GET`
* **参数**:
    * `fileKey` (string, **必需**): Figma文件的唯一标识符。
    * `access_token` (string, 可选): Figma OAuth访问令牌。
    * `personal_token` (string, 可选): Figma个人访问令牌。

* **成功响应 (200 OK)**:
    * 返回包含文件信息的JSON对象。

* **成功响应 (200 OK) 示例**:

  ```json
  {
    "document": {
      "id": "0:0",
      "name": "Page 1",
      "type": "CANVAS",
      "children": [
        {
          "id": "0:1",
          "name": "Frame 1",
          "type": "FRAME",
          // ... 其他节点属性
        }
      ]
    },
    "components": {
      // ... 文件中的组件信息
    },
    "componentSets": {
      // ... 文件中的组件集信息
    },
    "schemaVersion": 0,
    "styles": {
      // ... 文件中的样式信息
    },
    "name": "My Figma File",
    "lastModified": "2023-10-27T10:00:00Z",
    "thumbnailUrl": "[https://figma-alpha-api.s3.us-west-2.amazonaws.com/](https://figma-alpha-api.s3.us-west-2.amazonaws.com/)...",
    "version": "1234567890"
  }
  ```

* **示例请求**:

  ```
  [https://f2c-figma-api.yy.com/api/file?fileKey=DkzGbKo09kf2w1ytMPALxd&personal_token=YOUR_PERSONAL_TOKEN](https://f2c-figma-api.yy.com/api/file?fileKey=DkzGbKo09kf2w1ytMPALxd&personal_token=YOUR_PERSONAL_TOKEN)
  ```
### 2. 获取节点信息与转换 (`/nodes`)
获取Figma文件中一个或多个节点的详细信息，并支持将节点内容转换为HTML、JSON等格式。

* **URL**: `/nodes`
* **方法**: `GET`, `POST`
* **GET 参数**:
    * `fileKey` (string, **必需**): Figma文件的唯一标识符。如果提供了`figma_link`且其中包含`fileKey`，则此参数可选。
    * `nodeIds` (string, **必需**): 一个或多个节点ID，以逗号分隔 (例如: `80-1956,80-1957`)。如果提供了`figma_link`且其中包含`node-id`，则此参数可选。
    * `format` (string, 可选): 输出格式。支持的值包括：
        * `html`: 将节点转换为HTML代码。
        * `files`: 将节点转换为包含多个文件描述的JSON结构（适用于组件化输出）。
        * `allFiles`: 与`files`类似，但可能包含更全面的数据，如图片元数据。
        * 如果此参数为空或未提供，则返回原始的Figma节点JSON数据。

    * `figma_link` (string, 可选): Figma文件的URL链接，例如 `https://www.figma.com/design/your_file_key/project-name?node-id=123-456`。服务会尝试从此链接中解析 `fileKey` 和 `nodeIds`。如果同时提供了`fileKey`/`nodeIds`和`figma_link`，则优先使用直接提供的`fileKey`/`nodeIds`。
    * `option` (string, 可选): JSON字符串格式的附加转换选项。
        * 可以包含 `scaleSize` (number, 图片缩放比例，默认为1) 和 `imgFormat` (string, 图片格式，如 `png`, `svg`, `jpg`, 默认为`png`)。示例: `option={"scaleSize": 2, "imgFormat": "svg"}`。
        * 当`format`不是`html`时，可以包含`cssFramework`, 
            * 返回html: 示例: `option={"cssFramework": "inlinecss"}`。
            * 返回react+cssmodules，示例: `option={"cssFramework": "cssmodules"}`
            * 返回react+tailwind，示例: `option={"cssFramework": "tailwindcss"}`


    * `access_token` (string, 可选): Figma OAuth访问令牌。
    * `personal_token` (string, 可选): Figma个人访问令牌。

* **POST 参数**:
    * 支持 `application/json` 和 `multipart/form-data` 格式的请求体。
    * 参数与GET请求相同，通过请求体传递。

* **成功响应 (200 OK)**:
    * 如果 `format=html`: 返回 `text/html` 类型的HTML内容。
    * 如果 `format=files` 或 `format=allFiles`: 返回 `application/json` 类型的JSON对象。
    * 如果 `format` 未指定: 返回 `application/json` 类型的原始Figma节点数据。

* **成功响应 (200 OK) 示例**:
    * **当 **`format`** 未指定时 (原始Figma节点数据)**:


  ```
  {
    "nodes": {
      "80:1956": {
        "document": {
          "id": "80:1956",
          "name": "Login Button",
          "type": "COMPONENT",
          "children": [
            // ... 子节点信息
          ]
          // ... 其他节点属性
        },
        "components": {
          // ... 节点相关的组件信息
        },
        "schemaVersion": 0,
        "styles": {
          // ... 节点相关的样式信息
        }
      }
    },
    "name": "My Figma File",
    "lastModified": "2023-10-27T10:00:00Z",
    "thumbnailUrl": "[https://figma-alpha-api.s3.us-west-2.amazonaws.com/](https://figma-alpha-api.s3.us-west-2.amazonaws.com/)...",
    "version": "1234567890"
  }
  ```
    * **当 **`format=html`** 时**:

```
<div style="display: flex; flex-direction: column; align-items: center; justify-content: center; background-color: #ffffff; padding: 20px; border-radius: 8px;">
  <img src="[https://figma-alpha-api.s3.us-west-2.amazonaws.com/](https://figma-alpha-api.s3.us-west-2.amazonaws.com/)..." alt="icon" style="width: 50px; height: 50px; margin-bottom: 10px;" />
  <button style="background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px;">Click Me</button>
</div>
```
    * **当 **`format=files`** 时**:

```
{
  "files": [
    {
      "path": "components/Button/index.html",
      "content": "<button class=\"button_primary\">Submit</button>"
    },
    {
      "path": "components/Button/style.css",
      "content": ".button_primary { background-color: blue; color: white; }"
    }
  ]
}
```
    * **当 **`format=allFiles`** 时 (与 **`files`** 类似，可能包含更详细的图片信息)**:

```
{
  "files": [
    {
      "path": "MyComponent/index.html",
      "content": "<div><img src=\"[https://image.url/IMAGE_NODE_ID_REF](https://image.url/IMAGE_NODE_ID_REF)\" alt=\"My Image\"><span>Hello</span></div>"
    },
    {
      "path": "MyComponent/index.js",
      "content": "console.log('MyComponent loaded');"
    }
  ],
  "images": {
    "[https://image.url/IMAGE_NODE_ID_REF](https://image.url/IMAGE_NODE_ID_REF)": {
        "id": "IMAGE_NODE_ID",
        "name": "My Image",
        "fileExt": "png",
        "nodeType": "IMAGE"
    }
  }
}
```
* **示例请求 (GET)**:
    * 获取原始节点数据:


```
[https://f2c-figma-api.yy.com/api/nodes?fileKey=YOUR_FILE_KEY&nodeIds=NODE_ID_1,NODE_ID_2&personal_token=YOUR_PERSONAL_TOKEN](https://f2c-figma-api.yy.com/api/nodes?fileKey=YOUR_FILE_KEY&nodeIds=NODE_ID_1,NODE_ID_2&personal_token=YOUR_PERSONAL_TOKEN)
```
    * 获取HTML格式:

```
[https://f2c-figma-api.yy.com/api/nodes?fileKey=DkzGbKo09kf2w1ytMPALxd&nodeIds=80-1956&format=html&personal_token=YOUR_PERSONAL_TOKEN](https://f2c-figma-api.yy.com/api/nodes?fileKey=DkzGbKo09kf2w1ytMPALxd&nodeIds=80-1956&format=html&personal_token=YOUR_PERSONAL_TOKEN)
```
    * 使用 `figma_link` 获取HTML:

```
[https://f2c-figma-api.yy.com/api/nodes?figma_link=https://www.figma.com/design/q40bzrJSSMlqgGHYxJVXXP/Filename?node-id=526-12275&format=html&personal_token=YOUR_PERSONAL_TOKEN](https://f2c-figma-api.yy.com/api/nodes?figma_link=https://www.figma.com/design/q40bzrJSSMlqgGHYxJVXXP/Filename?node-id=526-12275&format=html&personal_token=YOUR_PERSONAL_TOKEN)
```
    * 获取 `files` 格式并指定图片选项:

```
[https://f2c-figma-api.yy.com/api/nodes?fileKey=YOUR_FILE_KEY&nodeIds=NODE_ID_1&format=files&option=](https://f2c-figma-api.yy.com/api/nodes?fileKey=YOUR_FILE_KEY&nodeIds=NODE_ID_1&format=files&option=){"scaleSize":1.5,"imgFormat":"svg"}&personal_token=YOUR_PERSONAL_TOKEN
```
* **示例请求 (POST, application/json)**:
    * URL: `https://f2c-figma-api.yy.com/api/nodes`
    * Headers: `Content-Type: application/json`
    * Body:


```
{
  "fileKey": "YOUR_FILE_KEY",
  "nodeIds": "NODE_ID_1,NODE_ID_2",
  "format": "html",
  "personal_token": "YOUR_PERSONAL_TOKEN"
}
```
### 3. 获取图片 (`/images`)
获取Figma文件中指定节点的图片渲染链接。

* **URL**: `/images`
* **方法**: `GET`
* **参数**:
    * `fileKey` (string, **必需**): Figma文件的唯一标识符。
    * `nodeIds` (string, **必需**): 一个或多个节点ID，以逗号分隔，用于生成图片。
    * `format` (string, 可选): 图片格式。支持的值: `jpg`, `png`, `svg`, `pdf`。默认为 `png`。
    * `scale` (number, 可选): 图片缩放比例。默认为 `1`。
    * `access_token` (string, 可选): Figma OAuth访问令牌。
    * `personal_token` (string, 可选): Figma个人访问令牌。

* **成功响应 (200 OK)**:
    * 返回JSON对象，其中包含一个 `images` 字段，该字段是一个对象，键是节点ID (原始格式，例如 `80:1956`)，值是对应图片的URL。

* **成功响应 (200 OK) 示例**:

```
{
  "err": null,
  "images": {
    "80:1956": "[https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/](https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/)...",
    "80:1957": "[https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/](https://figma-alpha-api.s3.us-west-2.amazonaws.com/images/)..."
  }
}
```
* **示例请求**:

```
[https://f2c-figma-api.yy.com/api/images?fileKey=YOUR_FILE_KEY&nodeIds=NODE_ID_1,NODE_ID_2&format=png&scale=2&personal_token=YOUR_PERSONAL_TOKEN](https://f2c-figma-api.yy.com/api/images?fileKey=YOUR_FILE_KEY&nodeIds=NODE_ID_1,NODE_ID_2&format=png&scale=2&personal_token=YOUR_PERSONAL_TOKEN)
```
### 4. 获取项目文件列表 (`/project-files`)
获取指定Figma项目中的文件列表。

* **URL**: `/project-files`
* **方法**: `GET`
* **参数**:
    * `projectId` (string, **必需**): Figma项目的唯一标识符。
    * `access_token` (string, 可选): Figma OAuth访问令牌。
    * `personal_token` (string, 可选): Figma个人访问令牌。

* **成功响应 (200 OK)**:
    * 返回包含项目文件列表的JSON对象。

* **成功响应 (200 OK) 示例**:

```
{
  "project_files": [
    {
      "key": "FILE_KEY_1",
      "name": "Design System",
      "thumbnail_url": "[https://figma-alpha-api.s3.us-west-2.amazonaws.com/](https://figma-alpha-api.s3.us-west-2.amazonaws.com/)...",
      "last_modified": "2023-10-26T12:00:00Z"
    },
    {
      "key": "FILE_KEY_2",
      "name": "Mobile App Mockups",
      "thumbnail_url": "[https://figma-alpha-api.s3.us-west-2.amazonaws.com/](https://figma-alpha-api.s3.us-west-2.amazonaws.com/)...",
      "last_modified": "2023-10-27T09:30:00Z"
    }
  ],
  "name": "My Awesome Project"
}
```
* **示例请求**:

```
[https://f2c-figma-api.yy.com/api/project-files?projectId=YOUR_PROJECT_ID&personal_token=YOUR_PERSONAL_TOKEN](https://f2c-figma-api.yy.com/api/project-files?projectId=YOUR_PROJECT_ID&personal_token=YOUR_PERSONAL_TOKEN)
```
## 注意事项
* **Node ID 格式**: 节点ID通常格式为 `数字-数字` (例如 `123-456`)。API内部会处理将其转换为Figma API可能需要的 `数字:数字` 格式。
* **CORS**: API已配置CORS头，允许跨域请求。