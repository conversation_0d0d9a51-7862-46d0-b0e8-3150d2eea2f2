根据 adapter.ts 文件的实现，以下是 `FigmaNodeAdapter` 类中的所有方法，并按功能和作用归类，每个方法用一句话解释其作用。

### 构造函数
- **`constructor(node: SceneNode | null, children: FigmaNodeAdapter[] = [], type = 'VISIBLE', option: Option = { uiFramework: UiFramework.react, absoluteLayout: false })`**
  - **作用**: 初始化 `FigmaNodeAdapter` 实例。

### 获取和设置属性的方法
- **`getChildren(): FigmaNodeAdapter[]`**
  - **作用**: 获取子节点。

- **`getReactionsChangeNodeAdapter()`**
  - **作用**: 获取反应变化节点适配器。

- **`setProcessedNode(node: SceneNode | null)`**
  - **作用**: 设置已处理的节点。

- **`getProcessedNode(): SceneNode | null`**
  - **作用**: 获取已处理的节点。

- **`setStylesClassName(stylesClassName: string)`**
  - **作用**: 设置样式类名。

- **`setName(name: string)`**
  - **作用**: 设置节点名称。

- **`setReactions(reactions: Reaction)`**
  - **作用**: 设置反应。

- **`setReactionsState(reactionsState)`**
  - **作用**: 设置反应状态。

- **`setReactionsChangeNodeAdapter(reactionsChangeNodeAdapter: FigmaNodeAdapter)`**
  - **作用**: 设置反应变化节点适配器。

- **`setCssAttributes(attributes: Attributes, isFilter = true)`**
  - **作用**: 设置 CSS 属性。

- **`addCssAttributes(attributes: Attributes)`**
  - **作用**: 添加 CSS 属性。

- **`filterCssAttributes(option: FilterOption = { zeroValueAllowed: false, truncateNumbers: true, absolutePositioningFilter: false, marginFilter: false, imgPropsFilter: false })`**
  - **作用**: 过滤 CSS 属性。

- **`addChildren(children: FigmaNodeAdapter[])`**
  - **作用**: 添加子节点。

- **`setChildren(children: FigmaNodeAdapter[])`**
  - **作用**: 设置子节点。

- **`addPositionalCssAttributes(attributes: Attributes)`**
  - **作用**: 添加位置 CSS 属性。

- **`removePositionalCssAttributes(key: string)`**
  - **作用**: 移除位置 CSS 属性。

- **`setPositionalCssAttributes(attributes: Attributes, isFilter = true)`**
  - **作用**: 设置位置 CSS 属性。

- **`getFilterPositionalCssAttributes(option: FilterOption = { zeroValueAllowed: false, truncateNumbers: true, absolutePositioningFilter: false, marginFilter: false }): Attributes`**
  - **作用**: 获取过滤后的位置 CSS 属性。

- **`getAPositionalAttribute(key: string): string`**
  - **作用**: 获取单个位置 CSS 属性。

- **`setType(type: NodeType)`**
  - **作用**: 设置节点类型。

- **`getOriginalType()`**
  - **作用**: 获取原始类型。

- **`getOriginalId()`**
  - **作用**: 获取原始 ID。

- **`resetOrder()`**
  - **作用**: 重置顺序。

- **`getOrder()`**
  - **作用**: 获取顺序。

- **`setOrder(order: string)`**
  - **作用**: 设置顺序。

- **`getPregeneratingOrder()`**
  - **作用**: 获取预生成顺序。

- **`setPregeneratingOrder(order: string)`**
  - **作用**: 设置预生成顺序。

### 计算和获取坐标的方法
<!-- - **`getRenderingBoxWidthAndHeight(): number[]`**
  - **作用**: 获取渲染盒子的宽度和高度。

- **`getAbsBoundingBoxWidthAndHeight(): number[]`**
  - **作用**: 获取绝对边界盒的宽度和高度。 -->

- **`getComputedCoordinates()`**
  - **作用**: 获取计算后的坐标。

- **`getComputedContentCoordinates()`**
  - **作用**: 获取计算后的内容坐标。在计算重叠关系的时候用

- **`getAbsoluteBoundingBoxCoordinates(): BoxCoordinates`**
  - **作用**: 获取绝对边界盒坐标。

- **`getRenderingBoundsCoordinates(): BoxCoordinates`**
  - **作用**: 获取渲染边界坐标。用于获取实际渲染的大小，对于后期 grouping 也有至关作用

- **`getPositionalRelationship(targetNode: FigmaNodeAdapter): PostionalRelationship`**
  - **作用**: 获取位置关系。

- **`areThereOverflowingChildren(): boolean`**
  - **作用**: 检查是否有溢出的子节点。

### 文本和样式相关的方法
- **`getFamilyName(): string`**
  - **作用**: 获取字体家族名称。

- **`initText(): string`**
  - **作用**: 初始化文本。

- **`isItalic(): boolean`**
  - **作用**: 检查是否为斜体。

- **`getStyles({ whType, unit = 'px', kebabcase = false }: { whType?: 'boundingbox' | 'renderbounds', unit?: string, kebabcase?: boolean })`**
  - **作用**: 获取样式。

- **`export(exportFormat: ExportFormat, exportSettings?: Partial<ExportSettings>): Promise<string>`**
  - **作用**: 导出节点。


Figma 的 REST API 提供的节点数据与 Figma 插件 API 中的节点数据有一些差异。以下是对 `FigmaNodeAdapter` 类中使用的 `this.node` 属性和方法在 Figma REST API 中的可用性分析：

### 可用属性和方法

1. **id**: 可用
2. **name**: 可用
3. **type**: 可用
4. **arcData**: 可用（仅适用于 `ELLIPSE` 类型节点）
5. **cornerRadius**: 可用（仅适用于 `RECTANGLE` 类型节点）
6. **effects**: 可用
7. **absoluteBoundingBox**: 可用
8. **absoluteRenderBounds**: 不可用（REST API 中没有直接对应的属性）
9. **blendMode**: 可用
10. **opacity**: 可用
11. **rotation**: 可用
12. **strokes**: 可用
13. **fills**: 可用
14. **clipsContent**: 可用（仅适用于 `FRAME`、`COMPONENT` 和 `INSTANCE` 类型节点）
15. **paragraphIndent**: 可用（仅适用于 `TEXT` 类型节点）
16. **fontName**: 可用（仅适用于 `TEXT` 类型节点）
17. **fontSize**: 可用（仅适用于 `TEXT` 类型节点）
18. **textAutoResize**: 不可用（REST API 中没有直接对应的属性）
19. **textAlignHorizontal**: 可用（仅适用于 `TEXT` 类型节点）
20. **textDecoration**: 可用（仅适用于 `TEXT` 类型节点）
21. **lineHeight**: 可用（仅适用于 `TEXT` 类型节点）
22. **textCase**: 可用（仅适用于 `TEXT` 类型节点）
23. **letterSpacing**: 可用（仅适用于 `TEXT` 类型节点）
24. **fontWeight**: 可用（仅适用于 `TEXT` 类型节点）
25. **textAlignVertical**: 可用（仅适用于 `TEXT` 类型节点）
26. **openTypeFeatures**: 可用（仅适用于 `TEXT` 类型节点）
27. **characters**: 可用（仅适用于 `TEXT` 类型节点）
28. **paragraphSpacing**: 可用（仅适用于 `TEXT` 类型节点）
29. **strokeWeight**: 可用（仅适用于 `STROKES` 类型节点）
30. **strokeTopWeight**: 不可用（REST API 中没有直接对应的属性）
31. **strokeBottomWeight**: 不可用（REST API 中没有直接对应的属性）
32. **strokeLeftWeight**: 不可用（REST API 中没有直接对应的属性）
33. **strokeRightWeight**: 不可用（REST API 中没有直接对应的属性）
34. **vectorNetwork**: 可用（仅适用于 `VECTOR` 类型节点）
35. **listOptions**: 可用（仅适用于 `TEXT` 类型节点）
36. **indentation**: 可用（仅适用于 `TEXT` 类型节点）
37. **hyperlink**: 可用（仅适用于 `TEXT` 类型节点）

### 不可用或需要转换的属性和方法

- **absoluteRenderBounds**: REST API 中没有直接对应的属性。
- **textAutoResize**: REST API 中没有直接对应的属性。
- **strokeTopWeight**: REST API 中没有直接对应的属性。
- **strokeBottomWeight**: REST API 中没有直接对应的属性。
- **strokeLeftWeight**: REST API 中没有直接对应的属性。
- **strokeRightWeight**: REST API 中没有直接对应的属性。

### 总结

大部分属性和方法在 Figma REST API 中都有对应的实现，但有一些属性（如 `absoluteRenderBounds` 和 `textAutoResize`）在 REST API 中没有直接对应的属性，需要进行额外的处理或转换。总体来说，切换到 Figma REST API 中的节点数据是可行的，但需要对某些属性进行适当的处理和转换。
