// Plugin executor - handles plugin loading and execution

// Plugin execution context interface
export interface PluginContext {
  // Chrome extension APIs
  chrome: typeof chrome

  // Utility functions
  utils: {
    toast: (message: string, type?: 'success' | 'error' | 'info') => void
    log: (message: string, ...args: any[]) => void
  }

  // Extension state access
  state: {
    getCurrentTab: () => Promise<chrome.tabs.Tab | null>
    getStorage: (key: string) => Promise<any>
    setStorage: (key: string, value: any) => Promise<void>
  }
}

// Plugin transform interfaces
export interface TransformParams {
  code: string
  style: Record<string, string>
  options: {
    useRem: boolean
    rootFontSize: number
    scale: number
  }
}

export interface TransformOptions {
  title?: string
  lang?: string
  transform?: (params: TransformParams) => string
}

export type CodeTransformConfig = TransformOptions | false

// Plugin interface
export interface Plugin {
  name: string
  version?: string
  description?: string

  // Plugin lifecycle hooks
  onInstall?: (context: PluginContext) => void | Promise<void>
  onUninstall?: (context: PluginContext) => void | Promise<void>
  onActivate?: (context: PluginContext) => void | Promise<void>
  onDeactivate?: (context: PluginContext) => void | Promise<void>

  // Plugin functionality
  commands?: Record<string, (context: PluginContext, ...args: any[]) => any>

  // Code transformation
  code?: {
    css?: CodeTransformConfig
    js?: CodeTransformConfig
  }

  ui?: {
    panels?: Array<{
      id: string
      title: string
      component: React.ComponentType<any>
    }>
    menuItems?: Array<{
      id: string
      label: string
      action: (context: PluginContext) => void
    }>
  }
}

// Import check regex - same as tempad-dev
const IMPORT_RE = /^\s*import\s+(([^'"\n]+|'[^']*'|"[^"]*")|\s*\(\s*[^)]*\s*\))/gm

// Safe evaluation function using dynamic import - same as tempad-dev
async function evaluate(code: string) {
  // Check for import statements (not allowed)
  if (IMPORT_RE.test(code)) {
    throw new Error('`import` is not allowed in plugins.')
  }

  const blob = new Blob([code], {type: 'text/javascript'})
  const url = URL.createObjectURL(blob)

  try {
    const module = await import(url)
    return module
  } finally {
    URL.revokeObjectURL(url)
  }
}

// Plugin executor class
export class PluginExecutor {
  private plugins: Map<string, Plugin> = new Map()
  private activePlugin: string | null = null

  constructor() {
    // Don't load plugins in constructor to avoid React hooks issues
    // Plugins will be loaded by the usePluginExecutor hook
  }

  private async loadInstalledPlugins() {
    // This will be called from the hook instead
    // to avoid issues with React hooks in constructor
  }

  async loadPlugin(source: string, code: string): Promise<void> {
    try {
      console.log(`Loading plugin from ${source}...`)
      console.log('Plugin code preview:', code.substring(0, 200) + '...')

      // Evaluate plugin code safely using dynamic import - same as tempad-dev
      const exports = await evaluate(code)
      console.log('Plugin evaluation result:', exports)

      // Extract plugin from exports - same as tempad-dev
      const plugin = (exports.default || exports.plugin) as Plugin

      if (!plugin || typeof plugin !== 'object') {
        throw new Error('Invalid plugin format')
      }

      if (!plugin.name) {
        throw new Error('Plugin must have a name')
      }

      console.log(`Plugin "${plugin.name}" parsed successfully:`, {
        name: plugin.name,
        version: plugin.version,
        hasCodeTransform: !!plugin.code,
        hasCSSTransform: !!plugin.code?.css,
        hasCommands: !!plugin.commands,
      })

      this.plugins.set(source, plugin)

      // Call onInstall hook if exists
      if (plugin.onInstall) {
        await plugin.onInstall(this.createContext())
      }

      console.log(`Plugin "${plugin.name}" loaded successfully`)
    } catch (error) {
      console.error(`Failed to load plugin from ${source}:`, error)
      throw error
    }
  }

  async unloadPlugin(source: string): Promise<void> {
    const plugin = this.plugins.get(source)
    if (!plugin) return

    try {
      // Deactivate if active
      if (this.activePlugin === source) {
        await this.deactivatePlugin(source)
      }

      // Call onUninstall hook if exists
      if (plugin.onUninstall) {
        await plugin.onUninstall(this.createContext())
      }

      this.plugins.delete(source)
      console.log(`Plugin "${plugin.name}" unloaded successfully`)
    } catch (error) {
      console.error(`Failed to unload plugin ${plugin.name}:`, error)
    }
  }

  async activatePlugin(source: string): Promise<void> {
    const plugin = this.plugins.get(source)
    if (!plugin) {
      throw new Error(`Plugin not found: ${source}`)
    }

    // Deactivate current plugin if any
    if (this.activePlugin && this.activePlugin !== source) {
      await this.deactivatePlugin(this.activePlugin)
    }

    try {
      // Call onActivate hook if exists
      if (plugin.onActivate) {
        await plugin.onActivate(this.createContext())
      }

      this.activePlugin = source
      console.log(`Plugin "${plugin.name}" activated`)
    } catch (error) {
      console.error(`Failed to activate plugin ${plugin.name}:`, error)
      throw error
    }
  }

  async deactivatePlugin(source: string): Promise<void> {
    const plugin = this.plugins.get(source)
    if (!plugin || this.activePlugin !== source) return

    try {
      // Call onDeactivate hook if exists
      if (plugin.onDeactivate) {
        await plugin.onDeactivate(this.createContext())
      }

      this.activePlugin = null
      console.log(`Plugin "${plugin.name}" deactivated`)
    } catch (error) {
      console.error(`Failed to deactivate plugin ${plugin.name}:`, error)
    }
  }

  executeCommand(pluginSource: string, command: string, ...args: any[]): any {
    const plugin = this.plugins.get(pluginSource)
    if (!plugin || !plugin.commands || !plugin.commands[command]) {
      throw new Error(`Command "${command}" not found in plugin`)
    }

    try {
      return plugin.commands[command](this.createContext(), ...args)
    } catch (error) {
      console.error(`Failed to execute command ${command}:`, error)
      throw error
    }
  }

  getActivePlugin(): Plugin | null {
    return this.activePlugin ? this.plugins.get(this.activePlugin) || null : null
  }

  getPlugin(source: string): Plugin | null {
    return this.plugins.get(source) || null
  }

  getAllPlugins(): Array<{source: string; plugin: Plugin}> {
    return Array.from(this.plugins.entries()).map(([source, plugin]) => ({
      source,
      plugin,
    }))
  }

  // Transform CSS code using active plugin
  transformCSS(
    code: string,
    style: Record<string, string>,
    options: TransformParams['options'],
  ): {code: string; title?: string; lang?: string} {
    const activePlugin = this.getActivePlugin()

    console.log('transformCSS called:', {
      hasActivePlugin: !!activePlugin,
      pluginName: activePlugin?.name,
      hasCSSConfig: !!activePlugin?.code?.css,
      code: code.substring(0, 100) + '...',
    })

    if (!activePlugin || !activePlugin.code?.css || activePlugin.code.css === false) {
      console.log('No active plugin or CSS config, returning original code')
      return {code, title: 'CSS', lang: 'css'}
    }

    const cssConfig = activePlugin.code.css
    let transformedCode = code

    if (cssConfig.transform) {
      try {
        console.log('Applying CSS transformation...')
        transformedCode = cssConfig.transform({code, style, options})
        console.log('CSS transformation result:', transformedCode.substring(0, 100) + '...')
      } catch (error) {
        console.error('Failed to transform CSS:', error)
        transformedCode = code
      }
    } else {
      console.log('No transform function in CSS config')
    }

    const result = {
      code: transformedCode,
      title: cssConfig.title || 'CSS',
      lang: cssConfig.lang || 'css',
    }

    console.log('transformCSS result:', result)
    return result
  }

  // Transform JS code using active plugin
  transformJS(
    code: string,
    style: Record<string, string>,
    options: TransformParams['options'],
  ): {code: string; title?: string; lang?: string} | null {
    const activePlugin = this.getActivePlugin()

    if (!activePlugin || !activePlugin.code?.js || activePlugin.code.js === false) {
      return null // Hide JS code block
    }

    const jsConfig = activePlugin.code.js
    let transformedCode = code

    if (jsConfig.transform) {
      try {
        transformedCode = jsConfig.transform({code, style, options})
      } catch (error) {
        console.error('Failed to transform JS:', error)
        transformedCode = code
      }
    }

    return {
      code: transformedCode,
      title: jsConfig.title || 'JavaScript',
      lang: jsConfig.lang || 'javascript',
    }
  }

  private createContext(): PluginContext {
    return {
      chrome,
      utils: {
        toast: (message: string, type: 'success' | 'error' | 'info' = 'info') => {
          // Use sonner toast - import dynamically to avoid issues
          import('sonner')
            .then(({toast}) => {
              if (type === 'success') {
                toast.success(message)
              } else if (type === 'error') {
                toast.error(message)
              } else {
                toast(message)
              }
            })
            .catch(() => {
              console.log(`[Plugin Toast] ${message}`)
            })
        },
        log: (message: string, ...args: any[]) => {
          console.log(`[Plugin] ${message}`, ...args)
        },
      },
      state: {
        getCurrentTab: async () => {
          try {
            const tabs = await chrome.tabs.query({active: true, currentWindow: true})
            return tabs[0] || null
          } catch (error) {
            console.error('Failed to get current tab:', error)
            return null
          }
        },
        getStorage: async (key: string) => {
          try {
            const result = await chrome.storage.local.get(key)
            return result[key]
          } catch (error) {
            console.error('Failed to get storage:', error)
            return null
          }
        },
        setStorage: async (key: string, value: any) => {
          try {
            await chrome.storage.local.set({[key]: value})
          } catch (error) {
            console.error('Failed to set storage:', error)
          }
        },
      },
    }
  }
}

// Global plugin executor instance
export const pluginExecutor = new PluginExecutor()
