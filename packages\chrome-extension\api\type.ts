export interface BucketOption {
  id: number
  name: string
  bucket: string
  cdnDomain: string
  businessId: number
}
export interface BusinessVO {
  id: number
  name: string
  reamrk: string
  createUid: number
  createUsername: string
  createTime: string
  lastOpUid: number
  lastOpUsername: string
  lastOpTime: string
  buckets: BucketOption[]
  baseUrl?: string[]
  sysFontFamily?: string[]
}
export interface ConfigVersionVO {
  id: number
  version: number
  configId: number
  configName: string
  createUid: number
  createUsername: string
  createTime: string
  bucketId: number
  tags: string[]
  bucketName: string
  value: string
}
export interface ConfigVO {
  id: number
  businessId: number
  name: string
  bucketId: number
  createUid: number
  createUsername: string
  createTime: string
  lastOpUid: number
  lastOpUsername: string
  lastOpTime: string
  lastVersion?: ConfigVersionVO
  groupId?: string
}
export interface UploadVO {
  configId: number
  version: number
  businessId: number
  name?: string
  groupId?: string
}

export interface BusiSettingVO {
  baseUrl?: string
  sysFontFamily?: string
}
