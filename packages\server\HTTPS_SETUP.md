# HTTPS 配置指南

本服务器已配置为支持 HTTPS。请按照以下步骤设置证书：

## 快速开始（开发环境）

### 1. 生成自签名证书

```bash
# 在 packages/server 目录下运行
./generate-certs.sh
```

这将在 `certs/` 目录下生成：
- `key.pem` - 私钥文件
- `cert.pem` - 证书文件

### 2. 启动服务器

```bash
npm start
# 或
bun run start
```

服务器将在 `https://localhost:3000` 启动。

## 生产环境

### 使用真实证书

1. 从证书颁发机构（如 Let's Encrypt）获取证书
2. 将证书文件放置在 `certs/` 目录：
   - `key.pem` - 私钥文件
   - `cert.pem` - 证书文件（包含完整证书链）

### 证书文件格式

- **私钥文件** (`key.pem`)：PEM 格式的私钥
- **证书文件** (`cert.pem`)：PEM 格式的证书，可以包含证书链

## 浏览器警告

使用自签名证书时，浏览器会显示安全警告。这在开发环境中是正常的：

1. 点击「高级」或「Advanced」
2. 点击「继续访问」或「Proceed to localhost (unsafe)"

## 故障排除

### 证书文件不存在

如果看到以下错误：
```
Error: ENOENT: no such file or directory, open 'certs/key.pem'
```

请确保：
1. 运行了 `./generate-certs.sh` 脚本
2. `certs/` 目录存在且包含证书文件

### 端口被占用

如果端口 3000 被占用，可以通过环境变量更改：

```bash
PORT=8443 npm start
```

## 配置说明

服务器配置位于 `src/index.ts`：

```typescript
tls: {
  key: fs.readFileSync(path.join(process.cwd(), 'certs', 'key.pem')),
  cert: fs.readFileSync(path.join(process.cwd(), 'certs', 'cert.pem')),
}
```

如需自定义证书路径，请修改此配置。