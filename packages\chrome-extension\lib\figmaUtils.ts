export const figmaLineHeightToCssString = (
    lineHeight: LineHeight,
    fontSize: number,
    fontFamily: string,
  ) => {
    switch (lineHeight.unit) {
      case 'AUTO':
        // if (uiFramework === UiFramework.rn) {
        //   return `${fontSize * 1.2}px`
        // }
        if (fontFamily.includes('PingFang SC')) {
          return `${(fontSize * 1.4).toFixed(1)}px`
        }
        return 'normal'
      case 'PERCENT':
        // 没有字体的时候，lineHeight是0
        return `${lineHeight.value === 0 ? 130 : getFloat(lineHeight.value)}%`
      case 'PIXELS':
        return `${lineHeight.value}px`
    }
  }