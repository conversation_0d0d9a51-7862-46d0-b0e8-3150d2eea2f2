import type {Option} from '@baidu/f2c-plugin-base/dist/types'
import {UiFramework} from '@baidu/f2c-plugin-base/dist/types/constants'
import type {JsonAdapter} from '../jsonAdapter'
import {getGeneratorConfig} from '../store/coreStore'
import {nameStore} from '../store/nameStore'
import {type Attributes, NodeType} from '../type'
import {isEmpty} from '../utils/utils'
import {Direction, getDirection, getOppositeDirection, reorderNodesBasedOnDirection} from './direction'
import {getContainerLineFromNodes, getLineBasedOnDirection, getLinesFromNodes} from './line'
import {absolutePositioningAnnotation} from './overlap'
import {RelativePoisition} from './type'
import {selectBox, shouldUseAsBackgroundImage} from './util'

enum JustifyContent {
  FLEX_START = 'flex-start',
  FLEX_END = 'flex-end',
  CENTER = 'center',
  SPACE_BETWEEN = 'space-between',
}

enum AlignItems {
  FLEX_START = 'flex-start',
  FLEX_END = 'flex-end',
  CENTER = 'center',
  SPACE_BETWEEN = 'space-between',
}

export const addAdditionalCssAttributesToNodes = (
  node: JsonAdapter,
  parentNode: null | JsonAdapter,
  option: Option,
) => {
  if (isEmpty(node)) {
    return
  }

  addAdditionalCssAttributes(node)
  // addThemeVariable(node)

  const children = node.getChildren()
  if (isEmpty(children)) {
    return
  }

  const direction = option.absoluteLayout ? Direction.ABSOLUTE : getDirection(node)

  reorderNodesBasedOnDirection(node, direction)
  node.addPositionalCssAttributes(getAdditionalParentLayoutCss(node, direction, parentNode))

  for (const child of children) {
    addAdditionalCssAttributesToNodes(child, node, option)
  }
}
// 这个函数计算父节点相对于其子节点内容边界的“内边距”
export const getPaddingInPixels = (
  node: JsonAdapter,
  direction: Direction,
  justifyContentValue: JustifyContent,
  alignItemsValue: AlignItems,
): number[] => {
  let paddingTop = 0
  let paddingBot = 0
  let paddingLeft = 0
  let paddingRight = 0

  const targetLine = getContainerLineFromNodes(node.getChildren(), direction)
  const parentLine = getContainerLineFromNodes([node], direction)

  const perpendicularTargetLine = getContainerLineFromNodes(node.getChildren(), getOppositeDirection(direction))

  const perpendicularParentLine = getContainerLineFromNodes([node], getOppositeDirection(direction))

  if (direction === Direction.VERTICAL) {
    const leftGap: number = targetLine.lower - parentLine.lower
    const rightGap: number = parentLine.upper - targetLine.upper

    const topGap: number = perpendicularTargetLine.lower - perpendicularParentLine.lower
    const botGap: number = perpendicularParentLine.upper - perpendicularTargetLine.upper

    switch (justifyContentValue) {
      case JustifyContent.SPACE_BETWEEN:
        paddingLeft = leftGap
        paddingRight = rightGap
        break
      case JustifyContent.FLEX_START:
        paddingLeft = leftGap
        break
      case JustifyContent.FLEX_END:
        paddingRight = rightGap
        break
    }

    switch (alignItemsValue) {
      case AlignItems.FLEX_START:
        paddingTop = topGap
        break
      case AlignItems.FLEX_END:
        paddingBot = botGap
        break
    }

    return [paddingTop, paddingRight, paddingBot, paddingLeft]
  }

  const topGap: number = targetLine.lower - parentLine.lower
  const botGap: number = parentLine.upper - targetLine.upper

  const leftGap: number = perpendicularTargetLine.lower - perpendicularParentLine.lower
  const rightGap: number = perpendicularParentLine.upper - perpendicularTargetLine.upper

  switch (justifyContentValue) {
    case JustifyContent.SPACE_BETWEEN:
      paddingTop = topGap
      paddingBot = botGap
      break
    case JustifyContent.FLEX_START:
      paddingTop = topGap
      break
    case JustifyContent.FLEX_END:
      paddingBot = botGap
      break
  }

  switch (alignItemsValue) {
    case AlignItems.FLEX_START:
      paddingLeft = leftGap
      break
    case AlignItems.FLEX_END:
      paddingRight = rightGap
      break
  }

  return [paddingTop, paddingRight, paddingBot, paddingLeft]
}

const setMarginsForChildren = (
  parentNode: JsonAdapter,
  direction: Direction,
  justifyContentValue: JustifyContent,
  alignItemsValue: AlignItems,
  paddings: number[],
) => {
  const children = parentNode.getChildren()
  // console.log('【setMarginsForChildren】parentNode: ', parentNode)
  // console.log('【setMarginsForChildren】direction: ', direction)
  // console.log('【setMarginsForChildren】justifyContentValue: ', justifyContentValue)
  // console.log('【setMarginsForChildren】alignItemsValue: ', alignItemsValue)
  // console.log('【setMarginsForChildren】paddings: ', paddings)
  const [paddingTop, paddingRight, paddingBot, paddingLeft] = paddings

  for (let i = 0; i < children.length; i++) {
    const targetNode = children[i]
    // console.log('targetNode', targetNode)

    let marginTop = 0
    let marginBot = 0
    let marginLeft = 0
    let marginRight = 0

    let topGap = 0
    let botGap = 0
    let leftGap = 0
    let rightGap = 0

    const targetLine = getLineBasedOnDirection(targetNode, direction)
    // console.log('【setMarginsForChildren】targetNode: ', targetNode, 'targetLine', targetLine)
    const parentLine = getLineBasedOnDirection(parentNode, direction)
    // console.log('【setMarginsForChildren】parentNode: ', parentNode, 'parentLine', parentLine)
    const perpendicularTargetLine = getLineBasedOnDirection(targetNode, getOppositeDirection(direction))
    const perpendicularParentLine = getLineBasedOnDirection(parentNode, getOppositeDirection(direction))

    let prevTarget = children[i]
    if (i > 0) {
      prevTarget = children[i - 1]
    }
    const prevTargetLine = getLineBasedOnDirection(prevTarget, direction)

    let nextTarget = children[i]
    if (i < children.length - 1) {
      nextTarget = children[i + 1]
    }
    const nextTargetLine = getLineBasedOnDirection(nextTarget, direction)

    if (direction === Direction.HORIZONTAL) {
      botGap =
        i === children.length - 1
          ? parentLine.upper - targetLine.upper - paddingBot
          : nextTargetLine.lower - targetLine.upper
      topGap = i === 0 ? targetLine.lower - parentLine.lower - paddingTop : targetLine.lower - prevTargetLine.upper

      switch (justifyContentValue) {
        case JustifyContent.SPACE_BETWEEN:
          if (i === 0 && i === children.length - 1) {
            marginTop = topGap
            marginBot = botGap
            break
          }

          if (i === 0) {
            marginTop = topGap
            break
          }

          if (i === children.length - 1) {
            marginBot = botGap
          }
          break
        case JustifyContent.FLEX_START:
          marginTop = topGap
          break
        case JustifyContent.FLEX_END:
          marginBot = botGap
          break
      }

      leftGap = perpendicularTargetLine.lower - perpendicularParentLine.lower - paddingLeft
      rightGap = perpendicularParentLine.upper - perpendicularTargetLine.upper - paddingRight

      switch (alignItemsValue) {
        case AlignItems.FLEX_START:
          marginLeft = leftGap
          break
        case AlignItems.FLEX_END:
          marginRight = rightGap
          break
      }

      targetNode.addPositionalCssAttributes({
        ...(marginTop > 0 && {'margin-top': `${marginTop}px`}),
        ...(marginLeft > 0 && {'margin-left': `${marginLeft}px`}),
        ...(marginBot > 0 && {'margin-bottom': `${marginBot}px`}),
        ...(marginRight > 0 && {'margin-right': `${marginRight}px`}),
      })
      continue
    }

    rightGap =
      i === children.length - 1
        ? parentLine.upper - targetLine.upper - paddingRight
        : nextTargetLine.lower - targetLine.upper
    leftGap = i === 0 ? targetLine.lower - parentLine.lower - paddingLeft : targetLine.lower - prevTargetLine.upper

    switch (justifyContentValue) {
      case JustifyContent.SPACE_BETWEEN:
        if (i === 0 && i === children.length - 1) {
          marginLeft = leftGap
          marginRight = rightGap
          break
        }

        if (i === 0) {
          marginLeft = leftGap
          break
        }

        if (i === children.length - 1) {
          marginRight = rightGap
          break
        }
        break
      case JustifyContent.FLEX_START:
        marginLeft = leftGap
        break
      case JustifyContent.FLEX_END:
        marginRight = rightGap
        break
    }

    topGap = perpendicularTargetLine.lower - perpendicularParentLine.lower - paddingTop
    botGap = perpendicularParentLine.upper - perpendicularTargetLine.upper - paddingBot

    switch (alignItemsValue) {
      case AlignItems.FLEX_START:
        marginTop = topGap
        break
      case AlignItems.FLEX_END:
        marginBot = botGap
        break
    }

    targetNode.addPositionalCssAttributes({
      ...(marginTop > 0 && {'margin-top': `${marginTop}px`}),
      ...(marginLeft > 0 && {'margin-left': `${marginLeft}px`}),
      ...(marginBot > 0 && {'margin-bottom': `${marginBot}px`}),
      ...(marginRight > 0 && {'margin-right': `${marginRight}px`}),
    })
  }
}

// 进队类型image处理样式
export const addAdditionalCssAttributes = (node: JsonAdapter) => {
  if (node.getType() === NodeType.IMAGE) {
    const id: string = node.getId()
    const imageComponentName: string = nameStore.getImageName(id)

    const extension = 'png'

    if (shouldUseAsBackgroundImage(node)) {
      // console.log(`add background image:`, imageComponentName, extension)
      node.addCssAttributes({
        'background-image': `url('./assets/${imageComponentName}.${extension}')`,
        // 'background-image': `url(${node.node.imageUrl})`,
        'background-size': '100% 100%',
        'background-repeat': 'no-repeat',
      })
    }
    node.addHtmlAttributes({
      src:
        getGeneratorConfig().uiFramework === UiFramework.react
          ? `require("./assets/${imageComponentName}.${extension}")`
          : `./assets/${imageComponentName}.${extension}`,
    })
    // node.addHtmlAttributes({src: node.node.imageUrl})
  }
}

const cssValueToNumber = (cssValue: string): number => {
  if (cssValue.endsWith('px')) {
    return Number.parseInt(cssValue.slice(0, -2), 10)
  }

  return Number.NEGATIVE_INFINITY
}

// 根据子元素获取该元素的positionalCssAttributes，绝对定位、flex布局
export const getAdditionalParentLayoutCss = (
  node: JsonAdapter,
  direction: Direction,
  parentNode: null | JsonAdapter,
): Attributes => {
  const attributes: Attributes = {}
  const positionalCssAttributes = node.getPositionalCssAttributes()
  const isAutoLayout = node.getAnnotation('AUTOLAYOUT')
  // 已经有autolayout了
  if (isAutoLayout !== undefined && isAutoLayout) {
    let autolayoutHasAbsolute = false
    const children = node.getChildren()
    children.forEach(child => {
      // @ts-ignore
      if (child.node && child.node.layoutPositioning === 'ABSOLUTE') {
        autolayoutHasAbsolute = true
      }
    })
    if (autolayoutHasAbsolute) {
      attributes['position'] = 'relative'

      const currentBox = selectBox(node)
      for (const child of node.getChildren()) {
        // @ts-ignore
        if (child.node.layoutPositioning === 'AUTO') {
          continue
        }
        const childAttributes: Attributes = {}
        const targetBox = selectBox(child)
        const vertical = Math.abs(currentBox.leftTop.y - targetBox.leftTop.y)
        const horizontal = Math.abs(currentBox.leftTop.x - targetBox.leftTop.x)

        childAttributes['position'] = 'absolute'
        if (currentBox.leftTop.y < targetBox.leftTop.y && currentBox.leftTop.y > targetBox.rightBot.y) {
          childAttributes['top'] = `-${vertical}px`
        } else {
          childAttributes['top'] = `${vertical}px`
        }

        if (currentBox.leftTop.x < targetBox.rightTop.x && currentBox.leftTop.x > targetBox.leftTop.x) {
          childAttributes['left'] = `-${horizontal}px`
        } else {
          childAttributes['left'] = `${horizontal}px`
        }

        child.addPositionalCssAttributes(childAttributes)
      }
    }

    // 由于gap属性的兼容性问题，这里用margin代替
    const gap = positionalCssAttributes['gap']
    // 只有一个子元素也没必要对gap进行处理，后续removeBgParent会过滤掉
    if (gap !== undefined && node.getChildren().length > 1) {
      node.getChildren().forEach((child, idx) => {
        const childAttributes: Attributes = {}
        if (positionalCssAttributes['flex-direction'] === 'column') {
          if (
            Math.abs(node.getComputedCoordinates().rightBot.y - child.getComputedCoordinates().rightBot.y) <
            cssValueToNumber(gap)
          ) {
            // 为了配合list标签，使用伪类
            // node.addCssAttributes({
            //   [`&>div:nth-child(${idx + 1}) {margin-bottom`]: `0px}`,
            // })
          } else {
            childAttributes['margin-bottom'] = gap
          }
          if (idx + 1 === node.getChildren().length) {
            childAttributes['margin-bottom'] = '0px'
          }
        } else {
          if (
            Math.abs(node.getComputedCoordinates().rightBot.x - child.getComputedCoordinates().rightBot.x) <
            cssValueToNumber(gap)
          ) {
            // 为了配合list标签，使用伪类
            // node.addCssAttributes({
            //   [`&>div:nth-child(${idx + 1}) {margin-right`]: `0px}`,
            // })
          } else {
            childAttributes['margin-right'] = gap
          }
          if (idx + 1 === node.getChildren().length) {
            childAttributes['margin-right'] = '0px'
          }
        }
        child.addPositionalCssAttributes(childAttributes)
      })
    }
    delete positionalCssAttributes['gap']
    node.removePositionalCssAttributes('gap')

    return {
      ...positionalCssAttributes,
      ...attributes,
    }
  }

  if (isEmpty(node.getChildren())) {
    return positionalCssAttributes
  }

  if (node.hasAnnotation(absolutePositioningAnnotation) || direction === Direction.ABSOLUTE) {
    if (parentNode && parentNode.hasAnnotation(absolutePositioningAnnotation)) {
      attributes['position'] = 'absolute'
    } else {
      attributes['position'] = 'relative'
    }

    const currentBox = selectBox(node)
    for (const child of node.getChildren()) {
      const childAttributes: Attributes = {}
      const targetBox = selectBox(child)
      const vertical = Math.abs(currentBox.leftTop.y - targetBox.leftTop.y)
      const horizontal = Math.abs(currentBox.leftTop.x - targetBox.leftTop.x)
      // const right = Math.abs(currentBox.rightBot.x - targetBox.rightBot.x)
      // console.log('currentBox', currentBox, 'targetBox', targetBox)

      childAttributes['position'] = 'absolute'
      if (currentBox.leftTop.y < targetBox.leftTop.y && currentBox.leftTop.y > targetBox.rightBot.y) {
        childAttributes['top'] = `-${vertical}px`
      } else {
        childAttributes['top'] = `${vertical}px`
      }

      if (currentBox.leftTop.x < targetBox.rightTop.x && currentBox.leftTop.x > targetBox.leftTop.x) {
        childAttributes['left'] = `-${horizontal}px`
      } else {
        childAttributes['left'] = `${horizontal}px`
      }

      child.addPositionalCssAttributes(childAttributes)
    }

    return {
      ...positionalCssAttributes,
      ...attributes,
    }
  }

  attributes['display'] = 'flex'
  if (direction === Direction.HORIZONTAL) {
    attributes['flex-direction'] = 'column'
  }

  const justifyContentValue = getJustifyContentValue(node, direction)
  const alignItemsValue = getAlignItemsValue(node, getOppositeDirection(direction))

  attributes['justify-content'] = justifyContentValue
  attributes['align-items'] = alignItemsValue
  for (let i = 0; i < node.getChildren().length; i++) {
    const targetNode = node.getChildren()[i]
    if (targetNode.getType() === NodeType.TEXT) {
      targetNode.addCssAttributes({'flex-shrink': '0'})
    }
  }

  const paddings = [0, 0, 0, 0]

  setMarginsForChildren(node, direction, justifyContentValue, alignItemsValue, paddings)

  return {
    ...positionalCssAttributes,
    ...attributes,
  }
}

const getJustifyContentValue = (parentNode: JsonAdapter, direction: Direction): JustifyContent => {
  // console.log('getJustifyContentValue', parentNode, direction)
  const children = parentNode.getChildren()
  const targetLines = getLinesFromNodes(children, direction)
  const [parentLine] = getLinesFromNodes([parentNode], direction)

  if (targetLines.length === 1) {
    const targetLine = targetLines[0]
    const mid = parentLine.getMid()

    const touchingStart: boolean = parentLine.lower + 2 >= targetLine.lower && targetLine.lower >= parentLine.lower - 2
    const touchingEnd: boolean = parentLine.upper + 2 >= targetLine.upper && targetLine.upper >= parentLine.upper - 2

    if (touchingStart && touchingEnd) {
      return JustifyContent.CENTER
    }

    if (touchingStart) {
      return JustifyContent.FLEX_START
    }

    if (touchingEnd) {
      return JustifyContent.FLEX_END
    }

    switch (targetLine.getRelativeLinePosition(mid)) {
      case RelativePoisition.LEFT:
        return JustifyContent.FLEX_START
      case RelativePoisition.RIGHT:
        return JustifyContent.FLEX_END
      case RelativePoisition.MID:
        return JustifyContent.CENTER
    }
  }

  if (targetLines.length === 2) {
    const firstTargetLine = targetLines[0]
    const lastTargetLine = targetLines[1]
    // const mid = parentLine.getMid()

    if (firstTargetLine.lower === parentLine.lower && lastTargetLine.upper === parentLine.upper) {
      return JustifyContent.SPACE_BETWEEN
    }

    // todo: 居中对齐的margin需要重新适配
    /* if (mid === (lastTargetLine.upper + firstTargetLine.lower) / 2) {
      return JustifyContent.CENTER
    } */

    return JustifyContent.FLEX_START
  }

  if (targetLines.length > 2) {
    /* const gaps: number[] = []
    let prevLine: Line = null
    for (let i = 0; i < targetLines.length; i++) {
      const targetLine: Line = targetLines[i]
      if (i === 0) {
        prevLine = targetLine
        continue
      }

      gaps.push(targetLine.lower - prevLine.upper)
      prevLine = targetLine
    }

    const averageGap: number = gaps.reduce((a, b) => a + b) / gaps.length
    let isJustifyCenter = true
    for (let i = 0; i < targetLines.length; i++) {
      const targetLine: Line = targetLines[i]
      if (i === 0) {
        prevLine = targetLine
        continue
      }

      const gap: number = targetLine.lower - prevLine.upper

      if (Math.abs(gap - averageGap) / averageGap > 0.1) {
        isJustifyCenter = false
      }
      prevLine = targetLine
    }
    console.log('isJustifyCenter', isJustifyCenter)
    if (isJustifyCenter) {
      return JustifyContent.SPACE_BETWEEN
    } */
    // todos: 算法优化
  }

  return JustifyContent.FLEX_START
}

const getAlignItemsValue = (parentNode: JsonAdapter, direction: Direction): AlignItems => {
  const children = parentNode.getChildren()
  const targetLines = getLinesFromNodes(children, direction)
  const [parentLine] = getLinesFromNodes([parentNode], direction)
  const mid = parentLine.getMid()

  if (targetLines.length === 1) {
    const targetLine = targetLines[0]
    switch (targetLine.getRelativeLinePosition(mid)) {
      case RelativePoisition.LEFT:
        return AlignItems.FLEX_START
      case RelativePoisition.RIGHT:
        return AlignItems.FLEX_END
      case RelativePoisition.MID:
        return AlignItems.CENTER
    }
  }

  let numberOfItemsTippingLeft = 0
  let numberOfItemsTippingRight = 0
  let numberOfItemsInTheMiddle = 0

  for (const targetLine of targetLines) {
    switch (targetLine.getRelativeLinePosition(mid)) {
      case RelativePoisition.LEFT:
        numberOfItemsTippingLeft++
        break
      case RelativePoisition.RIGHT:
        numberOfItemsTippingRight++
        break
      case RelativePoisition.MID:
        numberOfItemsInTheMiddle++
        break
    }
  }

  /* if (numberOfItemsInTheMiddle === children.length) {
    return AlignItems.CENTER
  } else if (numberOfItemsTippingLeft > numberOfItemsTippingRight) {
    return AlignItems.FLEX_START
  } else {
    return AlignItems.FLEX_END
  } */
  if (numberOfItemsInTheMiddle === children.length) {
    return AlignItems.CENTER
  } else {
    return AlignItems.FLEX_START
  }
}
