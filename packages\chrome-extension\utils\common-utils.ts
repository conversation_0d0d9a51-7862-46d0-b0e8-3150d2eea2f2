import JSZip from "jszip";

export const withTimeout = (
  onSuccess: {
    (response: { status: string; data: Record<string, string>; error?: string | undefined }): void
    // (response: { status: string; data: Record<string, string>; error?: string | undefined }): void
    // (response: { error: any }): void
    // (response: { error: any }): void
    // (response: any): void
    apply?: any
  },
  onTimeout: { (): void; (): void; (): void; (): void; (): void; (): void },
  timeout: number,
) => {
  let called = false

  const timer = setTimeout(() => {
    if (called) {
      return
    }
    called = true
    onTimeout()
  }, timeout)

  return (...args: any) => {
    if (called) {
      return
    }
    called = true
    clearTimeout(timer)
    onSuccess.apply(this, args)
  }
}

export const getObjType = (obj: any) => {
  return Object.prototype.toString.call(obj).slice(8, -1)
}

export function getImgFormat(format?: 'PNG' | 'SVG' | 'JPG') {
  switch (format?.toLocaleUpperCase()) {
    case 'JPG':
      return 'jpeg'
    case 'PNG':
      return 'png'
    case 'SVG':
      return 'svg+xml'
    default:
      return 'png'
  }
}

/**
 * 下载文件到本地
 * @param data 要下载的数据，可以是字符串、Blob、ArrayBuffer等
 * @param options 下载选项
 * @param options.filename 文件名（包含扩展名）
 * @param options.mimeType 文件MIME类型，默认根据文件名推断
 * @param options.charset 文本编码，默认为'utf-8'
 * @returns Promise<void>
 */
export function downloadFile(
  data: string | Blob | ArrayBuffer | Uint8Array,
  options: {
    filename: string
    mimeType?: string
    charset?: string
  }
): Promise<void | Error> {
  return new Promise((resolve, reject) => {
    try {
      // 处理不同类型的数据
      let blob: Blob
      if (typeof data === 'string') {
        const mimeType = options.mimeType || getMimeTypeFromFilename(options.filename)
        blob = new Blob([data], {
          type: `${mimeType};charset=${options.charset || 'utf-8'}`,
        })
      } else if (data instanceof Blob) {
        blob = data
      } else if (data instanceof ArrayBuffer || data instanceof Uint8Array) {
        const mimeType = options.mimeType || 'application/octet-stream'
        blob = new Blob([data], { type: mimeType })
      } else {
        throw new Error('Unsupported data type for download')
      }

      // 创建下载链接
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = options.filename
      a.style.display = 'none'

      // 添加点击事件
      document.body.appendChild(a)
      a.click()

      // 清理
      setTimeout(() => {
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
        resolve()
      }, 100)
    } catch (error) {
      reject(error)
    }
  })
}

/**
 * 根据文件名推断MIME类型
 */
function getMimeTypeFromFilename(filename: string): string {
  const extension = filename.split('.').pop()?.toLowerCase()
  switch (extension) {
    case 'txt':
      return 'text/plain'
    case 'html':
    case 'htm':
      return 'text/html'
    case 'css':
      return 'text/css'
    case 'js':
      return 'text/javascript'
    case 'json':
      return 'application/json'
    case 'png':
      return 'image/png'
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg'
    case 'gif':
      return 'image/gif'
    case 'svg':
      return 'image/svg+xml'
    case 'pdf':
      return 'application/pdf'
    case 'zip':
      return 'application/zip'
    default:
      return 'application/octet-stream'
  }
}


export const compressImg = async (
  buf: Uint8Array,
  figmaIdentityInfo: string,
  figmaIdentitySign: string,
) => {
  console.log('[plugin]压缩图片 compressImg')
  const formData = new FormData()
  // Create a Blob object from the Uint8Array, which is more compatible in various environments
  const blob = new Blob([buf], { type: 'image/png' })

  formData.append('file', blob, 'origin.png')

  try {
    const response = await fetch('https://compression-test.yy.com/api/image/compress', {
      method: 'POST',
      headers: {
        'figma-identity-info': figmaIdentityInfo,
        'figma-identity-sign': figmaIdentitySign,
      },
      body: formData,
    })

    if (response.ok) {
      const compressedData = await response.arrayBuffer()
      const compressBuf = new Uint8Array(compressedData)
      return compressBuf
    } else {
      // It's good practice to handle non-ok responses
      console.error('Image compression failed with status:', response.status);
      const errorText = await response.text();
      console.error('Error details:', errorText);
      return buf
    }
  } catch (error) {
    console.error('Image compression request failed:', error);
    return buf
  }
}


/**
 * @description 批量导出base64图片字符串数组到本地
 */
export async function exportBase64Image(
  base64Images: { buffer: Uint8Array; name: string; format: string; originFormat: string }[],
) {
  // Base64图片字符串数组格式：data:image/png;base64,base64String
  try {
    // 处理文件名重复
    const nameCounts: Record<string, number> = {};
    const processedImages = base64Images.map(image => {
      let newName = image.name;
      if (nameCounts[image.name]) {
        newName = `${image.name}_${nameCounts[image.name]}`;
        nameCounts[image.name]++;
      } else {
        nameCounts[image.name] = 1;
      }
      return { ...image, name: newName };
    });

    // 将每个Base64字符串转换为Blob对象
    const blobs = processedImages.map(base64Data => {
      try {
        return {
          name: base64Data.name,
          // blob: base64ToBlob(base64Data.base64, `image/${base64Data.format || 'png'}`),
          blob: new Blob([base64Data.buffer], { type: `image/${base64Data.format || 'png'}` }),
          originFormat: base64Data.originFormat || 'png',
        };
      } catch (error) {
        console.error(`Failed to convert base64 to blob for ${base64Data.name}:`, error);
        throw new Error(`Failed to process image "${base64Data.name}": ${error instanceof Error ? error.message : String(error)}`);
      }
    });
    if (blobs.length == 1) {
      const filename = blobs[0].name + `.${blobs[0].originFormat}`
      downloadFile(blobs[0].blob, { filename })
      return
    }
    const zip = new JSZip()
    // 将每个Blob对象添加到ZIP文件中，并使用唯一的文件名
    blobs.forEach(function (blob) {
      const fileName = blob.name + `.${blob.originFormat}`
      zip.file(fileName, blob.blob)
    })
    // 生成ZIP文件并触发下载
    const zipBlob = await zip.generateAsync({ type: 'blob' });
    await downloadFile(zipBlob, { filename: `figma-images.zip` });
  } catch (error) {
    console.error('Error in exportBase64Image:', error);
    throw error;
  }
}

/**
 * @description 将base64字符串转换为二进制
 */
export function base64ToBlob(base64Data: string, contentType: string) {
  contentType = contentType || ''
  const sliceSize = 1024
  const byteCharacters = atob(base64Data.split(',')[1] || base64Data)
  const bytesLength = byteCharacters.length
  const slicesCount = Math.ceil(bytesLength / sliceSize)
  const byteArrays = new Array(slicesCount)

  for (let sliceIndex = 0; sliceIndex < slicesCount; ++sliceIndex) {
    const begin = sliceIndex * sliceSize
    const end = Math.min(begin + sliceSize, bytesLength)

    const bytes = new Array(end - begin)
    for (let offset = begin, i = 0; offset < end; ++i, ++offset) {
      bytes[i] = byteCharacters[offset].charCodeAt(0)
    }

    byteArrays[sliceIndex] = new Uint8Array(bytes)
  }

  return new Blob(byteArrays, { type: contentType })
}


export async function compressImgWithTinyPNG(
  buf: Uint8Array,
  apiKey: string, // TinyPNG API密钥
) {
  console.log('[plugin]压缩图片 compressImg using TinyPNG')
  // 创建Blob对象
  const blob = new Blob([buf], { type: 'image/png' })

  try {
    // 第一步：上传图片到TinyPNG
    const response = await fetch('https://api.tinify.com/shrink', {
      method: 'POST',
      headers: {
        'Authorization': 'Basic ' + btoa('api:' + apiKey),
        'Content-Type': 'image/png'
      },
      body: blob
    })

    if (!response.ok) {
      console.error('[compressImgWithTinyPNG] TinyPNG compression failed with status:', response.status)
      throw new Error(`接口请求失败: ${response.status} ${response.statusText}`)
    }

    // 从响应头中获取压缩后的图片URL
    const compressedUrl = response.headers.get('Location')
    if (!compressedUrl) {
      console.error('[compressImgWithTinyPNG] No Location header found in TinyPNG response')
      throw new Error(`接口请求失败: ${response.status} ${response.statusText}`)
    }

    // 第二步：下载压缩后的图片
    const downloadResponse = await fetch(compressedUrl)
    if (!downloadResponse.ok) {
      throw new Error(`接口请求失败: ${downloadResponse.status} ${downloadResponse.statusText}`)
    }

    const compressedData = await downloadResponse.arrayBuffer()
    return new Uint8Array(compressedData)
  }
  catch (error: any) {
    throw new Error(`Image compression request failed: ${error.message}`)
  }
}
