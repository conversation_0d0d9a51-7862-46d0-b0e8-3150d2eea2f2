
export const getNums = (num: number | string) => {
    return parseInt(Number(num).toFixed(3))
}
export const getFloat = (num?: number | string, position = 2) => {
    const res = parseFloat(String(Number(num).toFixed(position)))
    return isNaN(res) ? (num as number) : (res as unknown as number)
}
export const getInt = (num?: number | string) => {
    const res = parseInt(String(Number(num).toFixed(0)))
    return isNaN(res)? (num as number) : (res as unknown as number)
}
export function fixNum(number: number, fractionDigits = 2) {
    if (number % 1 !== 0) {
      return number.toFixed(fractionDigits)
    } else {
      return Number(number).toString()
    }
  }