import {CSSProperties, JSX, ReactNode, useCallback} from 'react'
import dimensionStore, { LayerInfo } from '@/store/dimensionStore'
import UseColorSelect from './useColorSelect'
import {figmaLineHeightToCssString} from '@/lib/figmaUtils'
import {TransformUnit} from '@/lib/dimension'
import CopyItem from '@/components/Dimension/copyItem'
import Fills from './fills'
import DivideLine from '../divideLine'
interface ITextItem {
  title: string | number | boolean | JSX.Element | Iterable<ReactNode>
  value: string
  desc?: string | number | boolean | JSX.Element | Iterable<ReactNode>
  unit?: string
}
const Text = (props: {
  layerInfo: LayerInfo
}) => {
  const {ColorSelect, colorFormat, color} = UseColorSelect()
  const { currentSelectUnit, currentMagnification, currentRemBase, currentVwBase } = dimensionStore.state

  
  const textFills = props.layerInfo.css?.fontFills

  function getItem(
    title: ITextItem['title'],
    value: ITextItem['value'],
    desc?: ITextItem['desc'],
    unit?: ITextItem['unit'],
  ) {
    return <CopyItem title={title} value={value || 0} unit={unit ? currentSelectUnit : false} key={title + value} />
  }
  const changeUnit = useCallback(
    (value: string) => {
      return TransformUnit(
        value,
        'px',
        currentSelectUnit,
        (function () {
          switch (currentSelectUnit) {
            case 'rem':
              return currentMagnification / currentRemBase
            case 'px':
            case 'dp':
              return currentMagnification
            case 'vw':
              return (100 / currentVwBase) * currentMagnification
            default:
              return currentMagnification
          }
        })(),
      )
    },
    [currentSelectUnit, currentMagnification, currentRemBase, currentVwBase],
  )
  return (
    <div className="extContent w-full text-[12px]">
      {ColorSelect}
      {textFills?.map((textInfo: {
        lineHeight: { unit: string; value: number };
        fontSize: number;
        fontName: { family: string };
        characters: string;
        fontWeight: number;
        fills: any[];
      }, index:number) => {
        const lineHeight = figmaLineHeightToCssString(textInfo.lineHeight as any, textInfo.fontSize, textInfo.fontName.family)

        return (
          <div key={index} className={`w-full h-auto ${textFills.length > 1 ? 'mt-4' : ''}`}>
            {getItem(`文本 ${textFills.length > 1 ? index + 1 : ''}`, textInfo.characters)}
            {getItem('字体', textInfo.fontName.family)}
            {getItem('字号', String(changeUnit(String(textInfo.fontSize + 'px'))))}
            {getItem('字重', String(textInfo.fontWeight))}
            {getItem('行高', String(textInfo.lineHeight.unit == 'PIXELS' ? changeUnit(lineHeight) : lineHeight))}
            <Fills fills={textInfo.fills} color={color} colorFormat={colorFormat} />
            {textFills.length > 1 && <DivideLine />}
          </div>
        )
      })}
      {props.layerInfo.css?.['text-align'] && getItem('段落对齐', props.layerInfo.css?.['text-align'])}
    </div>
  )
}
export default Text
