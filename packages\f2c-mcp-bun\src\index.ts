import {McpServer} from '@modelcontextprotocol/sdk/server/mcp.js'
import {StdioServerTransport} from '@modelcontextprotocol/sdk/server/stdio.js'
import axios from 'axios'
import {z} from 'zod'

// 从环境变量获取 personalToken
const DEFAULT_PERSONAL_TOKEN = process.env.personalToken || ''

// 增强的 Figma URL 解析器，支持更多格式
function parseFigmaUrl(url: string) {
  try {
    const urlObj = new URL(url)
    const path = urlObj.pathname

    // 支持 file/xxx 和 design/xxx 两种格式
    const [, fileKey] = path.match(/(?:file|design)\/([^/]+)/) || []

    // 支持 node-id 参数和 hash 路由格式
    const nodeIdMatch =
      urlObj.searchParams.get('node-id') || url.match(/node-id=([^&]+)/) || url.match(/#([^:]+:[^:]+)/)

    const nodeId = nodeIdMatch ? (Array.isArray(nodeIdMatch) ? nodeIdMatch[1] : nodeIdMatch) : ''

    if (!fileKey) {
      throw new Error('无效的 Figma 链接：未找到 fileKey')
    }

    return {
      fileKey,
      nodeId: nodeId || '',
    }
  } catch (error) {
    throw new Error('无效的 Figma 链接')
  }
}

// 创建 MCP 服务器
const server = new McpServer({
  name: 'F2C MCP Bun',
  version: '0.0.1',
})

// 注册 figma_to_html 工具
server.tool(
  'figma_to_html',
  '将 Figma 文件中的节点转换为 HTML 内容',
  {
    personalToken: z.string().default(DEFAULT_PERSONAL_TOKEN).describe('您的 Figma 个人访问令牌'),
    figmaUrl: z.string().describe('Figma 设计的 URL，包含 fileKey 和 nodeId'),
  },
  async ({personalToken = DEFAULT_PERSONAL_TOKEN, figmaUrl}) => {
    console.log('收到 figma_to_html 工具调用请求:')
    console.log(`- Figma URL: ${figmaUrl}`)
    // 修改前：console.log(`- Personal Token: ${personalToken.slice(0, 4)}...${personalToken.slice(-4)}`)
    console.log(`- Personal Token: ${personalToken}`) // 修改后：完整打印token

    try {
      const {fileKey, nodeId} = parseFigmaUrl(figmaUrl)

      if (!fileKey) {
        throw new Error('fileKey 不能为空')
      }

      const response = await axios.get('https://f2c-figma-api.yy.com/api/nodes', {
        params: {
          fileKey,
          nodeIds: nodeId,
          personal_token: personalToken,
          format: 'html',
        },
      })

      return {
        content: [{type: 'text', text: response.data}],
      }
    } catch (error: any) {
      console.error('处理请求失败:', error)
      return {
        content: [{type: 'text', text: `错误: ${error.message}`}],
      }
    }
  },
)

// 启动服务器并连接到 stdio 传输
async function startServer() {
  console.log('启动 Figma-to-HTML MCP 服务器 (Bun 版本)...')
  const transport = new StdioServerTransport()
  await server.connect(transport)
  console.log('MCP 服务器已连接到 stdio')
}

startServer().catch(error => {
  console.error('启动 MCP 服务器失败:', error)
  process.exit(1)
})
