import {browser} from 'wxt/browser'
import {createIntegratedUi} from 'wxt/client'
import {storage} from 'wxt/storage'

export default defineContentScript({
  matches: ['https://www.figma.com/*'],
  runAt: 'document_end',
  main(ctx) {
    // console.log('Content script loaded on:', window.location.href)

    // 设置Chrome更新监听器
    if (chrome.runtime && chrome.runtime.onUpdateAvailable) {
      chrome.runtime.onUpdateAvailable.addListener(details => {
        console.log('Chrome Web Store更新可用:', details)
        // 通知UI组件有更新可用
        window.postMessage(
          {
            type: 'FROM_EXTENSION',
            messageId: 'chrome-update-available',
            payload: {
              type: 'updateAvailable',
              version: details.version,
            },
          },
          '*',
        )
      })
    }

    // 设置页面到扩展的消息桥接
    // 监听 postMessage 消息
    window.addEventListener('message', async (event: MessageEvent) => {
      if (event.source !== window) return

      const {type, messageId, payload} = event.data || {}

      // 处理新的消息格式 (TO_EXTENSION)
      if (type === 'TO_EXTENSION') {
        try {
          let response
          console.log('payload', payload, chrome)
          // 根据消息类型处理不同的请求
          switch (payload.type) {
            case 'checkAuthStatus':
              const [figmaAccessTokenData, figmaAuthExpireTime] = await storage.getItems([
                'local:figmaAccessTokenData',
                'local:figmaAuthExpireTime',
              ])
              if (figmaAuthExpireTime.value && Date.now() >= figmaAuthExpireTime.value) {
                // 过期 返回未授权状态
                response = {
                  isAuthenticated: false,
                  token: null,
                  figmaAuthExpireTime: figmaAuthExpireTime.value,
                }
                break
              }
              response = {
                isAuthenticated: !!figmaAccessTokenData.value,
                token: figmaAccessTokenData.value || null,
              }
              break

            case 'removeAuth':
              await storage.removeItems([
                'local:figmaAccessTokenData',
                'local:figmaAuthExpireTime',
                'local:authStatus',
                'local:authError',
              ])
              response = true
              break

            case 'gotoAuth':
              // 发送消息给 background 脚本开始认证流程
              response = await browser.runtime.sendMessage({
                type: 'backgroundStartAuth',
              })
              console.log(response, 'gotoAuth_resp')

              break

            case 'storeLoginInfo':
              await storage.setItems([
                {
                  key: 'local:figmaIdentityInfo',
                  value: payload.data.figmaIdentityInfo,
                },
                {
                  key: 'local:figmaIdentitySign',
                  value: payload.data.figmaIdentitySign,
                },
              ])
              response = true
              break
            case 'removeLoginInfo':
              await storage.removeItems(['local:figmaIdentityInfo', 'local:figmaIdentitySign'])
              response = true
              break
            case 'getLoginInfo':
              const [figmaIdentityInfo, figmaIdentitySign] = await storage.getItems([
                'local:figmaIdentityInfo',
                'local:figmaIdentitySign',
              ])
              response = {
                figmaIdentityInfo: figmaIdentityInfo.value,
                figmaIdentitySign: figmaIdentitySign.value,
              }
              break
            case 'setLocalBuisSettings':
              await storage.setItems([
                {
                  key: 'local:selectedBusiness',
                  value: payload.data.selectedBusiness,
                },
                {
                  key: 'local:seletedBucket',
                  value: payload.data.seletedBucket,
                },
              ])
              response = true
              break
            case 'getLocalBuisSettings':
              const [selectedBusiness, seletedBucket] = await storage.getItems([
                'local:selectedBusiness',
                'local:seletedBucket',
              ])
              response = {
                selectedBusiness: selectedBusiness.value,
                seletedBucket: seletedBucket.value,
              }
              break

            // 版本管理相关消息
            case 'getCurrentVersion':
              response = {
                version: chrome.runtime.getManifest().version,
              }
              break

            case 'applyUpdate':
              // 不能在 content script 中使用 chrome.runtime.reload()
              // 改为通过 background script 来重载扩展
              try {
                response = await browser.runtime.sendMessage({
                  type: 'applyUpdate',
                })
              } catch (error) {
                response = {status: 'error', error: error instanceof Error ? error.message : 'Unknown error'}
              }
              break

            default:
              throw new Error(`Unknown message type: ${payload.type}`)
          }
          // 发送成功响应
          window.postMessage(
            {
              type: 'FROM_EXTENSION',
              messageId,
              payload: response,
            },
            '*',
          )
        } catch (error) {
          // 发送错误响应
          window.postMessage(
            {
              type: 'FROM_EXTENSION',
              messageId,
              error: error instanceof Error ? error.message : 'Unknown error',
            },
            '*',
          )
        }
      }
    })
    // console.log('ui.js loaded')
    const ui = createIntegratedUi(ctx, {
      tag: 'f2c',
      position: 'inline',
      onMount(root) {
        const script = document.createElement('script')
        script.src = browser.runtime.getURL('/ui.js')
        root.appendChild(script)
        script.onload = () => {
          script.remove()
        }
        const styleEl = document.createElement('link')
        styleEl.setAttribute('rel', 'stylesheet')
        styleEl.setAttribute('href', browser.runtime.getURL('assets/ui.css' as any))

        root.appendChild(styleEl)

        // Prevent Figma's event capture so that text selection works.
        // Both of the following are required.
        root.tabIndex = -1
        root.classList.add('js-fullscreen-prevent-event-capture')
      },
    })

    ui.mount()
  },
})
