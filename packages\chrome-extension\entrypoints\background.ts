import {browser} from 'wxt/browser'
import {defineBackground} from 'wxt/sandbox'
import {storage} from 'wxt/storage'

export default defineBackground(() => {
  console.log('Background script initialized - with runtime adapter support')
  browser.runtime.onMessage.addListener((message: any, sender, sendResponse) => {
    // 注意：移除了 async 关键字，改为同步处理消息
    if (message.type === 'accessTokenFromNewTab') {
      if (message.data) {
        try {
          storage.setItems([
            {
              key: 'local:figmaAccessTokenData',
              value: message.data.token,
            },
            {
              key: 'local:figmaAuthExpireTime',
              value: Date.now() + Number(message.data.expireTime.trim()) * 1000, // 过期剩余秒数
            },
            {
              key: 'local:authStatus',
              value: 'success',
            },
          ])
        } catch (error) {
          console.log(error, 'accessTokenFromNewTab')
        }
      }
      // 同步处理，不需要保持消息通道
      return
    } else if (message.type === 'getAuthStatus') {
      ;(async () => {
        const [figmaAccessTokenData, figmaAuthExpireTime] = await storage.getItems([
          'local:figmaAccessTokenData',
          'local:figmaAuthExpireTime',
        ])
        console.log(figmaAuthExpireTime, 'pfigmaAuthExpireTimefigmaAuthExpireTimefigmaAuthExpireTime')
        if (figmaAccessTokenData.key === 'local:figmaAccessTokenData' && figmaAccessTokenData.value) {
          sendResponse({
            status: 'success',
            token: figmaAccessTokenData.value,
          })
        } else {
          sendResponse({
            status: 'error',
            message: 'No token found',
          })
        }
      })()
      return true
    } else if (message.type === 'removeAuth') {
      ;(async () => {
        try {
          await storage.removeItems([
            'local:figmaAccessTokenData',
            'local:figmaAuthExpireTime',
            'local:authStatus',
            'local:authError',
          ])
          sendResponse({
            status: 'success',
            message: 'Auth removed successfully',
          })
        } catch (error) {
          console.error('Remove auth error:', error)
          sendResponse({
            status: 'error',
            message: 'Failed to remove auth',
          })
        }
      })()
      return true
    } else if (message.type === 'backgroundStartAuth') {
      let originalTabId: number | undefined
      let newTabId = 0
      try {
        // 先获取当前活动标签
        chrome.tabs.query({active: true, currentWindow: true}, async tabs => {
          originalTabId = tabs[0]?.id

          // 设置本地状态为进行中
          await storage.setItems([
            {
              key: 'local:authStatus',
              value: 'pending',
            },
          ])

          // 创建一个 Promise 来跟踪授权流程
          const authPromise = new Promise<any>((resolve, reject) => {
            // 打开之后监听storage的变化
            const unwatch = storage.watch<string>('local:figmaAccessTokenData', (newValue, oldValue) => {
              console.log('Token changed:', {newValue, oldValue})
              if (newValue) {
                // 取消监听
                unwatch()
                chrome.tabs.remove(newTabId)
                if (originalTabId) {
                  chrome.tabs.update(originalTabId, {active: true})
                }
                // 如果有新的 token 值，表示授权成功
                storage.getItem('local:figmaAuthExpireTime').then(expireTime => {
                  resolve({
                    status: 'success',
                    message: 'Auth success',
                    token: newValue,
                    expireTime: expireTime,
                  })
                })
              }
            })

            // 设置超时，避免无限等待
            setTimeout(() => {
              // 如果 30 秒后仍未获得 token，则取消监听并返回错误
              unwatch()
              reject({
                status: 'error',
                message: '授权超时，请重试',
              })
            }, 30000)

            // 打开授权页面
            chrome.tabs.create({url: 'https://f2c-figma-api.yy.com/auth'}, newTab => {
              chrome.tabs.onUpdated.addListener(function listener(tabId, changeInfo) {
                console.log('onUpdated.addListener', tabId, changeInfo)
                if (
                  tabId === newTab.id &&
                  changeInfo?.url &&
                  changeInfo?.url.includes('oauth/callback') &&
                  changeInfo?.url.includes('code=')
                ) {
                  console.log('授权完成，准备注入脚本')
                  newTabId = newTab.id
                  chrome.tabs.onUpdated.removeListener(listener)

                  // 注入内容脚本
                  chrome.scripting.executeScript({
                    target: {tabId: newTab.id},
                    files: ['tokenScript.js'],
                  })
                }
              })
            })
          })

          // 使用 Promise 的方式处理响应
          authPromise
            .then(result => {
              console.log('授权结果:', result)
              sendResponse(result)
            })
            .catch(error => {
              sendResponse({
                status: 'error',
                message: error.message || '授权失败',
              })
            })
        })
      } catch (error: any) {
        console.error('授权流程错误:', error)
        sendResponse({
          status: 'error',
          message: error.message || '授权失败',
        })
      }

      // 在 Chrome 扩展的消息传递系统中，如果我们需要异步处理消息（比如等待 Promise），
      // 就必须返回 true 来告诉 Chrome 我们会在稍后调用 sendResponse，
      // 否则消息通道会在函数返回后立即关闭
      return true
    } else if (message.type === 'applyUpdate') {
      // 处理扩展重载请求
      console.log('收到重载请求，开始重载扩展')
      try {
        chrome.runtime.reload()
        sendResponse({success: true})
      } catch (error) {
        sendResponse({
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
        })
      }
      return true
    }

    // 对于未知的消息类型，不返回值（表示同步处理）
    return
  })
})
