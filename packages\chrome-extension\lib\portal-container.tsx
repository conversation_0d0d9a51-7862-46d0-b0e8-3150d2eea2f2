'use client'

import type React from 'react'
import {createContext, useContext, useEffect, useRef, useState} from 'react'

interface PortalContainerContextType {
  container: HTMLElement | null
}

const PortalContainerContext = createContext<PortalContainerContextType>({
  container: null,
})

export const usePortalContainer = () => {
  const context = useContext(PortalContainerContext)
  return context.container
}

interface PortalContainerProviderProps {
  children: React.ReactNode
}

export const PortalContainerProvider: React.FC<PortalContainerProviderProps> = ({children}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [container, setContainer] = useState<HTMLElement | null>(null)

  useEffect(() => {
    setContainer(containerRef.current)
  }, [])

  return (
    <PortalContainerContext.Provider value={{container}}>
      {children}
      {/* Portal 容器，位于 f2c 作用域内 */}
      <div ref={containerRef} className="f2c-portal-container relative" style={{position: 'relative'}} />
    </PortalContainerContext.Provider>
  )
}
