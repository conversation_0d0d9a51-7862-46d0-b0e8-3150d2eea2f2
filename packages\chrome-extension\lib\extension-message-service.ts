export interface ExtensionMessage {
  type: string
  messageId: string
  payload?: any
  error?: string
}

export interface VersionInfo {
  version: string
}

export class ExtensionMessageService {
  private static instance: ExtensionMessageService
  private messageListeners: Map<string, Set<(data: any) => void>> = new Map()
  private pendingMessages: Map<string, {resolve: (value: any) => void; reject: (error: any) => void}> = new Map()

  constructor() {
    this.setupMessageListener()
  }

  static getInstance(): ExtensionMessageService {
    if (!ExtensionMessageService.instance) {
      ExtensionMessageService.instance = new ExtensionMessageService()
    }
    return ExtensionMessageService.instance
  }

  private setupMessageListener(): void {
    window.addEventListener('message', (event: MessageEvent) => {
      if (event.source !== window) return

      const message: ExtensionMessage = event.data
      if (message.type === 'FROM_EXTENSION') {
        // 处理来自扩展的响应
        if (message.messageId && this.pendingMessages.has(message.messageId)) {
          const {resolve, reject} = this.pendingMessages.get(message.messageId)!
          this.pendingMessages.delete(message.messageId)

          if (message.error) {
            reject(new Error(message.error))
          } else {
            resolve(message.payload)
          }
        }

        // 处理广播消息（如更新通知）
        if (message.payload?.type) {
          const listeners = this.messageListeners.get(message.payload.type)
          if (listeners) {
            listeners.forEach(listener => listener(message.payload))
          }
        }
      }
    })
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  private sendMessage(type: string, payload?: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const messageId = this.generateMessageId()

      // 存储Promise的resolve和reject函数
      this.pendingMessages.set(messageId, {resolve, reject})

      // 发送消息到content script
      window.postMessage(
        {
          type: 'TO_EXTENSION',
          messageId,
          payload: {
            type,
            ...payload,
          },
        },
        '*',
      )

      // 设置超时
      setTimeout(() => {
        if (this.pendingMessages.has(messageId)) {
          this.pendingMessages.delete(messageId)
          reject(new Error('Message timeout'))
        }
      }, 30000) // 30秒超时
    })
  }

  // 版本管理相关方法
  async getCurrentVersion(): Promise<string> {
    try {
      const result: VersionInfo = await this.sendMessage('getCurrentVersion')
      return result.version
    } catch (error) {
      console.warn('获取版本失败，使用默认版本:', error)
      return '1.0.0'
    }
  }

  async applyUpdate(): Promise<void> {
    try {
      await this.sendMessage('applyUpdate')
    } catch (error) {
      console.warn('应用更新失败:', error)
      throw error
    }
  }

  // 监听器管理
  addUpdateListener(listener: (data: any) => void): void {
    if (!this.messageListeners.has('updateAvailable')) {
      this.messageListeners.set('updateAvailable', new Set())
    }
    this.messageListeners.get('updateAvailable')!.add(listener)
  }

  removeUpdateListener(listener: (data: any) => void): void {
    const listeners = this.messageListeners.get('updateAvailable')
    if (listeners) {
      listeners.delete(listener)
    }
  }

  // 通用监听器方法
  addEventListener(type: string, listener: (data: any) => void): void {
    if (!this.messageListeners.has(type)) {
      this.messageListeners.set(type, new Set())
    }
    this.messageListeners.get(type)!.add(listener)
  }

  removeEventListener(type: string, listener: (data: any) => void): void {
    const listeners = this.messageListeners.get(type)
    if (listeners) {
      listeners.delete(listener)
    }
  }
}

// 导出默认实例
export const extensionMessageService = ExtensionMessageService.getInstance()

// 导出便捷方法
export const getCurrentVersion = () => extensionMessageService.getCurrentVersion()
export const applyUpdate = () => extensionMessageService.applyUpdate()
