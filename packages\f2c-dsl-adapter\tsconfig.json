{"compilerOptions": {"target": "es6", "lib": ["es2021"], "module": "ESNext", "jsx": "preserve", "experimentalDecorators": true, "baseUrl": ".", "allowJs": true, "esModuleInterop": true, "outDir": "./dist", "skipLibCheck": true, "isolatedModules": false, "moduleResolution": "Node", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "preserveConstEnums": true, "typeRoots": ["./node_modules/@types", "./node_modules/@figma"], "noUnusedLocals": true, "noUnusedParameters": true}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "dist"]}