import J<PERSON>Z<PERSON> from 'jszip'
import React, {useCallback, useEffect, useState} from 'react'
import {useLocation} from 'react-router-dom'
import {toast} from 'sonner'
import VersionUpdateNotification from '@/components/VersionUpdateNotification'
import Auth from '@/lib/auth'
import {getDimensionBaseInfo} from '@/lib/dimension'
import {f2cDataReport, ReportType} from '@/report/dataReport'
import dimensionStore from '@/store/dimensionStore'
import {downloadFile} from '@/utils/common-utils'
export enum ActionType {
  DOWNLOAD_FILE = 0,
  PREVIEW_HTML = 1,
}

// 全局状态和逻辑提供者
export const useAppLogic = () => {
  const [currentNodeId, setCurrentNodeId] = useState<string>('')
  const [previewUrl, setPreviewUrl] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [clearTokenTooltip, setClearTokenTooltip] = useState('')
  const [popoverOpen, setPopoverOpen] = useState(false)
  const [drapDwonOpen, setDrapDwonOpen] = useState(false)
  const [curUrl, setCurUrl] = useState('')
  const location = useLocation()

  const getCurUrlInfo = async (): Promise<{
    currentUrl: string
    fileKey: string
    nodeId: string
    name: string
  }> => {
    try {
      const currentUrl = window.location.href
      const currentNodeName = await figma.getNodeByIdAsync(currentNodeId)
      if (!currentUrl) {
        throw new Error('No URL found')
      }

      // 从 URL 中提取 fileKey
      const fileKeyMatch = currentUrl.split('/design/')[1]?.split('/')[0]

      return {
        currentUrl,
        fileKey: fileKeyMatch || '',
        nodeId: currentNodeId,
        name: currentNodeName?.name || '',
      }
    } catch (error) {
      console.error('获取URL信息失败:', error)
      return {
        currentUrl: '',
        fileKey: '',
        nodeId: '',
        name: '',
      }
    }
  }

  const handleZulu = async () => {
    const getComateUrl = async () => {
      const {currentUrl, fileKey: fileKeyFromUrl, name} = await getCurUrlInfo()
      if (fileKeyFromUrl) {
        return `comate://baiducomate.comate/open-comate-agent?conversationType=F2cBotConversation&figmaToken=${dimensionStore.globalSettings.personalToken}&figmaUrl=${currentUrl}&name=${name}&strategy=accuracy`
      }
    }
    getComateUrl().then(url => {
      window.open(url)
      f2cDataReport(ReportType.codeCount, '', 1, {
        dim5: 'chrome',
      })
    })
  }

  const handleSelectionChange = useCallback(async () => {
    const selection = window.figma?.currentPage?.selection
    const node = window.figma?.currentPage?.selection?.[0]
    if (selection?.length > 0 && selection[0] && typeof selection[0].getCSSAsync === 'function') {
      const dimensionInfo = await getDimensionBaseInfo(selection[0])
      dimensionStore.setDimensionInfo(dimensionInfo)
    }
    setCurrentNodeId(node?.id)
  }, [])

  const handlePopover = async (nodeIds = currentNodeId) => {
    setPopoverOpen(true)
    setIsLoading(true)
    figma.getNodeByIdAsync(nodeIds).then((node: BaseNode | null) => {
      if (!node) return
      ;(node as FrameNode)
        .exportAsync({
          format: 'PNG',
          constraint: {
            type: 'SCALE',
            value: 1,
          },
        })
        .then(buf => {
          const blob = new Blob([buf], {type: `image/PNG`})
          const url = URL.createObjectURL(blob)
          setPreviewUrl(url)
          setIsLoading(false)
        })
    })
  }

  // 该函数的运行错误会由toast.promise处理
  const handleDownloadZIP = async (url: string, filename: string) => {
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status} ${response.statusText}`)
    }
    const zip = new JSZip()
    zip.file(`${filename}.html`, await response.bytes())
    await downloadFile(await zip.generateAsync({type: 'blob'}), {filename: `figma-${filename}.zip`})
  }

  const handleAction = async (action: ActionType) => {
    if (!dimensionStore.globalSettings.personalToken) {
      dimensionStore.setModalShow({})
      return
    }
    const {fileKey: fileKeyFromUrl, nodeId} = await getCurUrlInfo()
    console.log('fileKeyFromUrl', fileKeyFromUrl, 'nodeId', nodeId)
    const url = `https://f2c-figma-api.yy.com/api/nodes?fileKey=${fileKeyFromUrl}&nodeIds=${currentNodeId}&format=html&access_token=${dimensionStore.globalSettings.personalToken}`

    switch (action) {
      case ActionType.DOWNLOAD_FILE:
        toast.promise(handleDownloadZIP(url, `${fileKeyFromUrl}-${nodeId}`), {
          loading: '下载文件中...',
          success: '文件已下载至本地!',
          error: (e: any) => {
            console.error('[handleDownloadZIP->下载过程中出现错误:] ', e)
            return `文件下载失败: ${e.message}`
          },
          style: {
            '--normal-bg': 'rgba(255,255,255)',
          } as React.CSSProperties,
          richColors: true,
          position: 'top-center',
        })
        break
      case ActionType.PREVIEW_HTML:
        window.open(url)
        break
      default:
        break
    }
  }

  const getCurUrl = async () => {
    const {fileKey: fileKeyFromUrl} = await getCurUrlInfo()
    const getUrl = (fileKey: string, fileName: string, nodeId: string) =>
      `https://figma.com/design/${fileKey}/${fileName}?node-id=${nodeId}`

    const urls = window.figma.currentPage.selection?.map(item => {
      return getUrl(fileKeyFromUrl || window.figma.fileKey!, window.figma.root.name, item.id)
    })
    setCurUrl(urls.join('\n'))
  }

  const handleClearToken = async (event: {stopPropagation: () => void}) => {
    event.stopPropagation()
    try {
      await Auth.impl.removeAuth()
      setClearTokenTooltip('Token已清除')
      setTimeout(() => {
        setClearTokenTooltip('')
        setDrapDwonOpen(false)
        setPopoverOpen(false)
      }, 2000)
    } catch (error) {
      setClearTokenTooltip('清除失败')
      setDrapDwonOpen(false)
      setPopoverOpen(false)
      setTimeout(() => {
        setClearTokenTooltip('')
        setDrapDwonOpen(false)
        setPopoverOpen(false)
      }, 2000)
    }
  }

  // 初始化和事件监听
  useEffect(() => {
    handleSelectionChange()
  }, [])

  useEffect(() => {
    const canvas = document.querySelector('#fullscreen-root canvas')
    canvas?.addEventListener('click', handleSelectionChange)
    return () => {
      canvas?.removeEventListener('click', handleSelectionChange)
    }
  }, [handleSelectionChange])

  useEffect(() => {
    const leftPanel = document.querySelector('#left-panel-container')
    leftPanel?.addEventListener('click', handleSelectionChange)
    return () => {
      leftPanel?.removeEventListener('click', handleSelectionChange)
    }
  }, [handleSelectionChange])

  useEffect(() => {
    const canvas = document.querySelector('#fullscreen-root canvas')
    function closed() {
      setDrapDwonOpen(false)
      setPopoverOpen(false)
    }
    document.addEventListener('click', closed)
    canvas?.addEventListener('click', closed)

    const delayId = setTimeout(() => {
      f2cDataReport(ReportType.pluginOpenCount, '', 1, {
        dim4: 'chrome',
      })
      f2cDataReport(ReportType.userHeartbeat, '', 0, {
        dim4: 'chrome',
      })
      clearTimeout(delayId)
    }, 5000)
    // 30s上报一次，统计在线时长
    const heartbeatId = setInterval(() => {
      f2cDataReport(ReportType.userHeartbeat, '', 30, {
        dim4: 'chrome',
      })
    }, 30 * 1000)

    return () => {
      document.removeEventListener('click', closed)
      canvas?.removeEventListener('click', closed)
      if (heartbeatId) {
        clearInterval(heartbeatId)
      }
    }
  }, [])

  return {
    currentNodeId,
    previewUrl,
    isLoading,
    clearTokenTooltip,
    popoverOpen,
    setPopoverOpen,
    drapDwonOpen,
    setDrapDwonOpen,
    curUrl,
    location,
    handleZulu,
    handlePopover,
    handleAction,
    getCurUrl,
    handleClearToken,
  }
}

// AppProvider组件
export const AppProvider: React.FC<{children: React.ReactNode}> = ({children}) => {
  return (
    <>
      {children}
      <VersionUpdateNotification
        autoCheck={true}
        checkInterval={2 * 60 * 60 * 1000} // 2个钟检查一次
      />
    </>
  )
}
