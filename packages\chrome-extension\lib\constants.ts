export enum Platfrom {
  Web = 'web',
  Android = 'android',
  Ios = 'ios',
  WeChatMini = 'weChatMini',
  Other = 'other',
  RN = 'RN',
  React = 'React',
}
export enum DemisionColor {
  HEX = 'hex',
  RGBA = 'rgba',
  HSLA = 'hsla',
  HSBA = 'hsba',
}
export const ColorOptions = Object.keys(DemisionColor).map(item => {
  return {
    value: DemisionColor[item as keyof typeof DemisionColor],
    label: DemisionColor[item as keyof typeof DemisionColor],
  }
})

