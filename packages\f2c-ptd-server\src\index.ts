import { ConfigManager } from "./config/ServerConfig";
import { ChannelManager } from "./managers/ChannelManager";
import { MessageHandler } from "./handlers/MessageHandler";
import type { ServerWebSocket, Server } from "bun";
import { logger } from "./log/logger";

// 初始化配置管理器
const configManager = new ConfigManager();
const config = configManager.getConfig();

// 初始化频道管理器
const channelManager = new ChannelManager();

// 监控系统已禁用
const serverMonitor = null;

// 初始化消息处理器
const messageHandler = new MessageHandler({
  channelManager,
  onMCPCommand: async (ws: any, message: any) => {
    logger.info(`MCP Command in channel ${message.channel}: ${message}`);
    // 广播 MCP 命令给频道内所有客户端
    channelManager.broadcastToChannel(message.channel, {
      type: "system",
      message: `MCP Command executed: ${JSON.stringify(message.message)}`,
      timestamp: Date.now(),
    });
  },
});

// 客户端信息管理
interface ClientInfo {
  lastHeartbeat: number;
}

const clientInfos = new Map<ServerWebSocket<any>, ClientInfo>();
let activeConnections = 0;

// 缓存的消息
const CACHED_MESSAGES = {
  WELCOME: JSON.stringify({
    type: "system",
    message: "Connected to F2C-MCP WebSocket server",
    timestamp: Date.now(),
  }),
  HEARTBEAT: JSON.stringify({ type: "ping", timestamp: Date.now() }),
};

// 心跳检测
function startHeartbeat(ws: ServerWebSocket<any>) {
  const interval = setInterval(() => {
    const clientInfo = clientInfos.get(ws);
    if (!clientInfo) {
      clearInterval(interval);
      return;
    }

    const now = Date.now();
    if (now - clientInfo.lastHeartbeat > config.heartbeatTimeout) {
      logger.info(
        `Client ${ws.remoteAddress} heartbeat timeout, disconnecting`
      );
      clearInterval(interval);
      cleanupClient(ws);
      return;
    }

    try {
      ws.send(CACHED_MESSAGES.HEARTBEAT);
    } catch (error) {
      console.error(`Failed to send heartbeat to ${ws.remoteAddress}:`, error);
      clearInterval(interval);
      cleanupClient(ws);
    }
  }, config.heartbeatInterval);
}

// 清理客户端
function cleanupClient(ws: ServerWebSocket<any>) {
  try {
    // 从频道中移除客户端
    channelManager.removeClient(ws as any);

    // 清理客户端信息
    clientInfos.delete(ws);

    // 减少连接计数
    if (activeConnections > 0) {
      activeConnections--;
    }

    logger.info(
      `Client ${ws.remoteAddress} disconnected (${activeConnections}/${config.maxConnections})`
    );

    // 关闭连接
    if (ws.readyState === 1) {
      // OPEN
      ws.close();
    }
  } catch (error) {
    console.error(`Error during client cleanup:`, error);
  }
}

// 处理新连接
function handleConnection(ws: ServerWebSocket<any>) {
  // 检查连接数限制
  if (activeConnections >= config.maxConnections) {
    console.warn(
      `Connection limit reached (${config.maxConnections}), rejecting ${ws.remoteAddress}`
    );
    ws.close(1013, "Server overloaded");
    return;
  }

  activeConnections++;
  logger.info(
    `New connection from ${ws.remoteAddress} (${activeConnections}/${config.maxConnections})`
  );

  // 初始化客户端信息
  clientInfos.set(ws, {
    lastHeartbeat: Date.now(),
  });

  // 启动心跳检测（如果启用）
  if (config.enableHeartbeat) {
    startHeartbeat(ws);
  }

  // 发送欢迎消息
  try {
    ws.send(CACHED_MESSAGES.WELCOME);
  } catch (error) {
    console.error(
      `Failed to send welcome message to ${ws.remoteAddress}:`,
      error
    );
    cleanupClient(ws);
  }
}

// 处理消息
function handleMessage(ws: ServerWebSocket<any>, message: string) {
  try {
    // 检查消息大小
    const messageSize = Buffer.byteLength(message, "utf8");
    if (messageSize > config.maxMessageSize) {
      console.warn(
        `Message too large from ${ws.remoteAddress}: ${messageSize} bytes`
      );
      ws.send(
        JSON.stringify({
          type: "error",
          message: `Message too large. Max size: ${config.maxMessageSize} bytes`,
        })
      );
      return;
    }

    const data = JSON.parse(message);

    // 处理心跳响应
    if (data.type === "pong") {
      const clientInfo = clientInfos.get(ws);
      if (clientInfo) {
        clientInfo.lastHeartbeat = Date.now();
      }
      return;
    }

    // 使用新的消息处理器处理消息
    messageHandler.handleMessage(ws as any, data);
  } catch (err) {
    console.error(`Error handling message from ${ws.remoteAddress}:`, err);

    // 发送错误响应
    try {
      ws.send(
        JSON.stringify({
          type: "error",
          message: "Invalid message format",
        })
      );
    } catch (sendError) {
      console.error(`Failed to send error response:`, sendError);
      cleanupClient(ws);
    }
  }
}

// 创建服务器
const server = Bun.serve({
  port: config.port,
  fetch(req: Request, server: Server) {
    if (req.method === "OPTIONS") {
      return new Response(null, {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type, Authorization",
        },
      });
    }

    const success = server.upgrade(req);
    if (success) {
      return undefined;
    }

    return new Response("WebSocket upgrade failed", { status: 400 });
  },
  websocket: {
    open: handleConnection,
    message: handleMessage,
    close: cleanupClient,
    drain(ws) {
      logger.info(`WebSocket backpressure relieved for ${ws.remoteAddress}`);
    },
  },
});

// 启动服务器
logger.info(`🚀 WebSocket server running on port ${server.port}`);
configManager.printConfig();
logger.info(`📡 Server ready for connections...`);
logger.info(`💓 Heartbeat: ${config.enableHeartbeat ? "enabled" : "disabled"}`);
logger.info(
  `🔒 Rate limiting: ${config.enableRateLimit ? "enabled" : "disabled"}`
);

// 优雅关闭处理
process.on("SIGINT", () => {
  logger.info("\n🛑 Shutting down server...");

  // 关闭所有连接
  clientInfos.forEach((_, ws) => {
    cleanupClient(ws);
  });

  logger.info("✅ Server shutdown complete");
  process.exit(0);
});

// 导出服务器实例和管理器（用于测试或外部访问）
export { server, configManager, serverMonitor, channelManager, messageHandler };
