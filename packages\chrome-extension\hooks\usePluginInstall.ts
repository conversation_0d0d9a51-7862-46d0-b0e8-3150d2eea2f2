import {useCallback, useRef, useState} from 'react'
import {toast} from 'sonner'
import {createPluginWorkerRequester} from '../lib/createPluginWorker'
import SNAPSHOT_PLUGINS from '../plugins/available-plugins.json'

const REGISTERED_SOURCE_RE = /@[a-z\d_-]+/

function getRegisteredPluginSource(source: string) {
  const name = source.slice(1)

  // Use local available-plugins.json directly instead of fetching from remote
  const pluginList = SNAPSHOT_PLUGINS as {
    name: string
    url: string
  }[]

  const plugins = Object.fromEntries(pluginList.map(({name, url}) => [name, url]))

  if (!plugins[name]) {
    throw new Error(`"${name}" is not a registered plugin.`)
  }

  return plugins[name]
}

export type PluginData = {
  code: string
  pluginName: string
  source: string // can be a URL or a registered plugin name like `@{plugin-name}`
}

export function usePluginInstall() {
  const [validity, setValidity] = useState('')
  const [installing, setInstalling] = useState(false)
  const controllerRef = useRef<AbortController | null>(null)

  const cancel = useCallback(() => {
    controllerRef.current?.abort()
    setInstalling(false)
  }, [])

  const install = useCallback(
    async (src: string, isUpdate = false): Promise<PluginData | null> => {
      console.log('🚀 Starting plugin installation...', {src, isUpdate})
      let installed: PluginData | null = null

      if (installing) {
        console.log('⏳ Installation already in progress, skipping...')
        return null
      }

      controllerRef.current?.abort()
      controllerRef.current = new AbortController()
      const {signal} = controllerRef.current
      let code: string | null = null

      console.log('📦 Setting installing state to true...')
      setInstalling(true)

      try {
        console.log('🔗 Resolving plugin URL...')
        const url = REGISTERED_SOURCE_RE.test(src) ? getRegisteredPluginSource(src) : src
        console.log('🔗 Final URL:', url)

        console.log('⬇️ Fetching plugin code...')
        const response = await fetch(url, {cache: 'no-cache', signal})
        if (response.status !== 200) {
          throw new Error('404: Not Found')
        }
        code = await response.text()
        console.log('✅ Plugin code fetched successfully, length:', code.length)

        try {
          console.log('🔧 Extracting plugin name using Web Worker...')
          // Use Web Worker for safe plugin evaluation - same as tempad-dev codegen
          const pluginName = await extractPluginNameWithWorker(code)
          console.log('🏷️ Plugin name extraction result:', pluginName)

          if (!pluginName) {
            console.error('❌ Plugin name is empty')
            setValidity('The plugin name must not be empty.')
          } else {
            console.log('✅ Plugin validation successful')
            setValidity('')
            const successMessage = `Plugin "${pluginName}" ${isUpdate ? 'updated' : 'installed'} successfully.`
            console.log('📢 Success message:', successMessage)
            toast.success(successMessage)
            installed = {code, pluginName, source: src}
            console.log('✅ PluginData created:', installed)
          }
        } catch (e) {
          const message = e instanceof Error ? e.message : 'Unknown error'
          console.error('❌ Plugin evaluation failed:', message, e)
          setValidity(`Failed to evaluate the code: ${message}`)
        }
      } catch (e) {
        if (signal.aborted) {
          console.log('🛑 Installation cancelled by user')
          return null
        }
        const message = e instanceof Error ? e.message : 'Unknown error'
        console.error('❌ Plugin installation failed:', message, e)
        setValidity(`Failed to install plugin: ${message}`)
      } finally {
        console.log('🔄 Setting installing state to false...')
        setInstalling(false)
      }

      console.log('🎯 Installation complete, result:', installed)
      return installed
    },
    [installing],
  )

  return {
    validity,
    installing,
    install,
    cancel,
    setValidity,
  }
}

// Safe plugin name extraction using Web Worker - same as tempad-dev codegen pattern
async function extractPluginNameWithWorker(code: string): Promise<string | null> {
  console.log('🔧 Starting plugin name extraction with Worker...')

  let workerRequester: ReturnType<typeof createPluginWorkerRequester> | null = null

  try {
    // Create worker requester - same pattern as tempad-dev
    console.log('🔧 Creating worker requester...')
    workerRequester = createPluginWorkerRequester()

    console.log('🔧 Sending plugin code to worker...')
    const result = await workerRequester.evaluate({pluginCode: code})

    console.log('🔧 Plugin name extraction successful:', result.pluginName)
    return result.pluginName || null
  } catch (e) {
    console.error('🔧 Failed to extract plugin name with worker:', e)
    throw e
  } finally {
    // Clean up worker
    if (workerRequester) {
      console.log('🔧 Terminating worker...')
      workerRequester.terminate()
    }
  }
}
