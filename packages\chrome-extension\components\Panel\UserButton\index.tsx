import {useEffect} from 'react'
import {toast} from 'sonner'
import {Avatar, AvatarFallback, AvatarImage} from '@/components/ui/avatar'
import {Button} from '@/components/ui/button'
import {Popover, PopoverContent, PopoverTrigger} from '@/components/ui/popover'
import VersionDisplay from '@/components/VersionDisplay'

import {cn} from '@/lib/utils'
import loginStore from '@/store/loginStore'
import {copyToClipboard} from '@/utils/copyClipboard'
import {AVATAR_URL, FEEDBACK_GROUP} from './constant'

// 用户头像组件
function UserAvatar({src, alt, className}: {src?: string; alt?: string; className?: string}) {
  return (
    <Avatar className={cn('w-6 h-6', className)}>
      <AvatarImage src={src} alt={alt || '用户头像'} />
      <AvatarFallback className="bg-blue-100 text-blue-500">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" className="text-blue-500">
          <path
            d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"
            fill="currentColor"
          />
        </svg>
      </AvatarFallback>
    </Avatar>
  )
}

export interface UserButtonProps {
  className?: string
}

export default function UserButton({className}: UserButtonProps) {
  const {isLoggedIn, email, setShowLogin, loginOut} = loginStore.state
  useEffect(() => {
    loginStore.checkLoginStatus()
  }, [])
  // 操作按钮区域
  const actionContent = isLoggedIn ? (
    <Button
      variant="ghost"
      className="w-full justify-start text-red-500 hover:text-red-600 hover:bg-red-50 p-2"
      onClick={() =>
        toast.promise(loginOut, {
          loading: '退出登录中...',
          success: '退出登录成功!',
          error: '退出登录失败',
        })
      }
    >
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" className="mr-2">
        <path
          d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"
          fill="currentColor"
        />
      </svg>
      退出登录
    </Button>
  ) : (
    <Button
      className="w-full text-blue-500 hover:text-blue-600 hover:bg-blue-50"
      variant="ghost"
      onClick={() => {
        setShowLogin(true)
      }}
    >
      点击登录账号
    </Button>
  )

  return (
    <div className={className}>
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="ghost" className="p-1 h-auto">
            <UserAvatar src={isLoggedIn ? AVATAR_URL : undefined} />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-64 p-4 border-0 shadow-lg" align="end">
          <div className="space-y-4">
            <VersionDisplay />
            <div className="flex flex-col items-center space-y-3">
              <UserAvatar src={isLoggedIn ? AVATAR_URL : undefined} className="w-12 h-12" />
              {isLoggedIn && (
                <div className="text-center">
                  <div className="font-medium text-sm truncate">开发工程师</div>
                  <div className="text-xs text-gray-500 truncate">{email}</div>
                </div>
              )}
            </div>

            {/* 登录/退出按钮 */}
            {actionContent}

            <div className="border-t border-gray-100"></div>

            {/* 公共功能区域 */}
            <div className="space-y-3">
              <a
                className="text-[12px] text-blue-500 hover:text-blue-700 cursor-pointer"
                href="https://f2c.yy.com"
                target="_blank"
                rel="noreferrer"
              >
                使用教程
              </a>
              <div className="space-y-1">
                <div className="flex items-center justify-between">
                  <span className="text-[12px] text-gray-600">意见反馈&交流 如流群：</span>
                  <a
                    href="https://applink-infoflow.baidu.com/share/contact/open/?token=4zw5n3U3pv117eYegjH0zoVsmD7khVvzJuxaIGihZ1E"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[12px] underline text-sky-400 cursor-pointer"
                  >
                    {FEEDBACK_GROUP}
                  </a>
                  <button
                    className="text-gray-400 hover:text-gray-600 p-1"
                    type="button"
                    onClick={() =>
                      toast.promise(copyToClipboard(FEEDBACK_GROUP), {
                        loading: '复制中...',
                        success: '复制成功',
                      })
                    }
                    title="复制群号"
                  >
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                      <path
                        d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"
                        fill="currentColor"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
