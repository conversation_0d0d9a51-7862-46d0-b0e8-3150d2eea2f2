import { useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/radix-select';
import { DropdownMenuItem } from '@radix-ui/react-dropdown-menu';

const BusinessSettings = () => {
    // 占位数据，您可以替换为从API获取的真实数据
    const businessGroups = [
        { value: 'group-a', label: '业务组A' },
        { value: 'group-b', label: '业务组B' },
        { value: 'group-c', label: '业务组C' },
    ];

    const buckets = [
        { value: 'bucket-1', label: 'Bucket-1' },
        { value: 'bucket-2', label: 'Bucket-2' },
        { value: 'bucket-3', label: 'Bucket-3' },
    ];

    const [selectedGroup, setSelectedGroup] = useState('');
    const [selectedBucket, setSelectedBucket] = useState('');

    return (
        <div className="p-4">
            <DropdownMenuItem>
            <div className="mb-4">
                <label className="block mb-2 text-sm font-medium text-gray-900">业务分组</label>
                <Select value={selectedGroup} onValueChange={setSelectedGroup}>
                    <SelectTrigger className="w-full">
                        <SelectValue placeholder="请选择业务分组" />
                    </SelectTrigger>
                    <SelectContent>
                        {businessGroups.map(group => (
                            <SelectItem key={group.value} value={group.value}>
                                {group.label}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            </div>
            </DropdownMenuItem>
            <DropdownMenuItem>
            <div>
                <label className="block mb-2 text-sm font-medium text-gray-900">所属Bucket</label>
                <Select value={selectedBucket} onValueChange={setSelectedBucket}>
                    <SelectTrigger className="w-full">
                        <SelectValue placeholder="请选择所属Bucket" />
                    </SelectTrigger>
                    <SelectContent>
                        {buckets.map(bucket => (
                            <SelectItem key={bucket.value} value={bucket.value}>
                                {bucket.label}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            </div>
            </DropdownMenuItem>

        </div>
    );
}

export default BusinessSettings;