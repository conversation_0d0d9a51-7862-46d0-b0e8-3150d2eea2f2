import waitFor from 'p-wait-for'
import ReactDOM from 'react-dom/client'
import {getCanvas, getLeftPanel} from '@/lib/utils'
import App from '../popup/App'
import './globals.css'

const sleep = (ms: number) => new Promise<true>(resolve => setTimeout(() => resolve(true), ms))

// 初始化runtime适配器

export default defineUnlistedScript(async () => {
  window.onload = async function () {
    // 添加异步加载的script标签
    const script = document.createElement('script')
    const bosScript = document.createElement('script')
    const hljsCss = '<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism.min.css">'
    document.head.insertAdjacentHTML('beforeend', hljsCss)
    script.src = 'https://enviroment-check-ee-fe.now.baidu-int.com/index.js'
    bosScript.src = 'https://bce.bdstatic.com/lib/@baiducloud/sdk/1.0.0-rc.40/baidubce-sdk.bundle.min.js'
    script.async = true
    document.head.appendChild(script)
    document.head.appendChild(bosScript)
    await waitFor(() => window.figma?.currentPage != null && getCanvas() !== null && getLeftPanel() !== null)
    await waitFor(() => sleep(50))

    const app = document.createElement('div')
    app.id = 'i2c-chrome-extension'
    app.style.position = 'relative'
    app.style.zIndex = '10'
    const root = ReactDOM.createRoot(app)
    root.render(<App />)
    document.querySelector('f2c')!.append(app)
  }
})
