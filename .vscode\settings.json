{
  "biome.enabled": true,
  "eslint.enable": false,
  "prettier.enable": false,
  "css.lint.unknownAtRules": "ignore",
  "editor.codeActionsOnSave": {
    "quickfix.biome": "explicit",
    "source.organizeImports.biome": "explicit"
  },
  "typescript.tsdk": "node_modules/typescript/lib",
  "typescript.enablePromptUseWorkspaceTsdk": true,
  "editor.defaultFormatter": "biomejs.biome",
  "[scss]": {
    "editor.defaultFormatter": "vscode.css-language-features"
  },
  "[typescript]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[json]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "mcpManager.servers": [
    {
      "name": "f2c-mcp",
      "type": "process",
      "command": "npx @f2c/mcp -y",
      "enabled": true,
    },
  ]
}
