
const API_BASE = 'https://f2c-api.yy.com'

interface ApplyUploadParams {
  businessId: number // required 业务id
  configId: number // required 配置id，如果 <=0 的话就自动生成一个
  configName: string // required 配置名称
  bucketId: number // required 选择的 bucketId, 对应业务列表 中 buckets 列表中的id
  tags: string[] // 标签，表示该配置存储的数据有哪些
  uploadFiles: string[] // 本次要上传的文件列表，包括生成的文件也需要预先定义好名称, 后端根据数组顺序为数组中每个文件名生成对应的上传文件名称
  groupId?: string // 配置所属分组，比如活动ID
  groupItemId?: string // 配置分组下的子项ID, businessId + groupId + groupItemId 唯一
}

interface ApplyUploadRes {
  code: number // 业务响应码 0 - 成功，其余失败，失败信息查看 msg
  msg: string // 错误消息
  serverTime: number // 服务端时间，毫秒
  data: {
    applyId: number // 申请ID,后端自动生成
    businessId: number // 所属业务ID
    configId: number // 所属配置id,配置id，如果 null 或者 <=0 则后台会创建一个
    configName: string // 配置名称
    version: number // 版本号
    bucketId: number // 所选bucketId
    tags: string[] // 本次上传的标签
    createUid: number // 创建者uid
    createTime: number // 创建时间
    uploadToken: {
      ak: string
      sk: string
      bucket: string
      expiration: number // token过期时间,毫秒级时间戳
      token: string
      endpoint: string
      baseUrl: string // 文件名访问前缀，baseUrl + uploadFilename 就是完整的文件访问URL
      pathPrefix: string // 上传文件前缀，形如 test/f2c/1/1/2 待上传的文件名称应该是 pathPrefix + "/" + 随机文件名.文件后缀
      uploadFiles: string[] // 上传到云存储所使用的 文件名, 和 请求中 uploadFiles 数组一一对应
    }
  }
}

async function applyUpload(
  params: ApplyUploadParams,
  figmaIdentityInfo: string,
  figmaIdentitySign: string,
): Promise<ApplyUploadRes> {
  const res = await fetch(API_BASE + '/manage/config/applyUpload', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: {
      'Content-Type': 'application/json',
      'figma-identity-info': figmaIdentityInfo,
      'figma-identity-sign': figmaIdentitySign,
    },
    credentials: 'include',
  })
  return res.json()
}

// 不同code含义 0：上传成功，1：上传中  -1： 上传失败 -2：大小超出限制 -3:文件类型错误
export const upload = async ({ bosconfig, bucket, key, item, cb, url }): Promise<string> => {
  return new Promise((resolve, reject) => {
  
    const cbRes = {
      code: -1,
      data: {
        url: '',
        progress: '',
      },
    }
    const BosClient = (window as any).baidubce?.sdk.BosClient
    const client = new BosClient(bosconfig)
    console.log('url', url)
    client
      .putObjectFromBlob(bucket, key, item.blob, { 'Content-Type': item.type })
      .then(function (res) {
        // 上传完成，添加您的代码
        cb &&
          cb({
            code: 0,
            data: {
              url: url,
              progress: 100,
            },
          })

        resolve(url)
      })
      .catch(async function (err) {
        console.log('upload err', err)
        // 上传失败，添加您的代码
        if (err.status_code === 400) {
          return await upload(item.blob)
        } else {
          console.log('err', err)
          cb && cb({ ...cbRes, code: -1 })
        }
      })
    client.on('progress', function (evt) {
      // 监听上传进度
      if (evt.lengthComputable) {
        // 添加您的代码
        const percentage = parseInt(((evt.loaded / evt.total) * 100).toString())
        console.log('上传中，已上传了' + percentage + '%')
        cb &&
          cb({
            code: 0,
            data: {
              url: '',
              progress: percentage,
            },
          })
      }
    })
  })
}


function toBlob(param: Uint8Array | string, format?: string) {
  if (Object.prototype.toString.call(param) === '[object Uint8Array]') {
    const af = (param as Uint8Array).buffer
    return { type: `image/${format || 'png'}`, blob: new Blob([af]) }
  } else {
    return { type: 'text/plain; charset=utf-8', blob: new Blob([param]) }
  }
}


export async function doBosUpload(params: {
  applyId: number
  uploadToken: any
  files: any[]
}): Promise<{ applyId: number; urls: string[] }> {
  const blobObjs = params.files.map(item => toBlob(item.content, getImgFormat(item?.format)))
  const { ak, sk, token, endpoint, uploadFiles, baseUrl, bucket } = params.uploadToken
  const bosconfig = {
    endpoint: 'https://' + endpoint,
    credentials: {
      ak, // 您的AccessKey
      sk, // 您的SecretAccessKey
    },
    sessionToken: token,
  }

  const promiseAll: Promise<string>[] = []

  blobObjs.forEach((item, index) => {
    const key = uploadFiles[index]
    const url = baseUrl + key
    promiseAll.push(
      upload({
        bosconfig,
        bucket,
        key,
        item,
        url,
        cb: () => {
          console.log('done')
        },
      }),
    )
  })

  const urls = await Promise.all(promiseAll)

  return {
    applyId: params.applyId,
    urls,
  }
}

/**
 * 批量上传图片
 * @param typeArray
 */
export async function doApplyUpload(params: {
  businessId: number
  fileNames: any[]
  bucketId: number
  configName: string
  configId?: number
  figmaIdentityInfo?: string
  figmaIdentitySign?: string
  groupId?: string
  groupItemId?: string
}): Promise<{ applyId: number; uploadToken: any }> {
  const reqData = {
    businessId: params.businessId,
    configName: params.configName,
    bucketId: params.bucketId,
    configId: params.configId || 0,
    tags: ['test'], // 后续优化
    uploadFiles: params.fileNames,
    groupId: params.groupId,
    groupItemId: params.groupItemId,
  }
  const res = await applyUpload(reqData, params.figmaIdentityInfo, params.figmaIdentitySign)
  if (res.code !== 0) {
    throw new Error(`applyUpload error, code: ${res.code}; msg: ${res.msg}`)
  }

  const { uploadToken, applyId } = res.data

  return {
    applyId,
    uploadToken,
  }
}
