import type {Option} from '@baidu/f2c-plugin-base'
import type {Node, RectangleNode, VectorNode} from '@figma/rest-api-spec'
import {isMask} from './css-util'
import {hasNonWhitelistedFonts} from './f2c/font-util'
import {RestApiAdapter} from './restApiAdapter'
import {FigmaNodeType, NodeType} from './type'
import {availableBound, isBgNode, isBlendModeNode, isEmptyEllipse, isRectangleVector, renderNode} from './util'
import Log from './utils/Log'
import {getFontsMetadata} from './utils/font-restapi'
import {checkChildrenHasText, isEmpty} from './utils/utils'

export type Feedback = {
  nodes: RestApiAdapter[]
}

export const convertRestApiNodesToF2CNodes = (
  nodes: Node[],
  nodeParent: RestApiAdapter | null = null,
  option: Option,
  depth = 0,
  maxDepth = Number.POSITIVE_INFINITY,
): Feedback => {
  const reordered: Node[] = []

  // 过滤可见且有有效边界的节点
  for (const node of nodes) {
    if (node.visible !== false && availableBound(node)) {
      reordered.push(node)
    }
  }
  Log.debug('reordered', reordered)
  const result: Feedback = {
    nodes: [],
  }

  const newNodes: RestApiAdapter[] = []

  for (const node of reordered) {
    if (!isEmpty(nodeParent) && nodeParent && nodeParent.getType() === NodeType.IMAGE) {
      continue
    }

    let newNode: RestApiAdapter = new RestApiAdapter(node, [], NodeType.IMAGE)

    if (!option.annotationMode && isBgNode(node)) {
      newNode = new RestApiAdapter(node, [], NodeType.IMAGE)
      newNodes.push(newNode)
      continue
    }
    switch (node.type) {
      case 'GROUP':
      case 'SECTION':
        newNode = new RestApiAdapter(node, [], NodeType.GROUP)
        break

      case 'FRAME':
      case 'COMPONENT':
      case 'INSTANCE':
        if (!node.absoluteRenderBounds) {
          // 节点不可见时，absoluteRenderBounds 为 null
          newNode = null as any
          break
        }
        if (renderNode(node, option, ['hasNonSolidBg', 'hasShadowEffect']) || option.reserveExcessiveNode) {
          newNode = new RestApiAdapter(node, [], NodeType.VISIBLE)
        }
        // Log.debug('renderNode', newNode)
        break

      case 'TEXT':
        // // console.log(newNode, 'text')
        const textNodePredit = new RestApiAdapter(node, [], NodeType.TEXT)
        // 检查是否包含白名单外的字体
        if (!hasNonWhitelistedFonts(Object.keys(getFontsMetadata(textNodePredit)))) {
          newNode = textNodePredit
        }
        // // console.log(newNode, 'text')
        break

      case 'RECTANGLE':
      case 'LINE':
        if (renderNode(node, option)) {
          newNode = new RestApiAdapter(node, [], NodeType.VISIBLE)
        }
        break

      case 'VECTOR':
        // // console.log(isRectangleVector(node as VectorNode), 'isVector')
        if (isRectangleVector(node as VectorNode)) {
          newNode = new RestApiAdapter(node, [], NodeType.VISIBLE)
        } else if (
          node.absoluteBoundingBox.width === 0 &&
          node.absoluteBoundingBox.height === 0 &&
          node.strokes?.length === 0 &&
          Array.isArray(node.fills) &&
          node.fills?.length === 0
        ) {
          newNode = null as any
        }
        break

      case 'ELLIPSE':
        if (isEmptyEllipse(node)) {
          newNode = null as any
        }
        break
    }
    Log.debug('newNode', newNode)

    if (newNode) {
      newNode.parent = nodeParent
    }
    if (isMask(node as RectangleNode) && nodeParent !== null) {
      // const figmaNode = node as RectangleNode
      if ((!option.preserveRendering && !option.reserverFigmaMask) || !checkChildrenHasText(nodeParent.node)) {
        nodeParent.setType(NodeType.IMAGE)
        newNode = null as any // 不需要添加为子组件了
        continue
      } else {
        // 实验性兼容，后续有问题可以删除
        if (nodeParent?.node?.type === FigmaNodeType.GROUP || nodeParent?.node?.type === FigmaNodeType.FRAME) {
          const filterArr = nodeParent.node.children.filter(
            (item: Node) => isBlendModeNode(item) && item.blendMode !== 'PASS_THROUGH',
          )
          if (filterArr.length > 0 && nodeParent.node.blendMode === 'PASS_THROUGH') {
            nodeParent.addCssAttributes({
              'mix-blend-mode': isBlendModeNode(filterArr[0])
                ? filterArr[0].blendMode.toLowerCase().replace('_', '-')
                : '',
            })
          }
        }
        // 处理最简单的遮罩用法
        nodeParent.addCssAttributes({
          width: `${nodeParent.node!.absoluteRenderBounds?.width || nodeParent.node!.absoluteBoundingBox.width}px`,
          height: `${nodeParent.node!.absoluteRenderBounds?.height || nodeParent.node!.absoluteBoundingBox.height}px`,
          overflow: 'hidden',
        })

        // newNode = null as any // 不需要添加为子组件了
        continue
      }
    }

    // 处理子节点
    if (node.children && newNode && newNode.getType() !== NodeType.IMAGE) {
      if (depth > maxDepth) {
        result.nodes = newNodes
        return result
      }
      const feedback: Feedback = convertRestApiNodesToF2CNodes(node.children, newNode, option, depth + 1, maxDepth)

      if (newNode.getType() !== NodeType.IMAGE) {
        newNode.addChildren(feedback.nodes)
      }
    }

    if (newNode) {
      newNodes.push(newNode)
    }
  }

  result.nodes = newNodes
  return result
}
