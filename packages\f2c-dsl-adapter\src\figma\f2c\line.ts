import {Direction} from './direction'
import {selectBox} from './util'

import type {JsonAdapter} from '../jsonAdapter'
import {RelativePoisition} from './type'

// 根据输入的方向获取节点的边界
export const getLineBasedOnDirection = (node: <PERSON>son<PERSON>dapter, direction: Direction) => {
  const coordinates = selectBox(node)

  if (direction === Direction.HORIZONTAL) {
    return new Line(coordinates.leftTop.y, coordinates.rightBot.y)
  }

  return new Line(coordinates.leftTop.x, coordinates.rightBot.x)
}

// Line 是节点的边界
export class Line {
  readonly upper: number
  readonly lower: number

  constructor(pointA: number, pointB: number) {
    if (pointA >= pointB) {
      this.upper = pointA
      this.lower = pointB
      return
    }

    this.upper = pointB
    this.lower = pointA
  }

  getLength(): number {
    return Math.abs(this.upper - this.lower)
  }

  getMid(): number {
    return (this.upper + this.lower) / 2
  }

  contain(point: number): boolean {
    return point >= this.lower && point <= this.upper
  }

  getRelativeLinePosition(point: number): RelativePoisition {
    if (this.getMid() > point) {
      return RelativePoisition.RIGHT
    }

    if (this.getMid() < point) {
      return RelativePoisition.LEFT
    }

    if (this.getMid() === point) {
      return RelativePoisition.MID
    }
  }

  getSymetricDifference = (point: number): number => {
    const distanceFromUpper = Math.abs(this.upper - point)
    const distanceFromLower = Math.abs(point - this.lower)
    return distanceFromUpper - distanceFromLower
  }

  overlap(l: Line, buffer: number): boolean {
    if (this.lower + buffer > l.upper) {
      return false
    }

    if (this.upper - buffer < l.lower) {
      return false
    }

    return true
  }
}

// 根据方向获取多个节点的边界集合
export const getLinesFromNodes = (nodes: JsonAdapter[], direction: Direction): Line[] => {
  const lines: Line[] = []
  for (const node of nodes) {
    const renderingBox = selectBox(node)

    if (direction === Direction.VERTICAL) {
      lines.push(new Line(renderingBox.leftTop.x, renderingBox.rightBot.x))
      continue
    }

    lines.push(new Line(renderingBox.leftTop.y, renderingBox.rightBot.y))
  }

  return lines
}

// 获取nodes在特定方向上的坐标域
export const getContainerLineFromNodes = (nodes: JsonAdapter[], direction: Direction): Line => {
  let lower = Number.POSITIVE_INFINITY
  let upper = Number.NEGATIVE_INFINITY
  if (direction === Direction.HORIZONTAL) {
    for (let i = 0; i < nodes.length; i++) {
      const renderingBox = selectBox(nodes[i])
      // console.log(nodes[i].name, renderingBox);
      lower = renderingBox.leftTop.y < lower ? renderingBox.leftTop.y : lower
      upper = renderingBox.rightBot.y > upper ? renderingBox.rightBot.y : upper
    }

    return new Line(lower, upper)
  }

  for (let i = 0; i < nodes.length; i++) {
    const renderingBox = selectBox(nodes[i])
    lower = renderingBox.leftTop.x < lower ? renderingBox.leftTop.x : lower
    upper = renderingBox.rightBot.x > upper ? renderingBox.rightBot.x : upper
  }

  return new Line(lower, upper)
}
