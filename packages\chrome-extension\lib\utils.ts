import {type ClassValue, clsx} from 'clsx'
import {twMerge} from 'tailwind-merge'

// 导入新的样式隔离工具函数
export {f2cClass, f2cn, shadcnCn} from './cn'

// 保持向后兼容的cn函数
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getCanvas() {
  return document.querySelector('#fullscreen-root .gpu-view-content canvas') as HTMLElement
}

export function getLeftPanel() {
  return document.querySelector('#left-panel-container') as HTMLElement
}
