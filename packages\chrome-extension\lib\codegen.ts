import { pluginExecutor } from './pluginExecutor'

export interface CodeBlock {
  name: string
  title: string
  code: string
  lang: string
}

export interface SerializeOptions {
  useRem: boolean
  rootFontSize: number
  scale: number
}

// Serialize CSS to string
function serializeCSS(style: Record<string, string>, options: SerializeOptions, transformOptions?: any): string {
  const cssString = Object.entries(style)
    .map(([key, value]) => `${key}: ${value};`)
    .join('\n')
  
  if (transformOptions?.transform) {
    try {
      return transformOptions.transform({ code: cssString, style, options })
    } catch (error) {
      console.error('Failed to transform CSS:', error)
      return cssString
    }
  }
  
  return cssString
}

// Main codegen function - similar to tempad-dev
export async function codegen(
  style: Record<string, string>,
  options: SerializeOptions,
  pluginCode?: string
): Promise<{ codeBlocks: CodeBlock[]; pluginName?: string }> {
  const codeBlocks: CodeBlock[] = []
  let plugin = null

  try {
    // Get active plugin
    const activePlugin = pluginExecutor.getActivePlugin()
    if (activePlugin) {
      plugin = activePlugin
    }
  } catch (e) {
    console.error('Failed to get active plugin:', e)
  }

  const {
    css: cssOptions,
    js: jsOptions,
    ...rest
  } = plugin?.code ?? {}

  // Generate CSS code block
  if (cssOptions !== false) {
    const cssCode = serializeCSS(style, options, cssOptions)
    if (cssCode) {
      codeBlocks.push({
        name: 'css',
        title: cssOptions?.title ?? 'CSS',
        lang: cssOptions?.lang ?? 'css',
        code: cssCode
      })
    }
  }

  // Generate JS code block
  if (jsOptions !== false) {
    const jsCode = serializeCSS(style, { ...options, toJS: true }, jsOptions)
    if (jsCode) {
      codeBlocks.push({
        name: 'js',
        title: jsOptions?.title ?? 'JavaScript',
        lang: jsOptions?.lang ?? 'js',
        code: jsCode
      })
    }
  }

  // Generate additional code blocks from plugin
  if (rest && typeof rest === 'object') {
    codeBlocks.push(
      ...Object.keys(rest)
        .map((name) => {
          const extraOptions = rest[name]
          if (extraOptions === false) {
            return null
          }

          const code = serializeCSS(style, options, extraOptions)
          if (!code) {
            return null
          }
          return {
            name,
            title: extraOptions.title ?? name,
            lang: extraOptions.lang ?? 'css',
            code
          }
        })
        .filter((item): item is CodeBlock => item != null)
    )
  }

  return {
    codeBlocks,
    pluginName: plugin?.name
  }
}
