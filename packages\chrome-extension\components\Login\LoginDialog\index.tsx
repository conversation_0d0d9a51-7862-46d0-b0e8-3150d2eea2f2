
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import loginStore from '@/store/loginStore'
import { ThirdPartyLoginForm } from '../ThirdPartyLoginForm'
import { YYLoginForm } from '../YYLoginForm'
import { HelpButton } from './HelpButton'

export interface LoginDialogProps {
  trigger?: React.ReactNode
}

export function LoginDialog({ trigger }: LoginDialogProps) {
  
  const {showLogin,setShowLogin} = loginStore.state
  return (
    <Dialog open={showLogin} onOpenChange={setShowLogin}>
      <DialogTrigger asChild>
        {trigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[400px] min-h-[500px] border-none bg-white box-border">
         <DialogHeader>
            <DialogTitle>登录F2C</DialogTitle>
          </DialogHeader>
          <Tabs defaultValue="yy" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="yy" className='data-[state=inactive]:bg-transparent!'>YY登录</TabsTrigger>
              <TabsTrigger value="third-party" className='data-[state=inactive]:bg-transparent!'>第三方登录</TabsTrigger>
            </TabsList>

            <TabsContent value="yy" className="mt-6">
              <YYLoginForm />
            </TabsContent>

            <TabsContent value="third-party" className="mt-6">
              <ThirdPartyLoginForm />
            </TabsContent>
          </Tabs>
        <HelpButton />
      </DialogContent>
    </Dialog>
  )
}
