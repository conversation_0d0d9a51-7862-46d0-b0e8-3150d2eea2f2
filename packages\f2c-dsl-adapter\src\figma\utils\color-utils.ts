import type {RGBA} from '@figma/rest-api-spec'
import type {Writeable} from 'src/types/AdvancedType'
import {getFloat} from './format-number-utils'

export type IColorObjLike = {
  r: number
  g: number
  b: number
  a?: number
}
export type IColorObj = IColorObjLike & {
  [k: string]: number | undefined
}

export function isColorObjEqual(color1: IColorObjLike, color2: IColorObjLike, diff = 0.01) {
  return (
    color1.r == color2.r &&
    color1.g == color2.g &&
    color1.b == color2.b &&
    Math.abs((color1.a || 1) - (color2.a || 1)) < diff
  )
}

type PaintColor = Pick<SolidPaint, 'color' | 'opacity'>
type ColorLike = RGB | RGBA | PaintColor
export function isPaintColor(rgba: ColorLike): rgba is PaintColor {
  return !!(rgba as PaintColor).color
}

export function isRgb(value: any): value is RGB | RGBA {
  return value.r != undefined && value.g != undefined && value.b != undefined
}

export function isRGBA(value: any): value is RGBA {
  return value.r != undefined && value.g != undefined && value.b != undefined && value.a != undefined
}

export function isNumEqual(a: number, b: number, diff = 0.01) {
  return Math.abs(a - b) < diff
}

export function isColorEqual(rgba1: ColorLike, rgba2: ColorLike, diff = 0.01) {
  const color1 = isPaintColor(rgba1)
    ? {...rgba1.color, a: (rgba1.opacity != undefined && rgba1.opacity) || 1}
    : isRGBA(rgba1)
      ? rgba1
      : {...rgba1, a: 1}
  const color2 = isPaintColor(rgba2)
    ? {...rgba2.color, a: (rgba2.opacity != undefined && rgba2.opacity) || 1}
    : isRGBA(rgba2)
      ? rgba2
      : {...rgba2, a: 1}
  return (
    isNumEqual(color1.r, color2.r) &&
    isNumEqual(color1.g, color2.g) &&
    isNumEqual(color1.b, color2.b) &&
    isNumEqual(color1.a || 1, color2.a || 1)
  )
}

/**
 * 255颜色值转16进制颜色值
 * @param n 255颜色值
 * @returns hex 16进制颜色值
 */
export const toHex = (n: number) => `${n > 15 ? '' : 0}${n.toString(16)}`

/**
 * rgba颜色对象转化为16进制颜色字符串
 * @param colorObj 颜色对象
 */
export const toHexString = (colorObj: IColorObjLike) => {
  const {r, g, b, a = 1} = colorObj
  return `#${toHex(r)}${toHex(g)}${toHex(b)}${a === 1 ? '' : toHex(Math.floor(a * 255))}`
}

/**
 * rgba颜色字符串转化为16进制颜色字符串
 * @param rgba rgba颜色字符串
 * @returns 16进制颜色字符串 或 16进制颜色对象{hex: string, a: number}
 */
export const rgbaToHex = (rgba: string) => {
  try {
    const colorObj = parseColorString(rgba)
    return toHexString(colorObj)
  } catch (e) {
    return ''
  }
}

/**
 * 16进制颜色字符串解析为rgba颜色对象
 * @param color 颜色字符串
 * @returns IColorObj
 */
export const parseHexColor = (color: string) => {
  let hex = color.slice(1)
  let a = 1
  if (hex.length === 3) {
    hex = `${hex[0]}${hex[0]}${hex[1]}${hex[1]}${hex[2]}${hex[2]}`
  }
  if (hex.length === 8) {
    a = Number.parseInt(hex.slice(6), 16) / 255
    hex = hex.slice(0, 6)
  }
  const bigint = Number.parseInt(hex, 16)
  return {
    r: (bigint >> 16) & 255,
    g: (bigint >> 8) & 255,
    b: bigint & 255,
    a,
  } as IColorObj
}

/**
 * 16进制颜色字符串转化为rgba颜色字符串
 * @param hex 16进制颜色字符串
 * @returns rgba颜色字符串
 */
export const hexToRgba = (hex: string) => {
  const colorObj = parseColorString(hex)
  return toRgbaString(colorObj)
}

/**
 * rgba颜色对象转化为16进制颜色字符串，并返回透明度
 * @param colorObj 颜色对象
 */
export const toHexStringWithOpacity = (colorObj: IColorObjLike) => {
  try {
    if (colorObj.r == 0 && colorObj.g == 0 && colorObj.b == 0 && colorObj.a == 0) {
      return {hex: '#00000000', a: 1}
    }
    const {r, g, b, a = 1} = colorObj
    return {hex: `#${toHex(r)}${toHex(g)}${toHex(b)}`, a}
  } catch (error) {
    return {hex: '#00000000', a: 1}
  }
}
/**
 * @description 将rgba字符串提取出16进制颜色及透明度
 * @param rgba rgba字符串
 *
 */
export const transferRgbaToHexWithOpacity = (rgba: string) => {
  const colorObj = parseColorString(rgba)
  const a = colorObj.a
  const hex = toHexString({...colorObj, a: 1})
  return {hex, a}
}

/**
 * @description 解析渐变色 返回颜色数组
 * @param colorObj 颜色对象
 */
export const parseLinearGradient = (
  gradientString: string,
): {colors: {hex: string; pos: string}[]; deg: string} | null => {
  // 正则表达式匹配渐变方向和颜色停止点
  const gradientRegex = /linear-gradient\((\d+)deg,(.*)\)/
  const match = gradientString.match(gradientRegex)

  if (!match) {
    return null // 如果不匹配，返回null
  }

  const deg = match[1] + 'deg' // 提取渐变方向
  const stopsString = match[2] // 提取颜色停止点字符串

  // 分割颜色停止点，以逗号分隔
  const stops = stopsString.split(',').map(stop => {
    // 每个停止点可能包含颜色和位置，用空格分隔
    const [color, position] = stop.trim().split(' ')

    // 确保颜色以#开头，如果不是，则添加#
    const hex = color.startsWith('#') ? color : '#' + color

    // 返回包含hex和pos的对象
    return {hex, pos: position || '100%'} // 如果没有指定位置，则默认为100%
  })

  // 返回包含颜色和方向的对象
  return {colors: stops, deg}
}
/**
 * Rgb颜色对象转化为rgb颜色字符串
 * @param colorObj 颜色对象
 */
export const toRgbString = (colorObj: IColorObjLike) => {
  const {r, g, b} = colorObj
  return `rgb(${r},${g},${b})`
}

/**
 * Rgba颜色对象转化为rgba颜色字符串
 * @param colorObj 颜色对象
 */
export const toRgbaString = (colorObj: IColorObjLike, n = 10000) => {
  const {r, g, b, a = 1} = colorObj
  return `rgba(${r},${g},${b},${Math.floor(a * n) / n})`
}

/**
 * rgba颜色字符串解析为颜色对象
 * @param color 颜色字符串
 * @returns IColorObj
 */
export const parseRgbaColor = (color: string) => {
  const arr = color.match(/(\d(\.\d+)?)+/g) || []
  const res = arr.map((s: string) => Number.parseInt(s, 10))
  return {
    r: res[0],
    g: res[1],
    b: res[2],
    a: Number.parseFloat(arr[3]),
  } as IColorObj
}

/**
 * 颜色对象转化为Hsl字符串
 * @param colorObj 颜色对象
 */
export function rgbaToHsla(colorObj: {r: number; g: number; b: number; a?: number}): string {
  const r = colorObj.r / 255
  const g = colorObj.g / 255
  const b = colorObj.b / 255
  const a = colorObj.a || 1
  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  let h = 0,
    s,
    l = (max + min) / 2

  if (max === min) {
    h = s = 0 // achromatic
  } else {
    const d = max - min
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min)
    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0)
        break
      case g:
        h = (b - r) / d + 2
        break
      case b:
        h = (r - g) / d + 4
        break
    }
    h /= 6
  }

  h = Math.round(h * 360)
  s = Math.round(s * 100 + 0.5)
  l = Math.round(l * 100 + 0.5)

  return `hsla(${getFloat(h)}, ${getFloat(s)}, ${getFloat(l)} ,${Math.floor(a * 10000) / 10000})`
}
/**
 * 颜色对象转化为Hsb字符串
 * @param colorObj 颜色对象
 */
export function rgbaToHsba(colorObj: IColorObjLike): string {
  const r = colorObj.r / 255;
  const g = colorObj.g / 255;
  const b = colorObj.b / 255;
  const a = colorObj.a !== undefined ? colorObj.a : 1;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0; // Hue
  let s = 0; // Saturation
  const v = max; // Brightness (Value) for HSB/HSV is the max component

  const delta = max - min;
  if (delta === 0) {
    h = 0; // Achromatic (grey)
    s = 0;
  } else {
    s = delta / max; // Saturation for HSB/HSV
    if (r === max) {
      h = (g - b) / delta; // Between yellow & magenta
    } else if (g === max) {
      h = 2 + (b - r) / delta; // Between cyan & yellow
    } else {
      h = 4 + (r - g) / delta; // Between magenta & cyan
    }
    h *= 60; // Convert to degrees
    if (h < 0) {
      h += 360;
    }
  }

  const hue = Math.round(h);
  const saturation = Math.round(s * 100);
  const brightness = Math.round(v * 100);

  return `hsba(${getFloat(hue)}, ${getFloat(saturation)}%, ${getFloat(brightness)}%, ${getFloat(a, 4)})`;
}

/**
 * 解析hex、rgb颜色字符串解析为颜色对象，返回rgba颜色对象
 * @param color 颜色字符串
 * @returns IColorObj
 */
export const parseColorString = (color: string) => {
  if (color.startsWith('#')) {
    return parseHexColor(color)
  } else if (color.startsWith('rgb')) {
    return parseRgbaColor(color)
  } else if (color === 'transparent') {
    return parseHexColor('#00000000')
  }
  throw new Error(`color string error: ${color}`)
}

/**
 * 颜色字符串解析为各种颜色表达方式
 * @param color 颜色字符串
 * @returns IColorObj
 */
export const getColorInfo = (color: string) => {
  const colorObj = parseColorString(color)
  const hex = toHexString(colorObj)
  const rgba = toRgbaString(colorObj)
  const rgb = toRgbString(colorObj)
  return {
    hex,
    rgba,
    rgb,
    rgbaObj: colorObj,
  }
}

/**
 * rgba颜色 或 渐变 字符串转化为16进制颜色对象
 * @param color rgba颜色或渐变色字符串
 * @returns 16进制颜色对象{hex: string, a: number}
 */
export const colorToHex = (color: string) => {
  try {
    let type
    if (color.startsWith('rgb')) {
      type = 'rgba'
    }
    if (color.startsWith('linear-gradient')) {
      type = 'Gradient'
    }
    if (color == 'transparent') {
      type = 'transparent '
    }
    if (color.startsWith('url(<path-to-image>)')) {
      type = 'image'
    }
    switch (type) {
      case 'rgba':
        const colorObj = parseColorString(color)
        return {type, ...toHexStringWithOpacity(colorObj)}
      case 'Gradient':
        const res = parseLinearGradient(color)
        return {type, ...res}
      case 'transparent':
        return {type: 'transparent', hex: '#00000000', a: 1}
      case 'image':
        return {type}
      default:
        return {type: 'none', hex: '#00000000', a: 1}
    }
  } catch (e) {
    return {type: 'none', hex: '#00000000', a: 1}
  }
}

const round = Math.round
export const roundToTwoDps = (num: number) => {
  return Math.round((num + Number.EPSILON) * 100) / 100
}

export function colorToString(color: RGB): string {
  const {r, g, b} = color
  return `rgb(${round(r * 255)},${round(g * 255)},${round(b * 255)})`
}

export function colorToStringWithOpacity(color: RGB, opacity: number): string {
  const {r, g, b} = color

  return `rgba(${round(r * 255)},${round(g * 255)},${round(b * 255)},${roundToTwoDps(opacity)})`
}

export function rgbaToString(color: RGBA, opacity = 1): string {
  const {r, g, b, a = 1} = color
  return `rgba(${round(r * 255)},${round(g * 255)},${round(b * 255)},${roundToTwoDps(a * opacity)})`
}

export function colorObjToRgba(color: IColorObjLike): RGBA {
  const result: Writeable<RGBA> = {r: 0, g: 0, b: 0, a: 1}
  for (const key in color) {
    const value = color[key]
    if (value) {
      if (key !== 'a') {
        result[key] = Number.parseFloat(String(value / 255))
      } else {
        result[key] = value
      }
    }
  }
  return result
}

export function rgbaToColorObj(rgb: RGB | RGBA): IColorObj {
  const result: IColorObj = {r: 0, g: 0, b: 0, a: 1}
  for (const key in rgb) {
    if (key !== 'a') {
      result[key] = round(rgb[key] * 255)
    } else {
      result[key] = rgb[key]
    }
  }
  return result
}

export function varValueToRgba(value: VariableValue): RGBA {
  const tempValue = value as any
  if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
    return {
      r: tempValue.r,
      g: tempValue.g,
      b: tempValue.b,
      a: 'a' in tempValue ? tempValue.a : 1,
    }
  } else {
    return {
      r: 0,
      g: 0,
      b: 0,
      a: 1,
    }
  }
}
