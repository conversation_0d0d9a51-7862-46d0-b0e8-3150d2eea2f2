import {type IColorObj} from '@/lib/color-utils'
import {DemisionColor} from '@/lib/constants'
import CopyItem from '@/components/Dimension/copyItem'
import ParseCssUtil from '../parseCss'
import { useCallback } from 'react'
import { getFloat } from '@/utils/format-number-utils'
interface IFills {
  fills: Paint[]
  colorFormat: (colorObj: IColorObj) => string
  color: DemisionColor
}
const Fills = (props: IFills) => {
  const {fills, colorFormat, color} = props
  const dealColorForamt = useCallback((colorInfo: IColorObj)=>{
    const a = color == DemisionColor.HEX ? 1 : colorInfo.a
    return colorFormat({...colorInfo, a})
  },[color, colorFormat])
  return fills.map((fill, index) => {
    switch (fill.type) {
      case 'SOLID':
        const colorObj: IColorObj = {
          r: 0,
          g: 0,
          b: 0,
          a: 1,
        }
        Object.keys(fill.color).forEach((key) => {
          if (key !== 'a') {
            colorObj[key] = parseFloat((fill.color[key as keyof RGB] * 255)?.toFixed(0))
          } else {
            colorObj[key] = getFloat(String(fill.color[key as keyof RGB]))
          }
        })
        console.log(fill, 'fill.color')
        return (
          <div className="extContent w-ful text-[12px]" key={index}>
            <CopyItem
              //   title={'颜色'}
              copyValue={dealColorForamt(colorObj)}
              value={dealColorForamt(colorObj)}
              prefix={
                <div
                  style={{
                    borderRadius: '6px',
                    border: '1px solid rgba(134, 141, 156, 0.20)',
                    background: 'transparent',
                  }}
                  className="mr-2"
                >
                  <div
                    className={`w-5 h-5 rounded`}
                    style={{
                      background: dealColorForamt(colorObj),
                      opacity: getFloat(fill.opacity),
                    }}
                  ></div>
                </div>
              }
              afterfix={
                color == DemisionColor.HEX ? (
                  <CopyItem value={getFloat((fill.opacity || 1) * 100) + '%'} disabledPropagation />
                ) : null
              }
            />
          </div>
        )
      case 'GRADIENT_LINEAR':
      case 'GRADIENT_RADIAL':
      case 'GRADIENT_ANGULAR':
      case 'GRADIENT_DIAMOND':
        const linearColor = ParseCssUtil.parseGradientColor(fill.type, fill)
        return (
          <div className="w-full h-auto mb-4">
            <CopyItem
              copyValue={linearColor}
              key={index + fill.type}
              value={fill.type.toLocaleLowerCase().substring(9)}
              prefix={
                <div
                  style={{
                    borderRadius: '6px',
                    border: '1px solid rgba(134, 141, 156, 0.20)',
                    background: 'transparent',
                  }}
                  className="mr-2"
                >
                  <div
                    className={`w-5 h-5 rounded`}
                    style={{
                      background: linearColor,
                    }}
                  ></div>
                </div>
              }
            />
            {fill.gradientStops.map((colorStop, index2) => {
              const colorObj: IColorObj = {
                r: 0,
                g: 0,
                b: 0,
                a: 1,
              }
              Object.keys(colorStop.color).forEach(key => {
                if (key !== 'a') {
                  colorObj[key] = parseFloat((colorStop.color[key as keyof RGBA] * 255)?.toFixed(0))
                } else {
                  colorObj[key] = getFloat(String(colorStop.color[key]))   
                }
              })
              // colorObj.a = color == DemisionColor.HEX ? 1 : getFloat(fill.opacity)

              return (
                <CopyItem
                  key={index + String(index2)}
                  copyValue={dealColorForamt(colorObj)}
                  value={dealColorForamt(colorObj)}
                  prefix={
                    <div
                      style={{
                        borderRadius: '6px',
                        border: '1px solid rgba(134, 141, 156, 0.20)',
                        background: 'transparent',
                      }}
                      className="mr-2"
                    >
                      <div
                        className={`w-5 h-5 rounded`}
                        style={{
                          background: dealColorForamt(colorObj),
                          opacity: getFloat(fill.opacity),
                        }}
                      ></div>
                    </div>
                  }
                  afterfix={
                    color == DemisionColor.HEX ? (
                      <CopyItem  value={getFloat((colorObj.a || 1) * 100) + '%'} disabledPropagation />
                    ) : null
                  }
                />
              )
            })}
          </div>
        )
    }
  })
}

export default Fills
