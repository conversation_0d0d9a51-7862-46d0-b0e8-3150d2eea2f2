import { logger } from "../log/logger";

// 服务器配置管理
export interface ServerConfig {
  // 服务器基础配置
  port: number;
  host?: string;

  // 连接限制
  maxConnections: number;
  maxChannels: number;
  maxChannelNameLength: number;
  maxMessageSize: number;

  // 心跳配置
  heartbeatInterval: number;
  heartbeatTimeout: number;

  // 安全配置
  enableRateLimit: boolean;
  rateLimitWindow: number; // 时间窗口（毫秒）
  rateLimitMaxRequests: number; // 最大请求数

  // 日志配置
  logLevel: "debug" | "info" | "warn" | "error";
  enableAccessLog: boolean;

  // 功能开关
  enableHeartbeat: boolean;
  enableMessageHistory: boolean;

  // 性能配置
  messageQueueSize: number;
  cleanupInterval: number;
}

// 默认配置
export const defaultConfig: ServerConfig = {
  port: 3055,
  host: "0.0.0.0",

  maxConnections: 10000,
  maxChannels: 1000,
  maxChannelNameLength: 50,
  maxMessageSize: 1024 * 1024, // 1MB

  heartbeatInterval: 30000, // 30秒
  heartbeatTimeout: 60000, // 60秒

  enableRateLimit: true,
  rateLimitWindow: 60000, // 1分钟
  rateLimitMaxRequests: 100, // 每分钟最多100个请求

  logLevel: "info",
  enableAccessLog: true,

  enableHeartbeat: true,
  enableMessageHistory: false,

  messageQueueSize: 1000,
  cleanupInterval: 60000, // 1分钟清理一次
};

// 配置验证函数
export function validateConfig(config: Partial<ServerConfig>): string[] {
  const errors: string[] = [];

  if (config.port !== undefined) {
    if (config.port < 1 || config.port > 65535) {
      errors.push("端口号必须在 1-65535 之间");
    }
  }

  if (config.maxConnections !== undefined) {
    if (config.maxConnections < 1) {
      errors.push("最大连接数必须大于 0");
    }
  }

  if (config.maxChannels !== undefined) {
    if (config.maxChannels < 1) {
      errors.push("最大频道数必须大于 0");
    }
  }

  if (config.maxChannelNameLength !== undefined) {
    if (config.maxChannelNameLength < 1 || config.maxChannelNameLength > 255) {
      errors.push("频道名最大长度必须在 1-255 之间");
    }
  }

  if (config.maxMessageSize !== undefined) {
    if (config.maxMessageSize < 1 || config.maxMessageSize > 1024 * 1024) {
      errors.push("消息最大大小必须在 1B-1MB 之间");
    }
  }

  if (config.heartbeatInterval !== undefined) {
    if (config.heartbeatInterval < 1000) {
      errors.push("心跳间隔不能少于 1 秒");
    }
  }

  if (config.heartbeatTimeout !== undefined) {
    if (config.heartbeatTimeout < config.heartbeatInterval!) {
      errors.push("心跳超时时间不能少于心跳间隔");
    }
  }

  return errors;
}

// 配置管理器
export class ConfigManager {
  private config: ServerConfig;

  constructor(customConfig?: Partial<ServerConfig>) {
    this.config = { ...defaultConfig, ...customConfig };

    // 验证配置
    const errors = validateConfig(this.config);
    if (errors.length > 0) {
      throw new Error(`配置验证失败: ${errors.join(", ")}`);
    }
  }

  /**
   * 获取配置
   */
  getConfig(): ServerConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(updates: Partial<ServerConfig>): void {
    const newConfig = { ...this.config, ...updates };

    // 验证新配置
    const errors = validateConfig(newConfig);
    if (errors.length > 0) {
      throw new Error(`配置更新失败: ${errors.join(", ")}`);
    }

    this.config = newConfig;
  }

  /**
   * 获取特定配置项
   */
  get<K extends keyof ServerConfig>(key: K): ServerConfig[K] {
    return this.config[key];
  }

  /**
   * 设置特定配置项
   */
  set<K extends keyof ServerConfig>(key: K, value: ServerConfig[K]): void {
    const updates = { [key]: value } as Partial<ServerConfig>;
    this.updateConfig(updates);
  }

  /**
   * 从环境变量加载配置
   */
  static fromEnv(): ConfigManager {
    const envConfig: Partial<ServerConfig> = {};

    if (process.env.PORT) {
      envConfig.port = parseInt(process.env.PORT, 10);
    }

    if (process.env.HOST) {
      envConfig.host = process.env.HOST;
    }

    if (process.env.MAX_CONNECTIONS) {
      envConfig.maxConnections = parseInt(process.env.MAX_CONNECTIONS, 10);
    }

    if (process.env.MAX_CHANNELS) {
      envConfig.maxChannels = parseInt(process.env.MAX_CHANNELS, 10);
    }

    if (process.env.LOG_LEVEL) {
      envConfig.logLevel = process.env.LOG_LEVEL as any;
    }

    if (process.env.ENABLE_HEARTBEAT) {
      envConfig.enableHeartbeat = process.env.ENABLE_HEARTBEAT === "true";
    }

    return new ConfigManager(envConfig);
  }

  /**
   * 导出配置为JSON
   */
  toJSON(): string {
    return JSON.stringify(this.config, null, 2);
  }

  /**
   * 打印配置信息
   */
  printConfig(): void {
    // logger.info("📊 服务器配置:");
    // logger.info(`   - 端口: ${this.config.port}`);
    // logger.info(`   - 最大连接数: ${this.config.maxConnections}`);
    // logger.info(`   - 最大频道数: ${this.config.maxChannels}`);
    // logger.info(`   - 最大消息大小: ${this.config.maxMessageSize} bytes`);
    // logger.info(`   - 心跳间隔: ${this.config.heartbeatInterval}ms`);
    // logger.info(`   - 心跳超时: ${this.config.heartbeatTimeout}ms`);
    // logger.info(`   - 日志级别: ${this.config.logLevel}`);
    // logger.info(`   - 启用心跳: ${this.config.enableHeartbeat}`);
    // logger.info(`   - 启用限流: ${this.config.enableRateLimit}`);
  }
}
