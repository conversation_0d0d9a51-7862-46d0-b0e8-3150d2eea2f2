[{"name": "example-plugin", "author": "@example", "description": "Example Chrome Extension Plugin", "repo": "https://github.com/example/chrome-extension-plugin", "url": "https://raw.githubusercontent.com/example/chrome-extension-plugin/main/dist/index.js"}, {"name": "test-plugin", "author": "@test", "description": "Test Plugin for Chrome Extension", "repo": "https://github.com/test/test-plugin", "url": "data:text/javascript;base64,Ly8gVGVzdCBQbHVnaW4KY29uc3QgcGx1Z2luID0gewogIG5hbWU6ICdUZXN0IFBsdWdpbicsCiAgdmVyc2lvbjogJzEuMC4wJywKICBkZXNjcmlwdGlvbjogJ0EgdGVzdCBwbHVnaW4gZm9yIGRlbW9uc3RyYXRpb24nLAogIAogIG9uSW5zdGFsbDogYXN5bmMgKGNvbnRleHQpID0+IHsKICAgIGNvbnRleHQudXRpbHMudG9hc3QoJ1Rlc3QgUGx1Z2luIGluc3RhbGxlZCEnLCAnc3VjY2VzcycpCiAgfSwKICAKICBvbkFjdGl2YXRlOiBhc3luYyAoY29udGV4dCkgPT4gewogICAgY29udGV4dC51dGlscy50b2FzdCgnVGVzdCBQbHVnaW4gYWN0aXZhdGVkIScsICdpbmZvJykKICB9LAogIAogIG9uRGVhY3RpdmF0ZTogYXN5bmMgKGNvbnRleHQpID0+IHsKICAgIGNvbnRleHQudXRpbHMudG9hc3QoJ1Rlc3QgUGx1Z2luIGRlYWN0aXZhdGVkIScsICdpbmZvJykKICB9LAogIAogIGNvbW1hbmRzOiB7CiAgICBoZWxsbzogKGNvbnRleHQsIG5hbWUgPSAnV29ybGQnKSA9PiB7CiAgICAgIGNvbnRleHQudXRpbHMudG9hc3QoYEhlbGxvLCAke25hbWV9IWAsICdzdWNjZXNzJykKICAgICAgcmV0dXJuIGBIZWxsbywgJHtuYW1lfSFgCiAgICB9LAogICAgCiAgICBnZXRUYWI6IGFzeW5jIChjb250ZXh0KSA9PiB7CiAgICAgIGNvbnN0IHRhYiA9IGF3YWl0IGNvbnRleHQuc3RhdGUuZ2V0Q3VycmVudFRhYigpCiAgICAgIGNvbnRleHQudXRpbHMubG9nKCdDdXJyZW50IHRhYjonLCB0YWIpCiAgICAgIHJldHVybiB0YWIKICAgIH0KICB9Cn0KCi8vIEV4cG9ydCBwbHVnaW4KaWYgKHR5cGVvZiBtb2R1bGUgIT09ICd1bmRlZmluZWQnICYmIG1vZHVsZS5leHBvcnRzKSB7CiAgbW9kdWxlLmV4cG9ydHMgPSBwbHVnaW4KfSBlbHNlIGlmICh0eXBlb2YgZXhwb3J0cyAhPT0gJ3VuZGVmaW5lZCcpIHsKICBleHBvcnRzLmRlZmF1bHQgPSBwbHVnaW4KICB9IGVsc2UgewogIHRoaXMucGx1Z2luID0gcGx1Z2luCn0="}, {"name": "kong", "author": "@Justineo", "description": "Kong Design System", "repo": "https://github.com/Justineo/tempad-dev-plugin-kong", "url": "https://raw.githubusercontent.com/Justineo/tempad-dev-plugin-kong/refs/heads/main/dist/kong.mjs"}, {"name": "kong/advanced", "author": "@Justineo", "description": "Kong Design System (Advanced)", "repo": "https://github.com/Justineo/tempad-dev-plugin-kong", "url": "https://raw.githubusercontent.com/Justineo/tempad-dev-plugin-kong/refs/heads/main/dist/kong-advanced.mjs"}, {"name": "fubukicss/unocss", "author": "@zouhangwithsweet", "description": "UnoCSS by FubukiCSS", "repo": "https://github.com/zouhangwithsweet/fubukicss-tool", "url": "https://raw.githubusercontent.com/zouhangwithsweet/fubukicss-tool/refs/heads/main/plugin/lib/index.js"}, {"name": "nuxt", "author": "@Justineo", "description": "Nuxt UI", "repo": "https://github.com/Justineo/tempad-dev-plugin-nuxt-ui", "url": "https://raw.githubusercontent.com/Justineo/tempad-dev-plugin-nuxt-ui/refs/heads/main/dist/nuxt-ui.mjs"}, {"name": "nuxt/pro", "author": "@Justineo", "description": "Nuxt UI Pro", "repo": "https://github.com/Justineo/tempad-dev-plugin-nuxt-ui", "url": "https://raw.githubusercontent.com/Justineo/tempad-dev-plugin-nuxt-ui/refs/heads/main/dist/nuxt-ui-pro.mjs"}, {"name": "baidu-health/wz-style", "author": "@KangXinzhi", "description": "Custom style for Baidu Health wz-style", "repo": "https://github.com/KangXinzhi/tempad-dev-plugin-wz-style", "url": "https://raw.githubusercontent.com/KangXinzhi/tempad-dev-plugin-wz-style/refs/heads/main/dist/index.mjs"}, {"name": "baidu-health/med-style", "author": "@KangXinzhi", "description": "Custom style for Baidu Health med-style", "repo": "https://github.com/KangXinzhi/tempad-dev-plugin-med-style", "url": "https://raw.githubusercontent.com/KangXinzhi/tempad-dev-plugin-med-style/refs/heads/main/dist/index.mjs"}, {"name": "tailwind", "author": "@haydenull", "description": "CSS to Tailwind CSS", "repo": "https://github.com/haydenull/tempad-dev-plugin-tailwind", "url": "https://raw.githubusercontent.com/haydenull/tempad-dev-plugin-tailwind/main/dist/index.mjs"}]