import {Button} from '@/components/ui/button'
import {Checkbox} from '@/components/ui/checkbox'
import {type PluginData, usePluginInstall} from '@/hooks/usePluginInstall'
import {RefreshCw, Trash2} from 'lucide-react'
import type React from 'react'
import {useEffect} from 'react'
import {toast} from 'sonner'

interface PluginItemProps {
  name: string
  source: string
  checked: boolean
  onUpdated: (pluginData: PluginData) => void
  onChange: (checked: boolean) => void
  onRemove: () => void
  className?: string
}

export const PluginItem: React.FC<PluginItemProps> = ({
  name,
  source,
  checked,
  onUpdated,
  onChange,
  onRemove,
  className = '',
}) => {
  const {validity, installing, install, cancel, setValidity} = usePluginInstall()

  // Show validation message as toast
  useEffect(() => {
    if (validity) {
      toast.error(validity)
      setValidity('')
    }
  }, [validity, setValidity])

  const handleUpdate = async () => {
    const updated = await install(source, true)
    if (updated) {
      onUpdated(updated)
    }
  }

  const handleChange = (newChecked: boolean) => {
    onChange(newChecked)
  }

  const handleRemove = () => {
    if (installing) {
      cancel()
    }
    onRemove()
  }

  return (
    <div
      className={`p-2 border rounded transition-colors ${
        checked ? 'border-primary/30 bg-primary/5' : 'border-border hover:bg-muted/30'
      } ${className}`}
    >
      {/* First row: Checkbox + Name */}
      <div className="flex items-center justify-between mb-1">
        <div className="flex items-center gap-2 flex-1 min-w-0">
          <Checkbox
            id={`plugin-${source}`}
            checked={checked}
            onCheckedChange={handleChange}
            disabled={installing}
            className="h-4 w-4 flex-shrink-0"
          />
          <div className="font-medium text-xs truncate">{name}</div>
        </div>
        {checked && <div className="px-1 py-0.5 text-xs bg-green-100 text-green-700 rounded text-[10px]">已启用</div>}
      </div>

      {/* Second row: Source + Actions */}
      <div className="flex items-center justify-between">
        <div className="text-xs text-muted-foreground truncate flex-1 min-w-0 pl-6">{source}</div>
        <div className="flex items-center gap-1 ml-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleUpdate}
            disabled={installing}
            title="更新插件"
            className="h-6 w-6 p-0 hover:bg-blue-50"
          >
            <RefreshCw className={`h-3 w-3 ${installing ? 'animate-spin' : ''}`} />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRemove}
            title="删除插件"
            className="h-6 w-6 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </div>
  )
}

export default PluginItem
