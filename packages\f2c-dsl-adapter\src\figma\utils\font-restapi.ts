import type {TypeStyle} from '@figma/rest-api-spec'
import type {RestApiAdapter} from '../restApiAdapter'
import {NodeType} from '../type'

type Font = {
  sizes: number[]
  isItalic: boolean
  familyCss: string
  weights: number[]
}

export type FontMetadataMap = {
  [family: string]: Font
}

// getFontsMetadata 收集所有在 F2C 节点中找到的字体并按字体大小排序
export const getFontsMetadata = (node: RestApiAdapter): FontMetadataMap => {
  const fonts: FontMetadataMap = {}
  findAllFonts(node, fonts)
  return fonts
}

// findAllFonts 查找所有字体
const findAllFonts = (node: RestApiAdapter, fonts: FontMetadataMap) => {
  if (node.getType() === NodeType.TEXT) {
    const textNode = node.node
    if (!textNode || textNode.type !== 'TEXT') return

    // 从 REST API 中获取文本样式
    const style = textNode.style as TypeStyle
    if (!style) return

    // 处理基本文本样式
    if (style.fontFamily && style.fontSize) {
      processFontStyle(fonts, {
        family: style.fontFamily,
        style: style.fontPostScriptName || '',
        fontSize: style.fontSize,
        fontWeight: style.fontWeight || 400,
        fontFamily: style.fontFamily,
      })
    }

    // 处理样式重写
    if (textNode.styleOverrideTable) {
      Object.values(textNode.styleOverrideTable).forEach(override => {
        if (override.fontFamily && override.fontSize) {
          processFontStyle(fonts, {
            family: override.fontFamily,
            style: override.fontPostScriptName || '',
            fontSize: override.fontSize,
            fontWeight: override.fontWeight || 400,
            fontFamily: override.fontFamily,
          })
        }
      })
    }

    return
  }

  const children = node.getChildren()
  for (const child of children) {
    findAllFonts(child, fonts)
  }
}

interface FontStyle {
  family: string
  style: string
  fontSize: number
  fontWeight: number
  fontFamily: string
}

const processFontStyle = (fonts: FontMetadataMap, style: FontStyle) => {
  const {family, style: fontStyle, fontSize, fontWeight, fontFamily} = style
  const isItalic = fontStyle.toLowerCase().includes('italic')

  if (family && fontSize) {
    const font = fonts[family]
    if (!font) {
      fonts[family] = {
        isItalic,
        familyCss: fontFamily,
        sizes: [fontSize],
        weights: [fontWeight],
      }
      return
    }

    if (!font.weights.includes(fontWeight)) {
      font.weights.push(fontWeight)
    }

    if (fontSize) {
      fonts[family].sizes.push(fontSize)
    }
  }
}
