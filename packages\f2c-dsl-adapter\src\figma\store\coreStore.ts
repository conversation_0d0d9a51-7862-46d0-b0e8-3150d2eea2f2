import type {Option} from '@baidu/f2c-plugin-base/dist/types'
import type {Node} from '@figma/rest-api-spec'
import type {JsonAdapter} from '../jsonAdapter'

interface IGeneratorConfig {
  option: Option
  rootNode?: Node
  rootAdatperNode?: JsonAdapter
}

export const GeneratorConfig: IGeneratorConfig = {
  option: {},
  rootNode: undefined,
}

export const SetGeneratorConfig = (option: Option, rootNode: Node) => {
  GeneratorConfig.option = option
  GeneratorConfig.rootNode = rootNode
}
export const getGeneratorConfig = () => {
  const option = GeneratorConfig.option || {}
  const dslOption = option.dslSetting?.[GeneratorConfig.option.curDslItem?.key || ''] || {}
  return {
    ...option,
    ...dslOption,
  }
}

export const getGenerateStore = () => {
  return GeneratorConfig
}
