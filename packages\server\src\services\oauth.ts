import { config } from '../config'
import { commonService } from './common'
import { logger } from './logger'

export class OAuthService {
  private static instance: OAuthService

  private constructor() {}

  // 单例模式
  static getInstance(): OAuthService {
    if (!OAuthService.instance) {
      OAuthService.instance = new OAuthService()
    }
    return OAuthService.instance
  }

  async handleAuth(type: 'page' | 'json' | 'astro') {
    const authUrl = `${config.figma.oauthUrl}?client_id=${config.figma.clientId}&redirect_uri=${type === 'page' ? config.figma.authBackUri : type === 'astro' ? config.figma.astroBackUri : config.figma.redirectUri}&scope=files:read&state=random&response_type=code`
    return commonService.addCorsHeaders(
      new Response(null, {
        status: 302,
        headers: {Location: authUrl},
      })
    )
  }

  async handleCallback(code: string) {
    try {
      const tokenResponse = await fetch(config.figma.tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          client_id: config.figma.clientId,
          client_secret: config.figma.clientSecret,
          redirect_uri: config.figma.redirectUri,
          code: code,
          grant_type: 'authorization_code',
        }),
      })

      if (!tokenResponse.ok) {
        throw new Error(`获取token失败: ${tokenResponse.status} ${tokenResponse.statusText}`)
      }

      const data = await tokenResponse.json()
      return commonService.addCorsHeaders(
        new Response(JSON.stringify(data), {
          headers: {'Content-Type': 'application/json'},
        })
      )
    } catch (error) {
      console.error('OAuth回调处理失败:', error)
      return commonService.addCorsHeaders(
        new Response('Error getting token: ' + error, {status: 500})
      )
    }
  }

  // 新增方法：获取回调数据而不直接返回响应
  async getCallbackData(code: string): Promise<any> {
    try {
      const tokenResponse = await fetch(config.figma.tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          client_id: config.figma.clientId,
          client_secret: config.figma.clientSecret,
          redirect_uri: config.figma.redirectUri,
          code: code,
          grant_type: 'authorization_code',
        }),
      })

      if (!tokenResponse.ok) {
        throw new Error(`获取token失败: ${tokenResponse.status} ${tokenResponse.statusText}`)
      }

      return await tokenResponse.json()
    } catch (error) {
      logger.error('OAuth获取数据失败:', error)
      return { error: true, message: error instanceof Error ? error.message : String(error) }
    }
  }

  getAuthUrl() {
    return `${config.figma.oauthUrl}?client_id=${config.figma.clientId}&redirect_uri=${config.figma.redirectUri}&scope=files:read&state=random&response_type=code`
  }
}

// 导出单例实例
export const oauthService = OAuthService.getInstance()
