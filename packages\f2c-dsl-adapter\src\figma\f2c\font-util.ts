import {getGeneratorConfig} from '../store/coreStore'

export const fontFamilyWhiteList = [
  'PingFang SC',
  'PingFang HK',
  'PingFang TC',
  'Microsoft YaHei',
  'Microsoft YaHei UI',
  'DIN',
  'DINPro',
  'DINCond-Bold',
  'FZXiaoBiaoSong-B05S',
  // 互动字体
  'Poppins',
  'Pontano Sans',
  // 'DIN-BlackItalic'
]

export const hasNonWhitelistedFonts = (arr: string[]) => {
  // console.log('figma getGeneratorConfig', getGeneratorConfig())
  return getGeneratorConfig().reserveFont ? false : arr.some(item => !fontFamilyWhiteList.includes(item))
}
