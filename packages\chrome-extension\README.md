## 安装和测试插件
1. 打开 Chrome 浏览器，进入 chrome://extensions/
2. 开启右上角的"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择 /Users/<USER>/Desktop/develop/f2c-workspace/i2c/chrome-extension 目录
## 使用说明
1. 确保你的服务器正在运行：
```bash
cd /Users/<USER>/Desktop/develop/f2c-workspace/i2c/packages/server
bun run dev
 ```
```

2. 点击 Chrome 工具栏中的插件图标
3. 在弹出窗口中点击"登录 Figma"按钮
4. 浏览器会打开 Figma 授权页面
5. 授权成功后，插件会显示获取到的 token 信息
## 注意事项
1. 这个插件使用了 Chrome 的 identity API，需要在 manifest.json 中声明权限
2. 插件需要访问 localhost:3000 ，确保服务器在这个端口运行
3. 为了安全起见，插件只显示 token 的部分信息
4. 在生产环境中，应该使用 HTTPS 并实现 token 刷新机制
这个插件实现了基本的 OAuth 授权流程，可以根据你的具体需求进一步扩展功能。


## UI库
https://ui.shadcn.com/docs/installation