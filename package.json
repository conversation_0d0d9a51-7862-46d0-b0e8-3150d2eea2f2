{"name": "i2c-workspace", "workspaces": ["packages/*"], "version": "1.0.0", "description": "i2c workspace", "main": "index.js", "type": "module", "repository": "https://git.yy.com/webs/hd/ai/i2c-server", "author": "ken <<EMAIL>>", "license": "MIT", "private": true, "scripts": {"lint": "biome check . --fix", "build:adapter": "pnpm --filter @baidu/f2c-dsl-adapter build", "build:server": "pnpm --filter @i2c/server build", "dev:server": "pnpm --filter @i2c/server dev", "dev:ptdserver": "pnpm --filter @i2c/f2c_ptd_server dev", "build:compile": "pnpm --filter @i2c/server compile:linux", "build:prod": "pnpm --filter @i2c/server build:prod", "build": "pnpm run build:adapter && pnpm run build:prod"}, "engines": {"node": ">=16.0.0", "pnpm": ">=8"}, "devDependencies": {"@biomejs/biome": "^2", "@empjs/biome-config": "^2"}, "peerDependencies": {"typescript": "^5.0.0"}}