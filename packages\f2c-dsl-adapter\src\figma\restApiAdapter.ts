import type {FrameNode, Node, TextNode} from '@figma/rest-api-spec'
import {JsonAdapter} from './jsonAdapter'
import {type Attributes, type BoxCoordinates, NodeType, type StyledTextSegment} from './type'
import {createId, isEmpty} from './utils/utils'

import {
  addDropShadowCssProperty,
  addGradientAttrs,
  addTextShadowByEffect,
  figmaLetterSpacingToCssString,
  figmaLineHeightToCssString,
  getBorderColor,
  getBorderRadius,
  getBorderStyle,
  getBorderWidth,
  getFillsColor,
  getRgbaFromPaints,
  getTextColor,
  setTextOverflow,
} from './css-util'
import {hasNonWhitelistedFonts} from './f2c/font-util'
import {absolutePositioningAnnotation} from './f2c/overlap'
import {selectBox} from './f2c/util'
import {getPositionalCssAttributes} from './position-util'
import {computePositionalRelationship} from './position-util'
import {getGeneratorConfig} from './store/coreStore'
import type {PostionalRelationship} from './type'
import {isMixed} from './util'
import {boundingBox, isAbsoluteRenderBoundsNode} from './util'
import {colorToString, rgbaToString} from './utils/color-utils'
import {convertFigmaBackgroundToCss} from './utils/convertFigmaBackgroundToCss'
export class RestApiAdapter extends JsonAdapter {
  node?: Node
  parent?: RestApiAdapter
  originType?: Node['type']
  // children: RestApiAdapter[] = []

  constructor(node?: Node | null, children: RestApiAdapter[] = [], type = NodeType.VISIBLE) {
    super(node, children, type)
    this.id = createId(node?.id)
    this.type = type as NodeType
    this.node = node!

    if (!isEmpty(node)) {
      this.name = node.name
      this.originType = node.type
      this.initCssAttributes()

      if (!getGeneratorConfig().absoluteLayout) {
        this.positionalCssAttributes = getPositionalCssAttributes(this)
      } else {
        this.addAnnotations(absolutePositioningAnnotation, true)
      }
      this.children = children
      this.initText()
      this.initStyledTextSegments()
      this.initParagraphSpacing()
      this.initFills()
    } else {
      this.name = '匿名'
      this.setChildren(children)
    }
    this.absRenderingBox = this.computeAbsRenderingBox()
  }
  // setChildren(children: RestApiAdapter[]) {
  //   this.children = children
  // }
  // addChildren(children: RestApiAdapter[]) {
  //   this.children = this.children.concat(children)
  // }
  // getChildren(): RestApiAdapter[] {
  //   return this.children
  // }
  setType(type: NodeType) {
    this.type = type
    if (type === NodeType.IMAGE) {
      this.setChildren([])
      this.initCssAttributes()
      this.filterCssAttributes({imgPropsFilter: true})
    }
    if (type === NodeType.Gradient) {
      this.initCssAttributes()
    }
  }
  getOriginalId() {
    if (this.node) {
      return this.node.id
    }
    return ''
  }
  getOriginalType() {
    if (this.node) {
      return this.node.type
    }
    return ''
  }

  // 添加 setWidthAndHeight 方法
  setWidthAndHeight() {
    const node = this.node!

    // 处理带有特效的节点
    if ('effects' in node && node.effects && node.effects.length > 0 && this.type !== NodeType.IMAGE) {
      const dropShadowEffects = node.effects.filter(
        effect => effect.visible && (effect.type === 'DROP_SHADOW' || effect.type === 'INNER_SHADOW'),
      )

      if (dropShadowEffects.length > 0) {
        if (node.absoluteBoundingBox) {
          this.cssAttributes['width'] = `${node.absoluteBoundingBox.width}px`
          this.cssAttributes['height'] = `${node.absoluteBoundingBox.height}px`
        }
        return
      }
    }

    // 获取计算后的坐标
    const coordinates = this.getComputedCoordinates()
    if (coordinates) {
      this.cssAttributes['width'] = `${coordinates.width}px`
      this.cssAttributes['height'] = `${coordinates.height}px`
    }
  }

  initCssAttributes() {
    const attributes: Attributes = {}
    const node = this.node!

    if (this.type === NodeType.IMAGE) {
      this.setWidthAndHeight()
    } else if (this.type === NodeType.GROUP) {
      this.setWidthAndHeight()
      if ('blendMode' in node && node.blendMode !== 'PASS_THROUGH') {
        attributes['mix-blend-mode'] = `${node.blendMode.toLowerCase().replace('_', '-')}`
      }
      if ('opacity' in node && typeof node.opacity === 'number' && node.opacity !== 1) {
        attributes['opacity'] = `${node.opacity}`
      }
    } else {
      if (node.type === 'FRAME' || node.type === 'COMPONENT' || node.type === 'INSTANCE') {
        if ('clipsContent' in node && node.clipsContent) {
          attributes['overflow'] = 'hidden'
        }
      }

      if ('blendMode' in node && node.blendMode !== 'PASS_THROUGH') {
        attributes['mix-blend-mode'] = `${node.blendMode.toLowerCase().replace('_', '-')}`
      }

      if ('opacity' in node && typeof node.opacity === 'number' && node.opacity !== 1) {
        attributes['opacity'] = `${node.opacity}`
      }

      if ('rotation' in node && typeof node.rotation === 'number' && node.rotation !== 0) {
        const degrees = (radians: number) => (radians * 180) / Math.PI
        attributes['transform'] = `rotate(${degrees(node.rotation)}deg)`
      }

      if (node.type === 'VECTOR' || node.type === 'ELLIPSE') {
        this.setWidthAndHeight()
        // 保留 addGradientAttrs 调用
        addGradientAttrs(node, attributes)
      }

      if (
        node.type === 'FRAME' ||
        node.type === 'RECTANGLE' ||
        node.type === 'INSTANCE' ||
        node.type === 'COMPONENT' ||
        node.type === 'VECTOR'
      ) {
        // 保留边框相关方法调用
        const borderRadius = getBorderRadius(node)
        if (borderRadius) {
          attributes['border-radius'] = borderRadius
        }

        const borderColor = getBorderColor(node)
        if (borderColor !== 'currentcolor') {
          attributes['border-color'] = borderColor
        }

        const borderStyle = getBorderStyle(node)
        if (borderStyle) {
          attributes['border-style'] = borderStyle
        }

        const borderWidth = getBorderWidth(node)
        if (borderWidth) {
          attributes['border-width'] = borderWidth
        }

        attributes['box-sizing'] = 'border-box'

        this.setWidthAndHeight()

        // 保留阴影相关方法调用
        if (
          node.type === 'RECTANGLE' ||
          (node.type === 'FRAME' && node.strokes?.length > 0) ||
          (node.type === 'INSTANCE' && node.strokes?.length > 0) ||
          (node.type === 'COMPONENT' && node.strokes?.length > 0)
        ) {
          addDropShadowCssProperty(node, attributes)
        }

        // 处理模糊效果
        if ('effects' in node) {
          const layerBlur = node.effects?.find(effect => effect.type === 'LAYER_BLUR' && effect.visible)
          if (layerBlur) {
            attributes['filter'] = `blur(${layerBlur.radius}px)`
          }

          const backgroundBlur = node.effects?.find(effect => effect.type === 'BACKGROUND_BLUR' && effect.visible)
          if (backgroundBlur) {
            attributes['backdrop-filter'] = `blur(${backgroundBlur.radius}px)`
          }
        }

        // 保留填充色方法调用
        if (this.type !== NodeType.Gradient) {
          // getFillsColor(node, attributes)
          const fillsCss = convertFigmaBackgroundToCss(
            node.fills,
            node.absoluteBoundingBox.width,
            node.absoluteBoundingBox.height,
          )

          for (const key in fillsCss) {
            attributes[key] = fillsCss[key]
          }
        }
      }

      if (node.type === 'LINE') {
        if ('strokes' in node && node.strokes) {
          const strokes = node.strokes as Paint[]
          if (strokes?.length > 0 && strokes[0].type === 'SOLID') {
            attributes['background-color'] = colorToString(strokes[0].color)
          }
        }
        this.setWidthAndHeight()
      }
      // 在 TEXT 节点处理部分修改为
      if (node.type === 'TEXT') {
        const style = (node as TextNode).style
        if (style) {
          // 文本缩进
          if (style.paragraphIndent) {
            attributes['text-indent'] = `${style.paragraphIndent}px`
          }

          // 字体家族
          let _fontFamily = ''
          if (style.fontFamily) {
            if (hasNonWhitelistedFonts([style.fontFamily])) {
              this.setWidthAndHeight()
              return attributes
            }
            _fontFamily = style.fontFamily
            attributes['font-family'] = _fontFamily
          }

          // 字体大小
          let _fontSize = 0
          if (style.fontSize) {
            _fontSize = style.fontSize
            attributes['font-size'] = `${style.fontSize}px`
          }

          // 宽高和文本自动调整大小处理
          if (node.absoluteBoundingBox) {
            if (style.textAutoResize === 'NONE' || !style.textAutoResize) {
              this.cssAttributes['width'] = `${node.absoluteBoundingBox.width}px`
              this.cssAttributes['height'] = `${node.absoluteBoundingBox.height}px`
            } else if (style.textAutoResize === 'HEIGHT') {
              this.cssAttributes['width'] = `${node.absoluteBoundingBox.width}px`
            } else if (style.textAutoResize === 'TRUNCATE') {
              this.cssAttributes['width'] = `${node.absoluteBoundingBox.width}px`
              this.cssAttributes['height'] = `${node.absoluteBoundingBox.height}px`

              const moreThanOneRow = node.absoluteRenderBounds
                ? node.absoluteRenderBounds.height >= _fontSize * 1.5
                : node.absoluteBoundingBox.height >= _fontSize * 1.5

              if (!moreThanOneRow) {
                this.addCssAttributes(setTextOverflow())
              }
            }
          }

          if (node.absoluteRenderBounds) {
            const moreThanOneRow = node.absoluteRenderBounds.height >= _fontSize * 1.5
            if (!moreThanOneRow) {
              attributes['white-space'] = 'nowrap'
            }
          } else if (node.absoluteBoundingBox) {
            const moreThanOneRow = node.absoluteBoundingBox.height >= _fontSize * 1.5
            if (!moreThanOneRow) {
              attributes['white-space'] = 'nowrap'
            }
          }

          // 文本对齐
          if (style.textAlignHorizontal) {
            switch (style.textAlignHorizontal) {
              case 'CENTER':
                attributes['text-align'] = 'center'
                break
              case 'RIGHT':
                attributes['text-align'] = 'right'
                break
              case 'JUSTIFIED':
                attributes['text-align'] = 'justify'
                break
            }
          }

          // 文本装饰
          if (style.textDecoration) {
            switch (style.textDecoration) {
              case 'STRIKETHROUGH':
                attributes['text-decoration'] = 'line-through'
                break
              case 'UNDERLINE':
                attributes['text-decoration'] = 'underline'
                break
            }
          }

          // 文本颜色
          getTextColor(node, attributes)

          // 行高
          if (style.lineHeightPx) {
            attributes['line-height'] = figmaLineHeightToCssString(style, _fontSize, _fontFamily, this.uiFrameWork)
          }

          // 文本转换
          if (style.textCase) {
            switch (style.textCase) {
              case 'UPPER':
                attributes['text-transform'] = 'uppercase'
                break
              case 'LOWER':
                attributes['text-transform'] = 'lowercase'
                break
              case 'TITLE':
                attributes['text-transform'] = 'capitalize'
                break
            }
          }

          // 字间距
          if (style.letterSpacing) {
            attributes['letter-spacing'] = figmaLetterSpacingToCssString(style)
          }

          // 字重
          if (style.fontWeight) {
            attributes['font-weight'] = style.fontWeight.toString()
          }

          // 斜体
          if (style.fontPostScriptName?.toLowerCase().includes('italic')) {
            attributes['font-style'] = 'italic'
          }

          // 字体垂直居中
          if (this.cssAttributes['width'] && this.cssAttributes['height']) {
            if (style.textAlignVertical === 'CENTER' || style.textAlignVertical === 'BOTTOM') {
              this.addAnnotations('AUTOLAYOUT', true)
              this.addAnnotations('TextAlignVerticalCompatibility', true)
              attributes['display'] = 'inline-flex'
              attributes['align-items'] = style.textAlignVertical === 'CENTER' ? 'center' : 'flex-end'
              switch (style.textAlignHorizontal) {
                case 'CENTER':
                  attributes['justify-content'] = 'center'
                  break
                case 'RIGHT':
                  attributes['justify-content'] = 'flex-end'
                  break
              }
            }
          }

          // 文本阴影
          if (node.effects?.length) {
            const textShadow = addTextShadowByEffect(node.effects)
            if (textShadow !== 'none') {
              attributes['text-shadow'] = textShadow
            }
          }
        }
      }
    }

    this.cssAttributes = {...this.cssAttributes, ...attributes}
    this.filterCssAttributes()
  }

  // 位置分析
  getComputedCoordinates() {
    if (isEmpty(this.node)) {
      return this.absRenderingBox
    }

    // 处理带有描边的节点
    if ('strokes' in this.node && this.node.strokes && this.node.strokes.length > 0) {
      const boundingBox = this.node.absoluteBoundingBox!
      let renderBounds = this.node.absoluteBoundingBox!

      // REST API 中使用 absoluteRenderBounds
      if (isAbsoluteRenderBoundsNode(this.node!) && !isEmpty(this.node.absoluteRenderBounds)) {
        renderBounds = this.node.absoluteRenderBounds
      }

      // 计算实际尺寸，取绝对边界盒和渲染边界盒中较大的值
      const x =
        boundingBox.width > renderBounds.width || boundingBox.height > renderBounds.height
          ? boundingBox.x
          : renderBounds.x
      const y =
        boundingBox.width > renderBounds.width || boundingBox.height > renderBounds.height
          ? boundingBox.y
          : renderBounds.y
      const width =
        boundingBox.width > renderBounds.width || boundingBox.height > renderBounds.height
          ? boundingBox.width
          : renderBounds.width
      const height =
        boundingBox.width > renderBounds.width || boundingBox.height > renderBounds.height
          ? boundingBox.height
          : renderBounds.height

      return {
        width,
        height,
        leftTop: {x, y},
        leftBot: {x, y: y + height},
        rightTop: {x: x + width, y},
        rightBot: {x: x + width, y: y + height},
      }
    }

    // 如果有绝对边界盒，则使用绝对边界盒的坐标
    if (boundingBox(this)) {
      return this.getAbsoluteBoundingBoxCoordinates()
    }
    // 默认返回渲染边界坐标
    return this.getRenderingBoundsCoordinates()
  }

  getAbsoluteBoundingBoxCoordinates(): BoxCoordinates {
    const boundingBox = this.node?.absoluteBoundingBox!

    return {
      width: boundingBox.width,
      height: boundingBox.height,
      leftTop: {
        x: boundingBox.x,
        y: boundingBox.y,
      },
      leftBot: {
        x: boundingBox.x,
        y: boundingBox.y + boundingBox.height,
      },
      rightTop: {
        x: boundingBox.x + boundingBox.width,
        y: boundingBox.y,
      },
      rightBot: {
        x: boundingBox.x + boundingBox.width,
        y: boundingBox.y + boundingBox.height,
      },
    }
  }

  getRenderingBoundsCoordinates(): BoxCoordinates {
    let boundingBox = this.node.absoluteBoundingBox!

    // 在 REST API 中，直接使用 absoluteRenderBounds
    if (this.node?.absoluteRenderBounds) {
      boundingBox = this.node.absoluteRenderBounds
    }

    return {
      width: boundingBox.width,
      height: boundingBox.height,
      leftTop: {
        x: boundingBox.x,
        y: boundingBox.y,
      },
      leftBot: {
        x: boundingBox.x,
        y: boundingBox.y + boundingBox.height,
      },
      rightTop: {
        x: boundingBox.x + boundingBox.width,
        y: boundingBox.y,
      },
      rightBot: {
        x: boundingBox.x + boundingBox.width,
        y: boundingBox.y + boundingBox.height,
      },
    }
  }

  getComputedContentCoordinates() {
    // 处理border情况下的Coordinates数据
    const coordinate = this.getComputedCoordinates()!
    const node = this.node!
    if ('strokes' in this.node && this.node.strokes.length > 0) {
      if (!('individualStrokeWeights' in this.node)) {
        const strokeWeight = this.node!.strokeWeight
        return {
          width: coordinate.width,
          height: coordinate.height,
          leftTop: {
            x: coordinate.leftTop.x + strokeWeight,
            y: coordinate.leftTop.y - strokeWeight,
          },
          leftBot: {
            x: coordinate.leftBot.x + strokeWeight,
            y: coordinate.leftBot.y - strokeWeight,
          },
          rightTop: {
            x: coordinate.rightTop.x - strokeWeight,
            y: coordinate.rightTop.y - strokeWeight,
          },
          rightBot: {
            x: coordinate.rightBot.x - strokeWeight,
            y: coordinate.rightBot.y - strokeWeight,
          },
        }
      } else {
        if ('individualStrokeWeights' in node && node.individualStrokeWeights) {
          const {top, right, bottom, left} = node.individualStrokeWeights
          return {
            width: coordinate.width,
            height: coordinate.height,
            leftTop: {
              x: coordinate.leftTop.x + left,
              y: coordinate.leftTop.y + top,
            },
            leftBot: {
              x: coordinate.leftBot.x + left,
              y: coordinate.leftBot.y - bottom,
            },
            rightTop: {
              x: coordinate.rightTop.x - right,
              y: coordinate.rightTop.y + top,
            },
            rightBot: {
              x: coordinate.rightBot.x - right,
              y: coordinate.rightBot.y - bottom,
            },
          }
        }
        return coordinate
      }
    } else {
      return coordinate
    }
  }

  computeAbsRenderingBox(): BoxCoordinates {
    // 如果节点存在且不为空，直接使用渲染边界
    if (!isEmpty(this.node)) {
      this.absRenderingBox = this.getRenderingBoundsCoordinates()
      return this.absRenderingBox
    }

    // 如果节点为空，需要计算所有子节点的边界框
    let xl = Number.POSITIVE_INFINITY
    let xr = Number.NEGATIVE_INFINITY
    let yt = Number.POSITIVE_INFINITY
    let yb = Number.NEGATIVE_INFINITY

    // 遍历所有子节点计算边界
    for (const child of this.getChildren()) {
      // 使用selectBox获取子节点的坐标信息
      const coordinates = selectBox(child)

      // 更新最左边界
      if (coordinates.leftTop.x < xl) {
        xl = coordinates.leftTop.x
      }

      // 更新最右边界
      if (coordinates.rightBot.x > xr) {
        xr = coordinates.rightBot.x
      }

      // 更新最上边界
      if (coordinates.leftTop.y < yt) {
        yt = coordinates.leftTop.y
      }

      // 更新最下边界
      if (coordinates.rightBot.y > yb) {
        yb = coordinates.rightBot.y
      }
    }

    // 更新节点的CSS属性
    this.cssAttributes['width'] = `${Math.abs(xr - xl)}px`
    this.cssAttributes['height'] = `${Math.abs(yb - yt)}px`

    // 构造并返回边界框信息
    const info: BoxCoordinates = {
      width: Number(Math.abs(xr - xl)),
      height: Number(Math.abs(yb - yt)),
      leftTop: {
        x: xl,
        y: yt,
      },
      leftBot: {
        x: xl,
        y: yb,
      },
      rightTop: {
        x: xr,
        y: yt,
      },
      rightBot: {
        x: xr,
        y: yb,
      },
    }

    return info
  }

  getPositionalRelationship(targetNode: RestApiAdapter): PostionalRelationship {
    const currentBox: BoxCoordinates = this.getComputedContentCoordinates()!
    const targetBox: BoxCoordinates = targetNode.getComputedContentCoordinates()!

    return computePositionalRelationship(currentBox, targetBox)
  }
  // 文字相关
  initStyledTextSegments() {
    this.textInfo.styledTextSegments = this.handleStyledTextSegments() || []
  }
  handleStyledTextSegments(): StyledTextSegment[] | undefined {
    if (this.node?.type !== 'TEXT') return undefined

    const textNode = this.node
    const segments: Array<{
      characters: string
      start: number
      end: number
      style: any
    }> = []

    // 基础样式
    const baseStyle = {
      fontSize: textNode.style?.fontSize,
      fontName: {
        family: textNode.style?.fontFamily,
        style: textNode.style?.fontPostScriptName || '',
      },
      fontWeight: textNode.style?.fontWeight,
      textDecoration: textNode.style?.textDecoration,
      textCase: textNode.style?.textCase,
      fills: textNode.fills,
      letterSpacing: textNode.style?.letterSpacing,
      lineHeight: textNode.style?.lineHeightPx,
      hyperlink: textNode.hyperlink || null,
    }

    // 使用 characterStyleOverrides 和 styleOverrideTable 处理样式
    const characterStyleOverrides = textNode.characterStyleOverrides || []
    const styleOverrideTable = textNode.styleOverrideTable || {}

    if (characterStyleOverrides.length > 0) {
      let currentStart = 0
      let currentStyleId = characterStyleOverrides[0]

      for (let i = 1; i <= textNode.characters.length; i++) {
        const nextStyleId = characterStyleOverrides[i]
        // 当样式改变或到达文本末尾时创建新段落
        if (i === textNode.characters.length || nextStyleId !== currentStyleId) {
          const style = currentStyleId ? styleOverrideTable[currentStyleId] : {}

          segments.push({
            characters: textNode.characters.slice(currentStart, i),
            start: currentStart,
            end: i,
            style: {
              ...baseStyle,
              ...style,
            },
          })

          if (i < textNode.characters.length) {
            currentStart = i
            currentStyleId = nextStyleId
          }
        }
      }
    } else {
      // 无样式重写时使用基础样式
      segments.push({
        characters: textNode.characters,
        start: 0,
        end: textNode.characters.length,
        style: baseStyle,
      })
    }

    const figmaTextDecorationToCssMap = {
      STRIKETHROUGH: 'line-through',
      UNDERLINE: 'underline',
      NONE: 'normal',
    } as const

    const figmaTextCaseToCssTextTransformMap = {
      ORIGINAL: 'none',
      SMALL_CAPS: 'none',
      SMALL_CAPS_FORCED: 'none',
      UPPER: 'uppercase',
      LOWER: 'lowercase',
      TITLE: 'capitalize',
    } as const

    return segments.map(segment => {
      const {style} = segment

      // 处理文本颜色
      const rgba = getRgbaFromPaints(segment.style.fills)
      const color = rgba ? rgbaToString(rgba) : ''

      // console.log('getTextColor', color)

      return {
        ...segment,
        fontName: {
          family: style.fontName.family,
          style: style.fontName.style,
        },
        fontFamily: style.fontName.family,
        color: color,
        fontSize: style.fontSize,
        fontWeight: style.fontWeight,
        letterSpacing: style.letterSpacing,
        lineHeight: style.lineHeight,
        listType: style.listType || undefined,
        indentation: style.indentation || 0,
        textDecoration: figmaTextDecorationToCssMap[style.textDecoration || 'NONE'],
        textTransform: figmaTextCaseToCssTextTransformMap[style.textCase || 'ORIGINAL'],
        fills: style.fills,
        href: style.hyperlink?.type === 'URL' ? style.hyperlink.value : undefined,
        style: style,
      } as StyledTextSegment
    })
  }

  initParagraphSpacing() {
    // 检查是否为文本节点
    if (this.node?.type !== 'TEXT') {
      return
    }

    // 从 style 中获取 paragraphSpacing
    const textNode = this.node
    if (textNode.style?.paragraphSpacing !== undefined) {
      this.textInfo.paragraphSpacing = textNode.style.paragraphSpacing
    } else {
      // 如果没有段落间距，设置为默认值 0
      this.textInfo.paragraphSpacing = 0
    }
    if (textNode.lineIndentations[0] !== 0) {
      this.textInfo.listSpacing = textNode.lineIndentations[0]
    } else {
      // 如果没有段落间距，设置为默认值 0
      this.textInfo.listSpacing = 0
    }
  }

  getFillsByFigmaNode() {
    // todo: 类型待优化
    if (this.node && (this.node as FrameNode).fills !== undefined && !isMixed((this.node as FrameNode).fills)) {
      return (this.node as FrameNode).fills
    }
    return []
  }

  initFills() {
    this.fills = this.getFillsByFigmaNode()
  }
  async export() {
    const node = this.node as Node & {
      imageUrl?: string
    }

    if (!node.imageUrl) {
      return ''
    }

    try {
      const response = await fetch(node.imageUrl)
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.statusText}`)
      }

      // 获取图片数据
      const buffer = await response.arrayBuffer()
      const bytes = new Uint8Array(buffer)

      // 原生方法转换为 base64
      const base64String = btoa(
        Array.from(bytes)
          .map(byte => String.fromCharCode(byte))
          .join(''),
      )

      // 确定 MIME 类型
      const mimeType = response.headers.get('content-type') || 'image/png'

      // 返回完整的 data URL
      return `data:${mimeType};base64,${base64String}`
    } catch (error) {
      console.error('Error exporting image:', error)
      return ''
    }
  }
}
