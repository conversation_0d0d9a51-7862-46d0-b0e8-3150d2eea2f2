import type {Paint} from '@figma/rest-api-spec'

export type Coordinate = {
  x: number
  y: number
}

export type Attributes = {
  [type: string]: string
}

export enum ExportFormat {
  SVG = 'SVG',
  PNG = 'PNG',
  JPG = 'JPG',
}

export type BoxCoordinates = {
  width?: number
  height?: number
  leftTop: Coordinate
  leftBot: Coordinate
  rightTop: Coordinate
  rightBot: Coordinate
}

export interface Node {
  getRenderingBoundsCoordinates(): BoxCoordinates
  getAbsoluteBoundingBoxCoordinates(): BoxCoordinates
  getOriginalId(): string
  getOriginalName(): string
  getCssAttributes(): Attributes
  getPositionalCssAttributes(): Attributes
  getFills(): Paint[] | undefined
  export(exportFormat: ExportFormat): Promise<string>
}

export interface StyledTextSegment {
  characters: string
  start: number
  end: number
  fontName: {
    family: string
    style: string
  }
  fontSize: number
  fontFamily: string
  fontWeight: number
  textDecoration: 'normal' | 'line-through' | 'underline'
  textTransform: 'none' | 'uppercase' | 'lowercase' | 'capitalize'
  color: string
  letterSpacing: string
  listType: 'none' | 'ul' | 'ol'
  indentation: number
  href: string
}

export enum PostionalRelationship {
  INCLUDE = 'INCLUDE',
  OVERLAP = 'OVERLAP',
  COMPLETE_OVERLAP = 'COMPLETE_OVERLAP',
  OUTSIDE = 'OUTSIDE',
}

export enum FigmaNodeType {
  GROUP = 'GROUP',
  TEXT = 'TEXT',
  IMAGE = 'IMAGE',
  VECTOR = 'VECTOR',
  ELLIPSE = 'ELLIPSE',
  FRAME = 'FRAME',
  RECTANGLE = 'RECTANGLE',
  INSTANCE = 'INSTANCE',
  STAR = 'STAR',
  SLICE = 'SLICE',
  COMPONENT = 'COMPONENT',
  BOOLEAN_OPERATION = 'BOOLEAN_OPERATION',
  LINE = 'LINE',
  WIDGET = 'WIDGET',
  HIGHLIGHT = 'HIGHLIGHT',
  POLYGON = 'POLYGON',
  STAMP = 'STAMP',
  WASHI_TAPE = 'WASHI_TAPE',
  STICKY = 'STICKY',
}

export enum NodeType {
  GROUP = 'GROUP',
  VISIBLE = 'VISIBLE',
  TEXT = 'TEXT',
  IMAGE = 'IMAGE',
  IMAGEBACKGROUND = 'IMAGEBACKGROUND', // rn 专用的背景类型节点
  FlatList = 'FlatList', // rn 专用生成 FlatList
  Gradient = 'Gradient', // rn 专用生成 LinearGradient组件
}

export const EXPORTABLE_NODE_TYPES: string[] = [
  FigmaNodeType.ELLIPSE,
  FigmaNodeType.VECTOR,
  FigmaNodeType.IMAGE,
  FigmaNodeType.INSTANCE,
  FigmaNodeType.GROUP,
  FigmaNodeType.STAR,
  FigmaNodeType.FRAME,
  FigmaNodeType.RECTANGLE,
  FigmaNodeType.BOOLEAN_OPERATION,
]

export const VECTOR_NODE_TYPES: string[] = [
  FigmaNodeType.ELLIPSE,
  FigmaNodeType.VECTOR,
  FigmaNodeType.STAR,
  FigmaNodeType.BOOLEAN_OPERATION,
  FigmaNodeType.RECTANGLE,
]

export type FilterOption = {
  truncateNumbers?: boolean
  zeroValueAllowed?: boolean
  absolutePositioningFilter?: boolean
  marginFilter?: boolean
  excludeBackgroundColor?: boolean
  excludeOpacity?: boolean
  imgPropsFilter?: boolean
}

export type Annotations = {
  [key: string]: any
}

export const VariableFieldMap = {
  height: 'height',
  width: 'width',
  // characters: '',
  // itemSpacing: '',
  // paddingLeft: '',
  // paddingRight: '',
  // paddingTop: '',
  // paddingBottom: '',
  // visible: '',
  topLeftRadius: 'border-top-left-radius',
  topRightRadius: 'border-bottom-left-radius',
  bottomLeftRadius: 'border-top-right-radius',
  bottomRightRadius: 'border-bottom-right-radius',
  // minWidth: '',
  // maxWidth: '',
  // minHeight: '',
  // maxHeight: '',
  // counterAxisSpacing: '',
}

export type PropertyVariable = {
  id: string
  name: string
  codeName: string
  propertyName: string
  propertyValue: string
  componentId?: string
}
