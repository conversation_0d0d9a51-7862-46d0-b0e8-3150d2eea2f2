import type {Option} from '@baidu/f2c-plugin-base/dist/types'
import type {Node} from '@figma/rest-api-spec'
import {convertRestApiNodesToF2CNodes} from './convertNodes'
import {groupNodes} from './f2c/grouping'
import type {JsonAdapter} from './jsonAdapter'
import {RestApiAdapter} from './restApiAdapter'
import {NodeType} from './type'
import Log from './utils/Log'
import {
  checkChildrenImage,
  markConvertedOrder,
  markPregeneratingOrder,
  preprocessNamedNode,
  removeExcessiveParent,
} from './utils/utils'
// import {handleDropAndInnerEffect} from './utils/utils'

export const initAdapters = (figmaNodes: Node[], option: Option) => {
  // figmaNodes = [require('./rest.json').nodes['80:1956'].document]

  let converted = []
  if (option.annotationMode) {
    converted = convertRestApiNodesToF2CNodes(figmaNodes, null, option, 0, 2).nodes || []
  } else {
    // console.log('before convertFigmaNodesToF2CNodes', figmaNodes)
    converted = convertRestApiNodesToF2CNodes(figmaNodes, null, option).nodes || []
    // Log.debug('after convertFigmaNodesToF2CNodes', converted)
    // converted = convertFigmaNodesToF2CNodes(figmaNodes, null, option).nodes || []
  }

  // 处理部分Effect
  const addonNodes = []
  // if (option.analyzeShadow) {
  //   for (let i = 0; i < converted.length; i++) {
  //     handleDropAndInnerEffect(converted[i], addonNodes)
  //   }
  // }
  Log.debug(
    'converted',
    converted[0].children.map(item => [item.name, item.type]),
  )

  // 子元素都是image的节点设置为image
  if (!option.skipGroupingNode) {
    checkChildrenImage(converted)
  }

  Log.debug(
    'checkChildrenImage nodes',
    converted[0].children.map(item => [item.name, item.type]),
  )

  for (let i = 0; i < converted.length; i++) {
    markConvertedOrder(converted[i], '', i)
  }

  // for (let i = 0; i < converted.length; i++) {
  //   initReactions(converted[i])
  // }

  // console.log('initReactions nodes', converted)

  // console.log(serverArgs);
  const startingNode: JsonAdapter =
    converted.length > 1 ? new RestApiAdapter(null, converted, NodeType.VISIBLE) : converted[0]

  // console.log('before groupNodes', startingNode)
  if (!option.absoluteLayout && !option.skipGroupingNode) {
    groupNodes(startingNode)
  }
  Log.debug(
    'after groupNodes',
    startingNode.children[0]?.children.map(item => [item.name, item.type]),
  )
  // instantiateRegistries(option, figmaNodes[0])

  // if (!option.isDslMode || !['react', 'rn', 'vue', 'talos'].includes(option.uiFramework)) {
  //   console.log('[dsl] 预置数据模式打开', option.uiFramework)

  let afterRemoveNode = startingNode
  // console.log('after addAdditionalCssAttributesToNodes', startingNode)
  if (!option.reserveExcessiveNode) {
    afterRemoveNode = removeExcessiveParent(startingNode)
  }
  Log.debug(
    'after removeBgParent',
    afterRemoveNode.children.map(item => [item.name, item.type]),
  )

  markPregeneratingOrder(afterRemoveNode, '', 0)

  // console.log('pregeneratingOrder nodes len:', converted.length, option)

  // if (!option.isDslMode) {
  //   registerReactComponents(afterRemoveNode)
  // }
  const analyticalNode = afterRemoveNode
  preprocessNamedNode(analyticalNode, false)
  return {analyticalNode, addonNodes}
}
