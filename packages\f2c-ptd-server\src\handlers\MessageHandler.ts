import {
  type Join<PERSON><PERSON>nelMessage,
  type ChatMessage,
  type <PERSON><PERSON>ommandMessage,
  type SystemMessage,
  type ErrorMessage,
  type ResponseMessage,
  isJoinMessage,
  isChatMessage,
  isMCPCommandMessage,
} from "../types/messages";
import { ChannelManager } from "../managers/ChannelManager";
import { logger } from "../log/logger"

export interface MessageHandlerOptions {
  channelManager: ChannelManager;
  onMCPCommand?: (ws: WebSocket, message: MCPCommandMessage) => Promise<void>;
  onSystemMessage?: (ws: WebSocket, message: SystemMessage) => Promise<void>;
}

export class MessageHandler {
  private channelManager: ChannelManager;
  private onMCPCommand?: (
    ws: WebSocket,
    message: MCPCommandMessage
  ) => Promise<void>;
  private onSystemMessage?: (
    ws: WebSocket,
    message: SystemMessage
  ) => Promise<void>;

  constructor(options: MessageHandlerOptions) {
    this.channelManager = options.channelManager;
    this.onMCPCommand = options.onMCPCommand;
    this.onSystemMessage = options.onSystemMessage;
  }

  /**
   * 处理接收到的消息
   */
  async handleMessage(ws: WebSocket, data: any): Promise<void> {
    try {
      // 基础验证
      if (!data) {
        await this.sendError(ws, "Invalid message format", "INVALID_FORMAT");
        return;
      }

      // 根据消息类型分发处理
      if (isJoinMessage(data)) {
        await this.handleJoinMessage(ws, data);
      } else if (isChatMessage(data)) {
        await this.handleChatMessage(ws, data);
      } else if (isMCPCommandMessage(data)) {
        await this.handleMCPCommand(ws, data);
      } else {
        await this.sendError(
          ws,
          `Unknown message type: ${data.type}`,
          "UNKNOWN_TYPE"
        );
      }
    } catch (error) {
      logger.error("处理消息时发生错误:", error);
      await this.sendError(ws, "Internal server error", "INTERNAL_ERROR");
    }
  }

  /**
   * 处理加入频道消息
   */
  private async handleJoinMessage(
    ws: WebSocket,
    message: JoinChannelMessage
  ): Promise<void> {
    try {
      const success = this.channelManager.joinChannel(ws, message);

      if (success) {
        await this.sendResponse(ws, {
          type: "system",
          message: {
            id: message.id,
            result: "Connected to channel: " + message.channel,
          },
          channel: message.channel,
        });
      } else {
        await this.sendError(ws, "Failed to join channel", "JOIN_FAILED");
      }
    } catch (error) {
      logger.error("处理加入频道消息失败:", error);
      await this.sendError(ws, "Failed to process join request", "JOIN_ERROR");
    }
  }

  /**
   * 处理聊天消息
   */
  private async handleChatMessage(
    ws: WebSocket,
    message: ChatMessage
  ): Promise<void> {
    try {
      // 验证频道
      if (!message.channel) {
        await this.sendError(ws, "Channel is required", "MISSING_CHANNEL");
        return;
      }

      // 广播消息到频道
      const broadcastMessage: ChatMessage = {
        ...message,
        timestamp: Date.now(),
      };

      const sentCount = this.channelManager.broadcastToChannel(
        message.channel,
        broadcastMessage
      );

      logger.info(
        `聊天消息已广播到频道 ${message.channel}，发送给 ${sentCount} 个客户端`
      );
    } catch (error) {
      logger.error("处理聊天消息失败:", error);
      await this.sendError(ws, "Failed to process chat message", "CHAT_ERROR");
    }
  }

  /**
   * 处理MCP命令消息
   */
  private async handleMCPCommand(
    ws: WebSocket,
    message: MCPCommandMessage
  ): Promise<void> {
    try {
      // 验证频道
      if (!message.channel) {
        await this.sendError(
          ws,
          "Channel is required for MCP commands",
          "MISSING_CHANNEL"
        );
        return;
      }

      // 验证命令
      if (!message.message.command) {
        await this.sendError(ws, "Command is required", "MISSING_COMMAND");
        return;
      }

      logger.info(
        `收到MCP命令: ${message.message.command} 在频道 ${message.channel}`
      );

      // 调用外部MCP命令处理器
      if (this.onMCPCommand) {
        await this.onMCPCommand(ws, message);
      } else {
        // 默认行为：广播到频道
        const broadcastMessage: MCPCommandMessage = {
          ...message,
          timestamp: Date.now(),
        };

        const sentCount = this.channelManager.broadcastToChannel(
          message.channel,
          broadcastMessage
        );

        logger.info(
          `MCP命令已广播到频道 ${message.channel}，发送给 ${sentCount} 个客户端`
        );
      }
    } catch (error) {
      logger.error("处理MCP命令失败:", error);
      await this.sendError(ws, "Failed to process MCP command", "MCP_ERROR");
    }
  }

  /**
   * 发送错误消息
   */
  private async sendError(
    ws: WebSocket,
    error: string,
    code?: string
  ): Promise<void> {
    if (ws.readyState === WebSocket.OPEN) {
      const errorMessage: ErrorMessage = {
        type: "error",
        error,
        code,
        timestamp: Date.now(),
      };

      try {
        ws.send(JSON.stringify(errorMessage));
      } catch (sendError) {
        logger.error("发送错误消息失败:", sendError);
      }
    }
  }

  /**
   * 发送响应消息
   */
  private async sendResponse(
    ws: WebSocket,
    response: ResponseMessage
  ): Promise<void> {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(response));
      } catch (error) {
        logger.error("发送响应消息失败:", error);
      }
    }
  }

  /**
   * 发送系统消息到指定频道
   */
  public sendSystemMessage(
    channel: string,
    message: string,
    level: "info" | "warning" | "error" = "info"
  ): void {
    const systemMessage: SystemMessage = {
      type: "system",
      message,
      level,
      timestamp: Date.now(),
    };

    this.channelManager.broadcastToChannel(channel, systemMessage);
  }

  /**
   * 获取处理器统计信息
   */
  public getStats() {
    return {
      channelStats: this.channelManager.getStats(),
      timestamp: Date.now(),
    };
  }
}
