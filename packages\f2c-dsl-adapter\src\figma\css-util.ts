import type {UiFramework} from '@baidu/f2c-plugin-base/dist/types/constants'
import type {Effect, GradientPaint, Node, Paint, RGBA, RectangleNode, SolidPaint, TextNode} from '@figma/rest-api-spec'
import type {Attributes, Coordinate} from './type'
import {isMixed} from './util'
import Log from './utils/Log'
import {colorToStringWithOpacity, rgbaToString} from './utils/color-utils'
import {convertFigmaBackgroundToCss} from './utils/convertFigmaBackgroundToCss'
import {getFloat} from './utils/format-number-utils'

// 辅助函数 - 检查属性是否为混合值
// function isMixed(value: any): boolean {
//   return Array.isArray(value) && value.some(v => v !== value[0])
// }

// 颜色混合函数保持不变
function blendColors(color1: RGBA, color2: RGBA) {
  const a = 1 - (1 - color2.a) * (1 - color1.a)
  const r = (color2.r * color2.a) / a + (color1.r * color1.a * (1 - color2.a)) / a
  const g = (color2.g * color2.a) / a + (color1.g * color1.a * (1 - color2.a)) / a
  const b = (color2.b * color2.a) / a + (color1.b * color1.a * (1 - color2.a)) / a
  return {r, g, b, a} as RGBA
}

// 重写 getRgbaFromPaints
export function getRgbaFromPaints(paints: Paint[], opacity?: number) {
  const solidPaints = paints.filter(paint => paint.type === 'SOLID')

  if (solidPaints.length === 0) {
    const gradientPaint = paints.filter(paint => /GRADIENT_/.test(paint.type)) as any
    if (gradientPaint.length > 0 && gradientPaint[0].gradientStops?.length > 0) {
      const color = gradientPaint[0].gradientStops[0].color
      return opacity !== undefined ? {...color, a: opacity} : color
    }
    return {r: 1, g: 1, b: 1, a: opacity ?? 1}
  }

  // REST API 中的颜色值已经是0-1范围
  const firstPaint = solidPaints[0]
  return {
    r: firstPaint.color.r,
    g: firstPaint.color.g,
    b: firstPaint.color.b,
    a: opacity ?? (firstPaint.color.a || 1),
  }
}

// 重写行高转换
export const figmaLineHeightToCssString = (
  style: TextNode['style'],
  fontSize: number,
  fontFamily: string,
  uiFramework?: UiFramework,
) => {
  // REST API 中使用 lineHeightUnit
  switch (style.lineHeightUnit as any) {
    case 'AUTO':
      if (uiFramework === UiFramework.rn) {
        return `${fontSize * 1.2}px`
      }
      if (fontFamily.includes('PingFang SC')) {
        return `${(fontSize * 1.4).toFixed(1)}px`
      }
      return 'normal'
    case 'PERCENT':
      return `${style.lineHeightPercent === 0 ? 130 : getFloat(style.lineHeightPercent)}%`
    case 'PIXELS':
      return `${style.lineHeightPx}px`
    default:
      return 'normal'
  }
}

// 重写字间距转换
export const figmaLetterSpacingToCssString = (style: TextNode['style']) => {
  if (style.letterSpacing === 0) {
    return 'normal'
  }

  // REST API 中直接使用像素值
  return `${style.letterSpacing}px`
}

// 重写字体名称转换
export const figmaFontNameToCssString = (style: TextNode['style']) => {
  return `'${style.fontFamily}'`
}

// 重写阴影检测
export const hasShadow = (node: Node) => {
  if (!('effects' in node)) return false

  return (
    node.effects?.some(
      effect =>
        effect.visible !== false &&
        (effect.type === 'DROP_SHADOW' ||
          effect.type === 'INNER_SHADOW' ||
          effect.type === 'LAYER_BLUR' ||
          effect.type === 'BACKGROUND_BLUR'),
    ) || false
  )
}

export const hasOtherShadow = (node: Node) => {
  // 类型守卫判断是否有 effects 属性
  if (!('effects' in node)) {
    return false
  }

  const effects = node.effects
  if (!effects || !Array.isArray(effects)) {
    return false
  }

  // 过滤出可见的模糊效果
  const blurEffects = effects.filter(
    (effect: Effect) => effect.visible !== false && (effect.type === 'LAYER_BLUR' || effect.type === 'BACKGROUND_BLUR'),
  )

  return blurEffects.length > 0
}
// 重写文本颜色获取
export const getTextColor = (node: Node, attributes: Attributes = {}) => {
  if (node.type !== 'TEXT' || !node.fills?.length) return undefined

  const fills = node.fills
  if (!isMixed(fills)) {
    const finalColor = getRgbaFromPaints(fills)
    if (finalColor) {
      attributes['color'] = rgbaToString(finalColor)
    }
  } else {
    // REST API 中处理混合文本颜色
    // 使用 styleOverrideTable 中的样式
    const styleOverrides = node.styleOverrideTable || {}
    const defaultStyle = node.style

    // 获取最常用的颜色
    const colorCounts = new Map<string, number>()
    node.characters.split('').forEach((char, index) => {
      const styleId = node.characterStyleOverrides?.[index]
      const style = styleId ? styleOverrides[styleId] : defaultStyle
      if (style?.fills?.length) {
        const color = rgbaToString(getRgbaFromPaints(style.fills))
        colorCounts.set(color, (colorCounts.get(color) || 0) + 1)
      }
    })

    // 找出出现次数最多的颜色
    let maxCount = 0
    let mostCommonColor
    colorCounts.forEach((count, color) => {
      if (count > maxCount) {
        maxCount = count
        mostCommonColor = color
      }
    })

    if (mostCommonColor) {
      attributes['color'] = mostCommonColor
    }
  }

  return attributes.color
}

// ... 其他方法类似改写
// 重点是:
// 1. 使用 REST API 的类型定义
// 2. 处理混合值的新方式
// 3. 适配 REST API 的数据结构
// 处理边框圆角
export function getBorderRadius(node: Node, unit = 'px') {
  if (!('cornerRadius' in node)) return undefined

  // REST API 中使用 cornerRadius 或 rectangleCornerRadii
  if (typeof node.cornerRadius === 'number') {
    // 所有角都使用相同的圆角值
    return `${node.cornerRadius}${unit}`
  }

  // 使用 rectangleCornerRadii 获取每个角的圆角值
  if (node.rectangleCornerRadii?.length === 4) {
    const [topLeft, topRight, bottomRight, bottomLeft] = node.rectangleCornerRadii

    // 判断是否所有角的圆角值都相同
    if (topLeft === topRight && topRight === bottomRight && bottomRight === bottomLeft) {
      return `${topLeft}${unit}`
    }

    // 返回四个角不同的圆角值
    return `${topLeft}${unit} ${topRight}${unit} ${bottomRight}${unit} ${bottomLeft}${unit}`
  }

  return undefined
}

// 分析描边颜色
export function analyseStrokeColor(node: Node) {
  if (!('strokes' in node) || !node.strokes?.length) return undefined

  const formatStrokes = node.strokes.filter(stroke => stroke.visible !== false && stroke.type === 'SOLID')
  if (formatStrokes.length > 0) {
    const colors = formatStrokes.map((stroke: any) => ({
      r: stroke.color.r,
      g: stroke.color.g,
      b: stroke.color.b,
      a: stroke.opacity ?? stroke.color.a,
    }))

    return rgbaToString(colors.reduce(blendColors))
  }
  return undefined
}

// 分析描边宽度// 分析描边宽度
export function analyseStrokeWidth(node: Node, unit = 'px') {
  if (!('strokeWeight' in node) || !node.strokes?.length) return undefined

  // REST API 中只有统一的 strokeWeight
  if (typeof node.strokeWeight === 'number') {
    return `${node.strokeWeight}${unit}`
  }

  return undefined
}

// 获取边框颜色
export function getBorderColor(node: Node) {
  if (node.type === 'TEXT' || !HasBorderWidth(node)) return undefined
  return analyseStrokeColor(node)
}

// 获取文本描边颜色
export function getTextStrokeColor(node: Node) {
  if (node.type !== 'TEXT') return undefined
  return analyseStrokeColor(node)
}

// 获取边框样式
export function getBorderStyle(node: Node & any) {
  if (!('strokes' in node) || !node.strokes?.length || !HasBorderWidth(node)) return undefined

  const formatStrokes = node.strokes.filter(stroke => stroke.visible !== false && stroke.type === 'SOLID')
  if (formatStrokes.length > 0) {
    // REST API 中使用 dashPattern 判断是否为虚线
    return 'dashPattern' in node && node.dashPattern?.length > 0 ? 'dashed' : 'solid'
  }
  return undefined
}

// 检查是否有边框宽度
// 检查是否有边框宽度
export function HasBorderWidth(node: Node) {
  if (!('strokeWeight' in node)) return false

  // REST API 中只有统一的 strokeWeight
  return typeof node.strokeWeight === 'number' && node.strokeWeight !== 0
}

// 字体相关属性获取
export function getFontStyles(node: Node) {
  if (node.type !== 'TEXT') return {}

  const style = node.style
  const characterStyleOverrides = node.characterStyleOverrides || []
  const styleOverrideTable = node.styleOverrideTable || {}

  // 获取默认样式
  const defaultStyles = {
    fontFamily: style?.fontFamily ? `'${style.fontFamily}'` : undefined,
    fontSize: style?.fontSize ? `${style.fontSize}px` : undefined,
    fontWeight: style?.fontWeight?.toString(),
    lineHeight: style?.lineHeightPx
      ? `${style.lineHeightPx}px`
      : style?.lineHeightPercent
        ? `${style.lineHeightPercent}%`
        : 'normal',
    color: style?.fills?.[0]?.type === 'SOLID' ? rgbaToString(getRgbaFromPaints(style.fills)) : undefined,
  }

  // 如果没有样式重写，直接返回默认样式
  if (!characterStyleOverrides.length) {
    return defaultStyles
  }

  // 统计最常用的样式
  const styleCounts = new Map()
  characterStyleOverrides.forEach((styleId, index) => {
    if (styleId) {
      const override = styleOverrideTable[styleId]
      const key = JSON.stringify(override)
      styleCounts.set(key, (styleCounts.get(key) || 0) + 1)
    }
  })

  // 找出最常用的样式
  let mostCommonStyle = defaultStyles
  let maxCount = 0

  for (const [key, count] of styleCounts) {
    if (count > maxCount) {
      maxCount = count
      const override = JSON.parse(key)
      mostCommonStyle = {
        ...defaultStyles,
        ...override,
      }
    }
  }

  return mostCommonStyle
}

// 其他辅助函数保持不变
export const setTextOverflow = () => ({
  overflow: 'hidden',
  'text-overflow': 'ellipsis',
  'word-wrap': 'normal',
  'white-space': 'nowrap',
})

// 数学计算相关函数保持不变
function round(number, precision = 0) {
  if (precision == 0) {
    return Math.round(number)
  }
  const factor = 10 ** precision
  return Math.round(+number * factor) / factor
}

function isEqualToZero(number: number) {
  return Math.abs(number) < 0.01
}

// 获取最常见的字段值
export function getMostCommonFieldInString<T>(
  node: TextNode,
  field: string,
  options: {
    areVariationsEqual?: (v1: any, v2: any) => boolean
    variationModifier?: (v: any) => any | null
  } = {},
): any {
  const {areVariationsEqual, variationModifier} = options

  // REST API 使用 characterStyleOverrides 和 styleOverrideTable
  const characterStyleOverrides = node.characterStyleOverrides || []
  const styleOverrideTable = node.styleOverrideTable || {}
  const defaultStyle = node.style || {}

  // 统计字段值的出现次数
  const fieldCounts = new Map()

  characterStyleOverrides.forEach((styleId, index) => {
    const style = styleId ? styleOverrideTable[styleId] : defaultStyle
    const value = style[field]

    if (value === null || value === undefined) return

    const modifiedValue = variationModifier ? variationModifier(value) : value
    if (modifiedValue === null) return

    if (areVariationsEqual) {
      for (const [existingValue, count] of fieldCounts) {
        if (areVariationsEqual(modifiedValue, existingValue)) {
          fieldCounts.set(existingValue, count + 1)
          return
        }
      }
      fieldCounts.set(modifiedValue, 1)
    } else {
      fieldCounts.set(modifiedValue, (fieldCounts.get(modifiedValue) || 0) + 1)
    }
  })

  // 找出出现次数最多的值
  let mostCommonValue
  let maxCount = 0
  for (const [value, count] of fieldCounts) {
    if (count > maxCount) {
      maxCount = count
      mostCommonValue = value
    }
  }

  return mostCommonValue || defaultStyle[field]
}

// 字体样式获取相关函数
export function getTextStyles(node: Node) {
  if (node.type !== 'TEXT') return {}

  const style = node.style || {}
  return {
    fontSize: style.fontSize ? `${style.fontSize}px` : undefined,
    fontFamily: style.fontFamily ? `'${style.fontFamily}'` : undefined,
    fontWeight: style.fontWeight?.toString(),
    letterSpacing: style.letterSpacing
      ? style.letterSpacing === 0
        ? 'normal'
        : `${style.letterSpacing}px`
      : undefined,
    lineHeight: style.lineHeightPx
      ? `${style.lineHeightPx}px`
      : style.lineHeightPercent
        ? `${style.lineHeightPercent}%`
        : 'normal',
  }
}

// 坐标变换相关函数
export function applyMatrixToPoint(matrix: number[][], point: number[]): number[] {
  return [
    matrix[0][0] * point[0] + matrix[0][1] * point[1] + matrix[0][2],
    matrix[1][0] * point[0] + matrix[1][1] * point[1] + matrix[1][2],
  ]
}

// 检查节点类型
export function isTextNode(node: Node): boolean {
  return node.type === 'TEXT'
}

export function isFrameNode(node: Node): boolean {
  return node.type === 'FRAME'
}

export function isGroupNode(node: Node): boolean {
  return node.type === 'GROUP'
}
export const isMask = (figmaNode: RectangleNode) => {
  return figmaNode.isMask
}
// 添加盒子阴影效果
export function addBoxShadowByEffect(node: Node, attributes: Attributes = {}) {
  if (!('effects' in node) || !node.effects?.length) return

  const shadowStrings = node.effects
    .filter(effect => effect.visible !== false && (effect.type === 'DROP_SHADOW' || effect.type === 'INNER_SHADOW'))
    .map(effect => {
      const {offset, radius, spread, color}: any = effect
      const shadowString = `${offset.x}px ${offset.y}px ${radius}px ${spread ?? 0}px ${rgbaToString(color)}`
      return effect.type === 'INNER_SHADOW' ? 'inset ' + shadowString : shadowString
    })

  if (shadowStrings.length) {
    attributes['box-shadow'] = shadowStrings.join(', ')
  }
}

// 添加文本阴影效果
export function addTextShadowByEffect(effects: Effect[] = []): string {
  const shadowStrings = effects
    .filter(effect => effect.visible && effect.type === 'DROP_SHADOW')
    .map(effect => {
      const {offset, radius, color}: any = effect
      return `${offset.x}px ${offset.y}px ${radius}px ${rgbaToString(color)}`
    })

  return shadowStrings.length ? shadowStrings.join(', ') : 'none'
}

// 检查是否有投影和内阴影
export function hasDropAndInnerShadow(node: Node): boolean {
  if (!('effects' in node)) return false

  return (
    node.effects?.some(
      effect => effect.visible !== false && (effect.type === 'DROP_SHADOW' || effect.type === 'INNER_SHADOW'),
    ) || false
  )
}

// 添加投影 CSS 属性
export function addDropShadowCssProperty(node: Node, attributes: Attributes = {}) {
  if (!('effects' in node)) return

  const dropShadowStrings = node.effects
    ?.filter(effect => effect.visible !== false && (effect.type === 'DROP_SHADOW' || effect.type === 'INNER_SHADOW'))
    .map(effect => {
      const {offset, radius, spread, color}: any = effect
      const shadowString = `${offset.x}px ${offset.y}px ${radius}px ${spread ?? 0}px ${rgbaToString(color)}`
      return effect.type === 'INNER_SHADOW' ? 'inset ' + shadowString : shadowString
    })

  if (dropShadowStrings?.length) {
    attributes['box-shadow'] = dropShadowStrings.join(', ')
  }
}

// 获取边框宽度
export function getBorderWidth(node: Node, unit = 'px'): string | undefined {
  if (!('strokeWeight' in node) || !node.strokes?.length) return undefined

  // REST API 中只有统一的 strokeWeight
  return typeof node.strokeWeight === 'number' ? `${node.strokeWeight}${unit}` : undefined
}
function calculateGradientStopsAndAngle(paint: GradientPaint, nodeWidth: number, nodeHeight: number) {
  const {gradientHandlePositions, gradientStops} = paint

  // 计算渐变线的起点和终点
  const glPoints = {
    start: {
      x: gradientHandlePositions[0].x * nodeWidth,
      y: gradientHandlePositions[0].y * nodeHeight,
    },
    end: {
      x: gradientHandlePositions[1].x * nodeWidth,
      y: gradientHandlePositions[1].y * nodeHeight,
    },
  }

  // 计算渐变线的方向向量
  const glDx = glPoints.end.x - glPoints.start.x
  const glDy = glPoints.end.y - glPoints.start.y

  // 计算角度
  const deg = Math.round((Math.atan2(glDy, glDx) * 180) / Math.PI)

  // 确定比例线的起点和终点
  let plPoints = {start: {x: 0, y: 0}, end: {x: nodeWidth, y: nodeHeight}}
  if ((deg > 90 && deg <= 180) || (deg < -180 && deg >= -270)) {
    plPoints = {start: {x: nodeWidth, y: 0}, end: {x: 0, y: nodeHeight}}
  } else if ((deg > 180 && deg <= 270) || (deg < -90 && deg >= -180)) {
    plPoints = {start: {x: nodeWidth, y: nodeHeight}, end: {x: 0, y: 0}}
  } else if ((deg > 270 && deg <= 360) || (deg < 0 && deg >= -90)) {
    plPoints = {start: {x: 0, y: nodeHeight}, end: {x: nodeWidth, y: 0}}
  }

  // 计算交点
  const intersectionPoints = {
    start: calculateIntersectionPoint(glDx, glDy, glPoints.start, plPoints.start),
    end: calculateIntersectionPoint(glDx, glDy, glPoints.start, plPoints.end),
  }

  // 计算新的渐变停止点
  const newGradientStops = gradientStops.map(stop => {
    const p = {
      x: glPoints.start.x + stop.position * glDx,
      y: glPoints.start.y + stop.position * glDy,
    }
    const position = calculateDistanceRatio(intersectionPoints.start, intersectionPoints.end, p)
    return {
      color: stop.color,
      position,
    }
  })

  return {
    deg: deg + 90, // 转换为 CSS 角度
    gradientStops: newGradientStops,
  }
}

// 获取填充颜色
export const getFillsColor = (node: Node, attributes?: Attributes) => {
  if (!('fills' in node) || !node.fills?.length) {
    return undefined
  }

  const fills = node.fills

  // 处理纯色背景
  const solidPaints = fills.filter(fill => fill.visible !== false && fill.type === 'SOLID') as SolidPaint[]
  if (solidPaints.length > 0) {
    let bgColor = undefined
    if (solidPaints.length > 0) {
      const colors = solidPaints.map(paint => ({
        r: paint.color.r,
        g: paint.color.g,
        b: paint.color.b,
        a: paint.opacity || 1,
      }))
      // Log.debug('colors', JSON.stringify(colors))
      bgColor = rgbaToString(colors.reduce(blendColors))
    }
    // Log.debug('bgColor', bgColor)
    if (bgColor) {
      attributes['background'] = bgColor
    }
    return bgColor
  }

  // 处理线性渐变
  const gradientLinearPaints = fills.filter(
    fill => fill.visible !== false && fill.type === 'GRADIENT_LINEAR',
  ) as GradientPaint[]

  if (gradientLinearPaints.length > 0) {
    const gradientLinears = gradientLinearPaints.reverse().map(paint => {
      const {deg, gradientStops} = calculateGradientStopsAndAngle(
        paint,
        node.absoluteBoundingBox?.width || 0,
        node.absoluteBoundingBox?.height || 0,
      )

      // 组合最终的渐变信息
      const colors = gradientStops.map(
        stop => `${rgbaToString(stop.color, paint.opacity)} ${round(stop.position, 4) * 100}%`,
      )

      return `linear-gradient(${deg}deg, ${colors.join(', ')})`
    })

    const backgroundStr = gradientLinears.join(', ')
    if (attributes) {
      attributes['background'] = backgroundStr
    }

    return backgroundStr
  }

  return undefined
}
export function calculateDistanceRatio(S, E, P) {
  const x1 = S.x
  const y1 = S.y
  const x2 = E.x
  const y2 = E.y

  const distanceSP = Math.sqrt((P.x - x1) ** 2 + (P.y - y1) ** 2)
  const distanceSE = Math.sqrt((E.x - x1) ** 2 + (E.y - y1) ** 2)

  const distanceRatio = (distanceSP / distanceSE) * Math.sign((x2 - x1) * (P.x - x1) + (y2 - y1) * (P.y - y1))

  return round(distanceRatio, 4)
}

export const getGradientCssString = (node: Node & any, gradientLinearPaint: GradientPaint) => {
  const {gradientStops, gradientHandlePositions} = gradientLinearPaint

  // 计算渐变变换矩阵
  const w = node.absoluteBoundingBox?.width || 0
  const h = node.absoluteBoundingBox?.height || 0
  const [start, end] = gradientHandlePositions

  // 计算渐变向量
  const dx = (end.x - start.x) * w
  const dy = (end.y - start.y) * h

  // 计算旋转角度
  const angle = Math.atan2(dy, dx)
  const rotationTruthy = Math.cos(angle) > 0 ? 1 : -1
  const rotation = (angle / Math.PI) * 180

  // Web 处理
  const colorList = gradientStops.map(stop => {
    const color = colorToStringWithOpacity(stop.color, Number(Math.fround(stop.color.a).toFixed(2)))
    return `${color} ${stop.position * 100}%`
  })

  const adjustedAngle = ((rotation * rotationTruthy + 90 + 360) % 360).toFixed(2)
  const gradientString = `linear-gradient(${adjustedAngle}deg, ${colorList.join(', ')})`
  return {gradientString, colorList}
}

export function addGradientAttrs(node: Node, attributes?: Attributes): string[] {
  if (!('fills' in node) || !node.fills?.length) {
    return []
  }

  const fills = node.fills
  // const gradientColors: string[] = []

  // 处理纯色背景
  const solidPaint = fills.find(fill => fill.visible !== false && fill.type === 'SOLID') as SolidPaint
  if (solidPaint) {
    attributes['background-color'] = colorToStringWithOpacity(solidPaint.color, solidPaint.opacity)
  }

  // 处理线性渐变
  const gradientLinearPaint = fills.find(
    fill => fill.visible !== false && fill.type === 'GRADIENT_LINEAR',
  ) as GradientPaint

  if (gradientLinearPaint) {
    const {gradientString, colorList} = getGradientCssString(node, gradientLinearPaint)

    attributes['background'] = gradientString
    return colorList.map(item => item.split(' ')[0])
  }

  return []
}
// 计算两条线的交点
function calculateIntersectionPoint(dx: number, dy: number, O: Coordinate, P: Coordinate) {
  if (dy == 0) {
    return {x: P.x, y: O.y}
  } else if (dx == 0) {
    return {x: O.x, y: P.y}
  }
  const M = dy / dx
  if (isEqualToZero(M)) {
    return {x: P.x, y: O.y}
  }
  // 计算斜线L的方程中的截距
  const b = O.y - M * O.x

  // 计算垂直线的斜率
  const perpendicularSlope = -1 / M

  // 计算交点Q的x坐标
  const Xq = (P.y - perpendicularSlope * P.x - b) / (M - perpendicularSlope)

  // 计算交点Q的y坐标
  const Yq = M * Xq + b
  // console.log(`calculateIntersectionPoint dx=${dx},dy=${dy}`, 'O=', O, 'P=', P, '=> Q=', {x: Xq, y: Yq})
  return {x: Xq, y: Yq}
}

// 辅助函数：检查一个值是否在两个值之间
function isBetween(value: number, min: number, max: number): boolean {
  if (min > max) {
    ;[min, max] = [max, min]
  }
  return value >= min - 0.01 && value <= max + 0.01
}
