// 检测是否在扩展环境中
function isExtensionEnvironment(): boolean {
    return typeof chrome !== 'undefined' && Boolean(chrome.runtime && chrome.runtime.id)
}

// 获取浏览器 API
function getBrowserAPI() {
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        return chrome
    }
    if (typeof browser !== 'undefined' && browser.runtime) {
        return browser
    }
    return null
}

// 扩展桥接类，用于在非扩展环境中与扩展通信
export class ExtensionBridge {
    private static instance: ExtensionBridge
    
    static getInstance(): ExtensionBridge {
        if (!this.instance) {
            this.instance = new ExtensionBridge()
        }
        return this.instance
    }
    
    sendMessage(message: any): Promise<any> {
        return new Promise((resolve, reject) => {
            const messageId = Date.now().toString() + Math.random().toString(36)
            
            // 发送消息到 content script
            window.postMessage({
                type: 'TO_EXTENSION',
                payload: message,
                messageId
            }, '*')
            
            // 监听回复
            const handler = (event: MessageEvent) => {
                if (event.data.type === 'FROM_EXTENSION' && 
                    event.data.messageId === messageId) {
                    window.removeEventListener('message', handler)
                    if (event.data.error) {
                        reject(new Error(event.data.error))
                    } else {
                        resolve(event.data.payload)
                    }
                }
            }
            
            window.addEventListener('message', handler)
            
            // 超时处理
            setTimeout(() => {
                window.removeEventListener('message', handler)
                reject(new Error('Message timeout'))
            }, 10000)
        })
    }
}

class Auth {
    static _instance: Auth
    static get impl () {
        if (!this._instance) {
            this._instance = new Auth()
        }
        return this._instance
    }
    constructor() {

    }

    async checkAuthStatus() {
        try {
            if (isExtensionEnvironment()) {
                // 在扩展环境中直接使用 chrome API
                const browserAPI = getBrowserAPI()
                if (browserAPI) {
                    const response = await browserAPI.runtime.sendMessage({
                        type: 'checkAuthStatus'
                    })
                    return response
                }
            } else {
                // 在网页环境中使用消息桥接
                const response = await ExtensionBridge.getInstance().sendMessage({
                    type: 'checkAuthStatus'
                })
                return response
            }
            
            return {
                isAuthenticated: false,
                token: null
            }
        } catch (error) {
            console.error('Auth check error:', error)
            return {
                isAuthenticated: false,
                token: null
            }
        }
    }
    
    async removeAuth() {
        try {
            if (isExtensionEnvironment()) {
                // 在扩展环境中直接使用 chrome API
                const browserAPI = getBrowserAPI()
                if (browserAPI) {
                    const response = await browserAPI.runtime.sendMessage({
                        type: 'removeAuth'
                    })
                    return response
                }
            } else {
                // 在网页环境中使用消息桥接
                const response = await ExtensionBridge.getInstance().sendMessage({
                    type: 'removeAuth'
                })
                return response
            }
            return false
        } catch (error) {
            console.error('Remove auth error:', error)
            return false
        }
    }
    
    async gotoAuth() {
        try {
            if (isExtensionEnvironment()) {
                // 在扩展环境中直接使用 chrome API
                const browserAPI = getBrowserAPI()
                if (browserAPI) {
                    // 查询当前活动标签页
                    const tabs = await browserAPI.tabs.query({active: true, currentWindow: true})
                    const currentTabId = tabs[0]?.id as number
                    
                    if (currentTabId) {
                        // 发送消息给 background 脚本开始认证流程
                        const response = await browserAPI.runtime.sendMessage({
                            type: 'backgroundStartAuth',
                            originalTabId: currentTabId,
                        })
                        console.log('popup backgroundStartAuth res', response)
                    }
                }
            } else {
                // 在网页环境中使用消息桥接
                const response = await ExtensionBridge.getInstance().sendMessage({
                    type: 'gotoAuth'
                })
                console.log('gotoAuth response:', response)
                return response
            }
        } catch (error) {
            console.error('授权请求错误:', error)
        }
    }
    
}
export default Auth