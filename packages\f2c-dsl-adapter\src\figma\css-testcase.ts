import {calculateDistanceRatio} from './css-util'

export function testDistanceRatio() {
  let start = {x: 0, y: 0}
  let end = {x: 2, y: 2}
  console.log('====test 左上=》右下', start, end)
  test(start, end, {x: 0, y: 0}, 0)
  test(start, end, {x: 1, y: 1}, 0.5)
  test(start, end, {x: 2, y: 2}, 1)
  test(start, end, {x: 3, y: 3}, 1.5)
  test(start, end, {x: -1, y: -1}, -0.5)

  start = {x: 2, y: 0}
  end = {x: 0, y: 2}
  console.log('====test 右上=》左下', start, end)
  test(start, end, {x: 2, y: 0}, 0)
  test(start, end, {x: 1, y: 1}, 0.5)
  test(start, end, {x: 0, y: 2}, 1)
  test(start, end, {x: -1, y: 3}, 1.5)
  test(start, end, {x: 3, y: -1}, -0.5)

  start = {x: 2, y: 2}
  end = {x: 0, y: 0}
  console.log('====test 右下=》左下', start, end)
  test(start, end, {x: 0, y: 0}, 1)
  test(start, end, {x: 1, y: 1}, 0.5)
  test(start, end, {x: 2, y: 2}, 0)
  test(start, end, {x: 3, y: 3}, -0.5)
  test(start, end, {x: -1, y: -1}, 1.5)

  start = {x: 0, y: 2}
  end = {x: 2, y: 0}
  console.log('====test 左下=》右上', start, end)
  test(start, end, {x: 2, y: 0}, 1)
  test(start, end, {x: 1, y: 1}, 0.5)
  test(start, end, {x: 0, y: 2}, 0)
  test(start, end, {x: -1, y: 3}, -0.5)
  test(start, end, {x: 3, y: -1}, 1.5)

  console.log('====test===')
  start = {x: 0, y: 0}
  end = {x: 2, y: 0}
  console.log('====test 左=》右', start, end)
  test(start, end, {x: 2, y: 0}, 1)
  test(start, end, {x: 1, y: 0}, 0.5)
  test(start, end, {x: 0, y: 0}, 0)
  test(start, end, {x: -1, y: 0}, -0.5)
  test(start, end, {x: 3, y: 0}, 1.5)

  start = {x: 0, y: 1}
  end = {x: 2, y: 1}
  console.log('====test 左=》右', start, end)
  test(start, end, {x: 2, y: 1}, 1)
  test(start, end, {x: 1, y: 1}, 0.5)
  test(start, end, {x: 0, y: 1}, 0)
  test(start, end, {x: -1, y: 1}, -0.5)
  test(start, end, {x: 3, y: 1}, 1.5)

  start = {x: 0, y: 2}
  end = {x: 2, y: 2}
  console.log('====test 左=》右', start, end)
  test(start, end, {x: 2, y: 2}, 1)
  test(start, end, {x: 1, y: 2}, 0.5)
  test(start, end, {x: 0, y: 2}, 0)
  test(start, end, {x: -1, y: 2}, -0.5)
  test(start, end, {x: 3, y: 2}, 1.5)

  console.log('====test===')
  start = {x: 2, y: 0}
  end = {x: 0, y: 0}
  console.log('====test 右=》左', start, end)
  test(start, end, {x: 2, y: 0}, 0)
  test(start, end, {x: 1, y: 0}, 0.5)
  test(start, end, {x: 0, y: 0}, 1)
  test(start, end, {x: -1, y: 0}, 1.5)
  test(start, end, {x: 3, y: 0}, -0.5)

  start = {x: 2, y: 1}
  end = {x: 0, y: 1}
  console.log('====test 右=》左', start, end)
  test(start, end, {x: 2, y: 1}, 0)
  test(start, end, {x: 1, y: 1}, 0.5)
  test(start, end, {x: 0, y: 1}, 1)
  test(start, end, {x: -1, y: 1}, 1.5)
  test(start, end, {x: 3, y: 1}, -0.5)

  start = {x: 2, y: 2}
  end = {x: 0, y: 2}
  console.log('====test 右=》左', start, end)
  test(start, end, {x: 2, y: 2}, 0)
  test(start, end, {x: 1, y: 2}, 0.5)
  test(start, end, {x: 0, y: 2}, 1)
  test(start, end, {x: -1, y: 2}, 1.5)
  test(start, end, {x: 3, y: 2}, -0.5)

  console.log('====test===')
  start = {x: 0, y: 0}
  end = {x: 0, y: 2}
  console.log('====test 上=》下', start, end)
  test(start, end, {x: 0, y: 0}, 0)
  test(start, end, {x: 0, y: 1}, 0.5)
  test(start, end, {x: 0, y: 2}, 1)
  test(start, end, {x: 0, y: 3}, 1.5)
  test(start, end, {x: 0, y: -1}, -0.5)

  start = {x: 1, y: 0}
  end = {x: 1, y: 2}
  console.log('====test 上=》下', start, end)
  test(start, end, {x: 1, y: 0}, 0)
  test(start, end, {x: 1, y: 1}, 0.5)
  test(start, end, {x: 1, y: 2}, 1)
  test(start, end, {x: 1, y: 3}, 1.5)
  test(start, end, {x: 1, y: -1}, -0.5)

  start = {x: 2, y: 0}
  end = {x: 2, y: 2}
  console.log('====test 上=》下', start, end)
  test(start, end, {x: 2, y: 0}, 0)
  test(start, end, {x: 2, y: 1}, 0.5)
  test(start, end, {x: 2, y: 2}, 1)
  test(start, end, {x: 2, y: 3}, 1.5)
  test(start, end, {x: 2, y: -1}, -0.5)

  console.log('====test===')
  start = {x: 0, y: 2}
  end = {x: 0, y: 0}
  console.log('====test 下=》上', start, end)
  test(start, end, {x: 0, y: 0}, 1)
  test(start, end, {x: 0, y: 1}, 0.5)
  test(start, end, {x: 0, y: 2}, 0)
  test(start, end, {x: 0, y: 3}, -0.5)
  test(start, end, {x: 0, y: -1}, 1.5)

  start = {x: 1, y: 2}
  end = {x: 1, y: 0}
  console.log('====test 下=》上', start, end)
  test(start, end, {x: 1, y: 0}, 1)
  test(start, end, {x: 1, y: 1}, 0.5)
  test(start, end, {x: 1, y: 2}, 0)
  test(start, end, {x: 1, y: 3}, -0.5)
  test(start, end, {x: 1, y: -1}, 1.5)

  start = {x: 2, y: 2}
  end = {x: 2, y: 0}
  console.log('====test 下=》上', start, end)
  test(start, end, {x: 2, y: 0}, 1)
  test(start, end, {x: 2, y: 1}, 0.5)
  test(start, end, {x: 2, y: 2}, 0)
  test(start, end, {x: 2, y: 3}, -0.5)
  test(start, end, {x: 2, y: -1}, 1.5)
}

function test(S, E, P, R) {
  const ret = calculateDistanceRatio(S, E, P)
  const isPass = Math.abs(ret - R) < 0.01
  console[isPass ? 'log' : 'warn']('Ratio', S, E, P, '=>', R, 'test R:', ret, Math.abs(ret - R) < 0.01)
}
