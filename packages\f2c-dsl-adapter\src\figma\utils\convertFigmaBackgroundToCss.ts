import type {Paint} from '@figma/rest-api-spec'
import type {RestApiAdapter} from '../restApiAdapter'

// --- 类型定义 (与之前相同，保持完整性) ---
interface FigmaColor {
  r: number
  g: number
  b: number
  a: number
}
interface GradientStop {
  color: FigmaColor
  position: number
}
interface Vector {
  x: number
  y: number
}
// type Paint = {
//   type: 'SOLID' | 'GRADIENT_LINEAR' | 'GRADIENT_RADIAL' | 'GRADIENT_DIAMOND' | 'GRADIENT_ANGULAR' | 'IMAGE'
//   visible?: boolean
//   opacity?: number
//   blendMode: string
//   color?: FigmaColor
//   gradientHandlePositions?: Vector[]
//   gradientStops?: GradientStop[]
// }

// --- 辅助函数 (部分参照或重用您项目中的逻辑) ---

/**
 * 将 Figma RGBA (0-1) 转换为 CSS rgba 字符串 (0-255)。
 */
function figmaColorToRgbaString(color: FigmaColor, opacity = 1): string {
  const r = Math.round(color.r * 255)
  const g = Math.round(color.g * 255)
  const b = Math.round(color.b * 255)
  const a = color.a * opacity
  return `rgba(${r}, ${g}, ${b}, ${Number.parseFloat(a.toFixed(3))})`
}

/**
 * 混合两种 RGBA 颜色 (参照您项目中 blendColors 的逻辑)。
 */
function blendFigmaColors(
  color1: {color: FigmaColor; opacity: number},
  color2: {color: FigmaColor; opacity: number},
): {color: FigmaColor; opacity: number} {
  const c1a = color1.color.a * color1.opacity
  const c2a = color2.color.a * color2.opacity
  const finalA = 1 - (1 - c2a) * (1 - c1a)
  if (finalA < 1e-6) return {color: {r: 0, g: 0, b: 0, a: 0}, opacity: 1}

  const r = (color2.color.r * c2a + color1.color.r * c1a * (1 - c2a)) / finalA
  const g = (color2.color.g * c2a + color1.color.g * c1a * (1 - c2a)) / finalA
  const b = (color2.color.b * c2a + color1.color.b * c1a * (1 - c2a)) / finalA

  return {color: {r, g, b, a: finalA}, opacity: 1}
}

/**
 * 将 Figma 渐变色标转换为 CSS 颜色断点。
 */
function figmaStopsToCssStrings(stops: GradientStop[], opacity = 1): string[] {
  return stops.map(stop => `${figmaColorToRgbaString(stop.color, opacity)} ${(stop.position * 100).toFixed(2)}%`)
}

/**
 * 将单个 Figma Paint 对象转换为 CSS 背景层字符串。
 * @param paint 单个 Paint 对象
 * @param nodeWidth 节点宽度
 * @param nodeHeight 节点高度
 */

function figmaPaintToCssLayerString(paint: Paint, nodeWidth: number, nodeHeight: number): string | null {
  const opacity = paint.opacity ?? 1

  switch (paint.type) {
    case 'SOLID':
      if (!paint.color) return null
      const colorStr = figmaColorToRgbaString(paint.color, opacity)
      return `linear-gradient(${colorStr}, ${colorStr})`

    case 'GRADIENT_LINEAR':
      if (!paint.gradientHandlePositions || !paint.gradientStops) return null

      // 严格参照您项目中 getGradientCssString 的角度计算逻辑
      const [start, end] = paint.gradientHandlePositions
      const dx = (end.x - start.x) * nodeWidth
      const dy = (end.y - start.y) * nodeHeight
      const angleRad = Math.atan2(dy, dx)
      const rotation = (angleRad / Math.PI) * 180
      // 这一步与您项目中 getGradientCssString 的逻辑可能略有不同，但更符合标准转换
      // CSS 0deg is 'to top', 90deg is 'to right'. A vector from (0,0) to (1,0) is 90deg.
      // atan2(0, 1) is 0 rad. So we need to adjust. The formula (90 - angle) seems more standard.
      // Let's adopt your project's logic if it was available, or use a robust one.
      // A standard robust formula is:
      let cssAngle = Number.parseFloat((90 - rotation).toFixed(2))
      if (cssAngle < 0) cssAngle += 360

      const stops = figmaStopsToCssStrings(paint.gradientStops, opacity).join(', ')
      return `linear-gradient(${cssAngle}deg, ${stops})`

    case 'GRADIENT_RADIAL':
    case 'GRADIENT_DIAMOND': // 将 DIAMOND 作为 RADIAL 的一种特殊情况处理
      if (!paint.gradientHandlePositions || !paint.gradientStops) return null
      const center = paint.gradientHandlePositions[0]
      const centerX = `${(center.x * 100).toFixed(2)}%`
      const centerY = `${(center.y * 100).toFixed(2)}%`
      const radialStops = figmaStopsToCssStrings(paint.gradientStops, opacity).join(', ')
      const shape = paint.type === 'GRADIENT_DIAMOND' ? 'farthest-corner' : 'ellipse'
      return `radial-gradient(${shape} at ${centerX} ${centerY}, ${radialStops})`

    // 其他类型...
    // case 'IMAGE':
    //   adapterNode.addAnnotations('imageRef', paint.imageRef)
    //   return null
    default:
      return null
  }
}

// --- 主函数 ---

/**
 * 将 Figma REST API 返回的 background (Paint 数组) 转换为 CSS 属性对象。
 * 此版本参照了您项目中 getFillsColor 的逻辑。
 * @param background Figma Paint 对象数组
 * @param nodeWidth 节点宽度 (对于渐变计算是必需的)
 * @param nodeHeight 节点高度 (对于渐变计算是必需的)
 * @returns 一个包含 CSS 属性的对象
 */
export function convertFigmaBackgroundToCss(
  background: Paint[],
  nodeWidth: number,
  nodeHeight: number,
): {[key: string]: string} {
  if (!background || background.length === 0) {
    return {}
  }

  const visiblePaints = background.filter(p => p.visible !== false)
  if (visiblePaints.length === 0) {
    return {}
  }

  // 1. 提取所有 SOLID fills 并混合它们，作为最终的 background-color
  const solidPaints = visiblePaints.filter(p => p.type === 'SOLID' && p.color)
  let finalSolidColor: string | undefined

  if (solidPaints.length > 0) {
    const blended = solidPaints.map(p => ({color: p.color!, opacity: p.opacity ?? 1})).reduce(blendFigmaColors) // 使用混合函数
    finalSolidColor = figmaColorToRgbaString(blended.color, blended.opacity)
  }

  // 2. 将所有非 SOLID fills (渐变等) 或所有 fills (如果需要将 SOLID 也作为图层) 转换为背景层
  // 按照 getFillsColor 的逻辑，它似乎将所有层都视为潜在的背景层，并将混合后的 SOLID 作为 `background` 的一部分。
  // 我们这里采取更分离的方式，SOLID 为 background-color, 其他为 background-image。
  const layerPaints = visiblePaints.filter(p => p.type !== 'SOLID')

  if (layerPaints.length === 0) {
    // 如果只有 SOLID fills，只返回 background-color
    return finalSolidColor ? {'background-color': finalSolidColor} : {}
  }

  // 3. 转换图层并构建 CSS
  // 反转数组以匹配 CSS 的上 -> 下顺序
  const reversedLayerPaints = [...layerPaints].reverse()

  const backgroundLayers = reversedLayerPaints
    .map(paint => figmaPaintToCssLayerString(paint, nodeWidth, nodeHeight))
    .filter(Boolean) as string[]

  const blendModes = reversedLayerPaints
    .map(paint => paint.blendMode.toLowerCase().replace('_', '-'))
    .filter(mode => mode !== 'normal')

  const css: {[key: string]: string} = {}

  if (backgroundLayers.length > 0) {
    // 将混合后的 solid color 作为最底层的背景
    const finalBackgroundValue = backgroundLayers.join(', ')
    if (finalSolidColor) {
      css['background-color'] = finalSolidColor
    }
    css['background-image'] = backgroundLayers.join(', ')
  } else if (finalSolidColor) {
    // 只有 solid color
    css['background-color'] = finalSolidColor
  }

  if (blendModes.length > 0 && backgroundLayers.length === blendModes.length) {
    css['background-blend-mode'] = blendModes.join(', ')
  }

  return css
}
