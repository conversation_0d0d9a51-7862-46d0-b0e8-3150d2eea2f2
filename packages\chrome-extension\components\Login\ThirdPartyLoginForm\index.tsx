import { Button } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import loginStore from "@/store/loginStore"
import { zodResolver } from "@hookform/resolvers/zod"
import { Mail, Shield } from "lucide-react"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { z } from "zod"

const formSchema = z.object({
  email: z.string().email({
    message: "请输入有效的邮箱地址",
  }),
  verificationCode: z.string().min(1, {
    message: "请输入验证码",
  }),
})

export function ThirdPartyLoginForm() {
  const [countdown, setCountdown] = useState(0)
  const {setLoginInfoToStorage,getUserInfo} = loginStore.state

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      verificationCode: "",
    },
    mode: "onChange", // 启用实时验证
  })

  useEffect(() => {
    let timer: ReturnType<typeof setTimeout>
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000)
    }
    return () => clearTimeout(timer)
  }, [countdown])

  async function onSubmit(values: z.infer<typeof formSchema>) {
    const response = await fetch('https://figma-plugin.yy.com/figma-plugin/verify/email-code',{
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        email: values.email,
        code: values.verificationCode,
      }),
    })
    if(!response.ok) {
      return toast.error(`网络错误: ${response.statusText}`)
    }
    const res = await response.json()
    if(res.code !== "0" || !res.data) {
      return toast.error(`登录失败: ${res.message}`)
    }

    const {figmaIdentityInfo = '', figmaIdentitySign = ''} = res.data
    setLoginInfoToStorage({figmaIdentityInfo, figmaIdentitySign})
    toast.promise(getUserInfo(figmaIdentityInfo, figmaIdentitySign), {
      loading: '登录中...',
      success: '登录成功!',
      error: '登录失败',
    })
  }

  // 获取表单状态
  const { isSubmitting } = form.formState

  const handleGetVerificationCode = async () => {
    const email = form.getValues("email")
    const emailValidation = formSchema.shape.email.safeParse(email)

    if (!emailValidation.success) {
      form.setError("email", {
        type: "manual",
        message: "请先输入有效的邮箱地址",
      })
      return
    }

    setCountdown(60)
    const response = await fetch('https://figma-plugin.yy.com/figma-plugin/send/email-code', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        email,
      }),
    })
    if(!response.ok) {
      setCountdown(0)
      toast.error(`网络错误: ${response.statusText}, 请稍后重试`)
      return
    }
    const res = await response.json()
    if(res.code !== "0") {
      setCountdown(0)
      toast.error(`发送验证码失败: ${res.message}, 请稍后重试`)
      return
    }
    toast.success('发送验证码成功，请查收')
  }

  const watchEmail = form.watch("email")
  const isValidEmail = formSchema.shape.email.safeParse(watchEmail).success
  const canGetCode = isValidEmail && countdown === 0

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="relative box-border">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input type="email" placeholder="邮箱地址" className="pl-10 h-11 box-border" {...field} />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="verificationCode"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="flex gap-2">
                  <div className="relative flex-1">
                    <Shield className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      type="text"
                      placeholder="验证码"
                      className="pl-10 h-11 box-border"
                      maxLength={6}
                      {...field}
                    />
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleGetVerificationCode}
                    disabled={!canGetCode}
                    className="whitespace-nowrap h-11"
                  >
                    {countdown > 0 ? `${countdown}s` : "获取"}
                  </Button>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          type="submit"
          className="w-full mt-8 h-11 text-base font-semibold"
          disabled={isSubmitting}
        >
          {isSubmitting ? "登录中..." : "登录"}
        </Button>
      </form>
    </Form>
  )
}
