import type { CSSProperties } from 'react'
import UseColorSelect from './useColorSelect'

import Fills from './fills'
interface IFrame {
  fillInfo: Paint[]
  layerInfo: {css?: CSSProperties}
}
const Frame = (props: IFrame) => {
  const {ColorSelect, colorFormat, color} = UseColorSelect()

  return (
    <div className="extContent w-full">
      {ColorSelect}
      <Fills fills={props.fillInfo} color={color} colorFormat={colorFormat} />
    </div>
  )
}
export default Frame
