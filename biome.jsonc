{
  // "$schema": "./node_modules/@biomejs/biome/configuration_schema.json",
  "extends": ["@empjs/biome-config"],
  "linter": {
    "rules": {
      "complexity": {
        "noThisInStatic": "off"
      },
      "suspicious": {
        "noFallthroughSwitchClause": "off",
        "noConfusingVoidType": "off"
      },
      "correctness": {
        "noSwitchDeclarations": "off"
      },
      "style": {
        "useExponentiationOperator": "off",
        "useDefaultParameterLast": "off"
      }
    }
  }
}
