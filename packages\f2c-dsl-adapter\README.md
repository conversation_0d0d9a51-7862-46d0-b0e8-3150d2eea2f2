# RestAPI 解析器开发工作流

## 现场还原
1. 使用api/nodes 复制json到`__test/rest.json`
> 示例： https://f2c-figma-api.yy.com/api/nodes?fileKey=kPM6jD1d4rgIK85mhFmwMS&nodeIds=2351:27&personal_token=figd_rg2T-YfN4vDWwS3ahRBpAlYmF5kI-8qv6vgPaJ1t
2. 跑Debug Test调试任务
2. 在调试控制台，查看Log.info打印的日志
> 为了防止node服务io阻塞，console.log()在构建时要全部去掉，我们可以使用`Log.info()`实现日志打印，绕过构建
```
npm run test
```

## Server联调验证
1. 跑dev或者build 构建解析包

2. 重新运行server，通过api `format=html` 查看还原度