import type {FigmaNodeAdapter} from '@baidu/f2c-plugin-base'

export interface File {
  content: string
  path: string
  isAbsolutePath?: boolean
}
export type ImportedComponentMeta = {
  node: FigmaNodeAdapter
  importPath: string
  componentName: string
  // url?: string
}

export type ComponentAssetMeta = {
  node: SceneNode
  importPath: string
  componentName: string
  // url?: string
}
export type FolderInfo = {
  id: string
  path: string // 生成代码路径
  node: FigmaNodeAdapter // 生成代码节点
  name: string // 组件名称
  module_name: string
  needReturnCode: boolean // 是否需要返回主体代码
}
