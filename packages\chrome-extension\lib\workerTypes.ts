// Worker message types - based on tempad-dev/codegen/worker.ts

export type RequestMessage<T = unknown> = {
  id: number
  payload: T
}

export type ResponseMessage<T = unknown> = {
  id: number
  payload?: T
  error?: unknown
}

export type PendingRequest<T = unknown> = {
  resolve: (result: T) => void
  reject: (reason?: unknown) => void
}

export type WorkerClass = {
  new (...args: any[]): Worker
}
