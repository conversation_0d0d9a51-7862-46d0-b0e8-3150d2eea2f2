import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

/**
 * 合并Tailwind CSS类名的工具函数
 * 自动处理f2c-前缀，确保样式隔离
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * 为Tailwind类名添加f2c-前缀的工具函数
 * 用于确保插件样式不会影响网页样式
 */
export function f2cClass(className: string): string {
  if (!className) return ''
  
  // 分割类名并为每个类名添加前缀
  return className
    .split(' ')
    .map(cls => {
      const trimmed = cls.trim()
      if (!trimmed) return ''
      
      // 如果已经有f2c-前缀，直接返回
      if (trimmed.startsWith('f2c-')) return trimmed
      
      // 处理伪类和修饰符（如 hover:, focus:, md: 等）
      const parts = trimmed.split(':')
      if (parts.length > 1) {
        const modifiers = parts.slice(0, -1)
        const baseClass = parts[parts.length - 1]
        return `${modifiers.join(':')}:f2c-${baseClass}`
      }
      
      // 普通类名直接添加前缀
      return `f2c-${trimmed}`
    })
    .filter(Boolean)
    .join(' ')
}

/**
 * 结合cn和f2cClass的便捷函数
 * 自动添加前缀并合并类名
 */
export function f2cn(...inputs: ClassValue[]): string {
  const merged = cn(...inputs)
  return f2cClass(merged)
}

/**
 * 用于shadcn/ui组件的类名处理函数
 * 确保shadcn/ui组件的样式被正确隔离
 */
export function shadcnCn(...inputs: ClassValue[]): string {
  return f2cn(...inputs)
}
