import {colorToStringWithOpacity} from '@/lib/color-utils'
import '@figma-plugin/helpers'
import {getObjType} from '@/utils/common-utils'
import { fixNum } from '@/utils/format-number-utils'
enum ValueType {
  NumberString = 'numberString',
  String = 'string',
  Function = 'function',
}
export interface IGradientTransformData {
  m00: number
  m01: number
  m02: number
  m10: number
  m11: number
  m12: number
}
class ParseCssUtil {
  constructor() {}
  static valueReg = /^[0-9][0-9a-zA-Z]*$/
  static stringReg = /^[a-zA-Z-]+$/
  static numReg = /[0-9a-zA-Z]*/
  static parseFuncReg = /^([0-9a-zA-Z_+-]*)\((.*)\)$/
  static annotationReg = /\/\*[^*\/]*\*+(?:[^\/*][^*\/]*\*+)*\//g
  static childFuncReg = /((,|\()\s*[0-9a-zA-Z_+-]*)\((.*)\).*(,|\))/
  static dealText(text: string) {
    const type = this.getTextType(text)
    switch (type) {
      case ValueType.NumberString:
        return <span className="text-[#f36300]">{text}</span>
      case ValueType.String:
        return <span className="text-[#246dff]">{text}</span>
      case ValueType.Function:
        const res: any = []
        const matchRes = text.match(this.parseFuncReg)
        try {
          if (matchRes) {
            const funcName = matchRes[1]
            const funcParams = matchRes[2]
            const paramsArr = funcParams.split(',')
            res.push(
              <span key={funcName} className="text-[#246dff]">
                {funcName}
                <span className="text-[#000]">(</span>
              </span>,
            )
            for (let i = 0; i < paramsArr.length; i++) {
              const childRes = this.dealText(paramsArr[i])
              if (getObjType(childRes) == 'Array') {
                res.push(...(childRes as any[]))
              } else {
                res.push(childRes)
              }
              if (i < paramsArr.length - 1) {
                res.push(
                  <span className="text-[#000]" key={funcName + 'span'}>
                    ,
                  </span>,
                )
              }
            }
            res.push(
              <span className="text-[#000]" key={funcName}>
                )
              </span>,
            )
            return res
          }
        } catch (error) {
          return text
        }
      default:
        return text
    }
  }
  static getTextType(text: string): ValueType {
    let type = ValueType.NumberString
    ;[this.valueReg, this.stringReg, this.parseFuncReg, this.annotationReg].find((reg, index: number) => {
      if (reg.test(text)) {
        type = Object.values(ValueType)[index] as ValueType
        return true
      }
    })
    return type
  }
  static parseGradientColor(type: GradientPaint['type'], paint: GradientPaint) {
    let func
    try {
      switch (type) {
        case 'GRADIENT_LINEAR':
          func = this.parseLinearGradient
          break
        case 'GRADIENT_RADIAL':
          func = this.parseRadialGradient
          break
        case 'GRADIENT_DIAMOND':
          func = this.parseDiamondGradient
          break
        case 'GRADIENT_ANGULAR':
          func = this.parseAngularGradient
          break
      }
      return func(paint)
    } catch (error) {
      return 'transparent'
    }
  }
  static parseLinearGradient(paint: GradientPaint) {
    const gradientLinearPaint = paint
    const {gradientTransform, gradientStops} = gradientLinearPaint
    let returnStr = ''
    if (!gradientTransform || !gradientStops) {
      return
    }
    let gradientTransformData: IGradientTransformData = {
      m00: 1,
      m01: 0,
      m02: 0,
      m10: 0,
      m11: 1,
      m12: 0,
    }
    const delta = gradientTransform[0][0] * gradientTransform[1][1] - gradientTransform[0][1] * gradientTransform[1][0]
    if (delta !== 0) {
      const deltaVal = 1 / delta
      gradientTransformData = {
        m00: gradientTransform[1][1] * deltaVal,
        m01: -gradientTransform[0][1] * deltaVal,
        m02:
          (gradientTransform[0][1] * gradientTransform[1][2] - gradientTransform[1][1] * gradientTransform[0][2]) *
          deltaVal,
        m10: -gradientTransform[1][0] * deltaVal,
        m11: gradientTransform[0][0] * deltaVal,
        m12:
          (gradientTransform[1][0] * gradientTransform[0][2] - gradientTransform[0][0] * gradientTransform[1][2]) *
          deltaVal,
      }
    }
    const rotationTruthy =
      gradientTransformData.m00 * gradientTransformData.m11 - gradientTransformData.m01 * gradientTransformData.m10 > 0
        ? 1
        : -1
    const rotationData = ((data: IGradientTransformData, param: {x: number; y: number}) => ({
      x: data.m00 * param.x + data.m01 * param.y,
      y: data.m10 * param.x + data.m11 * param.y,
    }))(gradientTransformData, {x: 0, y: 1})

    const colorList = gradientStops.map((gradientStop: any) => {
      const color = colorToStringWithOpacity(gradientStop.color, Number(Math.fround(gradientStop.color.a).toFixed(2)))
      const process = `${gradientStop.position * 100}%`
      return `${color} ${process}`
    })

    returnStr = `linear-gradient(${(
      (Math.atan2(rotationData.y * rotationTruthy, rotationData.x * rotationTruthy) / Math.PI) *
      180
    ).toFixed(2)}deg, ${colorList})`
    return returnStr
  }
  static calculateGradientDirection(transform: Transform) {
    const deltaX = transform[1][0] - transform[0][0]
    const deltaY = transform[1][1] - transform[0][1]
    const angle = Math.atan2(deltaY, deltaX) * (180 / Math.PI) + 90
    return `${angle}deg`
  }
  static formatNumberWithTwoDecimals(number: number) {
    return fixNum(number)
  }
  static parseRadialGradient(paint: GradientPaint) {
    const stops = paint.gradientStops
      .map(stop => {
        return `${ParseCssUtil.colorString(stop.color)} ${Math.round(stop.position * 60)}%`
      })
      .join(', ')

    return `radial-gradient(${stops})`
  }
  static parseDiamondGradient(paint: GradientPaint) {
    const numStops = paint.gradientStops.length
    const colors: string[] = []
    for (let i = 0; i < numStops; i++) {
      const stop = paint.gradientStops[i]
      const color = stop.color
      const formattedAlpha = (stop.color.a * (paint?.opacity || 1)).toFixed(2)
      const position = Math.round(stop.position * 50)
      colors.push(
        `rgba(${Math.round(color.r * 255)}, ${Math.round(color.g * 255)}, ${Math.round(
          color.b * 255,
        )}, ${formattedAlpha}) ${position}%`,
      )
    }

    const linearGradients = ['bottom right', 'bottom left', 'top left', 'top right'].map(
      direction => `linear-gradient(to ${direction}, ${colors.join(', ')}) ${direction} / 50% 50% no-repeat`,
    )

    return linearGradients.join(', ')
  }
  static parseAngularGradient(paint: GradientPaint) {
    const numStops = paint.gradientStops.length
    const colors: string[] = []
    for (let i = 0; i < numStops; i++) {
      const stop = paint.gradientStops[i]
      const color = stop.color
      const formattedAlpha = (stop.color.a * (paint.opacity || 1)).toFixed(2)
      const position = Math.round(stop.position * 100)
      colors.push(
        `rgba(${Math.round(color.r * 255)}, ${Math.round(color.g * 255)}, ${Math.round(
          color.b * 255,
        )}, ${formattedAlpha}) ${position}%`,
      )
    }

    const gradientDirection = `from ${Math.random() * 360}deg at ${paint.gradientTransform[0][0] * 100}% ${
      paint.gradientTransform[0][1] * 100
    }%`

    return `conic-gradient(${gradientDirection}, ${colors.join(', ')})`
  }
  static colorString(color: RGBA) {
    return `rgba(${Math.round(color.r * 255)}, ${Math.round(color.g * 255)}, ${Math.round(color.b * 255)}, ${color.a})`
  }
}
export default ParseCssUtil
