{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug Test", "program": "${workspaceFolder}/dist/test.js", "preLaunchTask": "npm: test", "cwd": "${workspaceFolder}", "internalConsoleOptions": "openOnSessionStart", "sourceMaps": true, "outFiles": ["${workspaceFolder}/dist/**/*.js"], "skipFiles": ["<node_internals>/**"], "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"], "smartStep": true, "console": "integratedTerminal"}]}