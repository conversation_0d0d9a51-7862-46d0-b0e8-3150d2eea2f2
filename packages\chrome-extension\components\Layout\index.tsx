import type {ReactNode} from 'react'
import {Outlet, useLocation, useNavigate} from 'react-router-dom'
import Header from '@/components/Header'
import Panel from '@/components/Panel'
import {Toaster} from '@/components/ui/sonner'
import {Tabs, TabsList, TabsTrigger} from '@/components/ui/tabs'
import {PortalContainerProvider} from '@/lib/portal-container'
import loginStore from '@/store/loginStore'
import {LoginDialog} from '../Login/LoginDialog'
import 'overlayscrollbars/overlayscrollbars.css'
import { OverlayScrollbarsComponent } from "overlayscrollbars-react"

// 导航配置
interface NavItem {
  key: string
  label: string
  path: string
  isActive: (pathname: string) => boolean
}

const navItems: NavItem[] = [
  {
    key: 'dimension',
    label: '标注信息',
    path: '/code/dimension',
    isActive: (pathname: string) => pathname === '/' || pathname === '/code/dimension',
  },
  {
    key: 'images',
    label: '快捷切图',
    path: '/code/images',
    isActive: (pathname: string) => pathname === '/code/images',
  },
  {
    key: 'plugins',
    label: '插件管理',
    path: '/code/plugins',
    isActive: (pathname: string) => pathname === '/code/plugins',
  },
]

interface LayoutProps {
  children?: ReactNode
}

const Layout = ({children}: LayoutProps) => {
  const location = useLocation()
  const navigate = useNavigate()

  const {isLoggedIn} = loginStore.state

  return (
    <>
      <PortalContainerProvider>
        <Panel>
          {/* Panel内部固定区域：Header + 导航菜单 */}
          <div className="flex flex-col flex-shrink-0">
            <Header />

            {/* 导航菜单 */}
            <div className="w-full px-2 flex justify-center my-2">
              <Tabs
                className="w-full"
                value={navItems.find(item => item.isActive(location.pathname))?.key || navItems[0].key}
                onValueChange={value => {
                  const item = navItems.find(nav => nav.key === value)
                  if (item) navigate(item.path)
                }}
              >
                <TabsList className="w-full">
                  {navItems.map(item => (
                    <TabsTrigger key={item.key} value={item.key} className="cursor-pointer">
                      {item.label}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </Tabs>
            </div>
          </div>

          {/* Panel内部可滚动区域：实际页面内容 */}
          {/* <div className="flex-1 min-h-0 overflow-y-auto scrollbar">
            {children || <Outlet />}
          </div> */}
          <OverlayScrollbarsComponent defer className="flex-1 min-h-0">
            {children || <Outlet />}
          </OverlayScrollbarsComponent>
        </Panel>
      </PortalContainerProvider>
      <Toaster position="top-center" />
      {!isLoggedIn && <LoginDialog />}
    </>
  )
}

export default Layout
