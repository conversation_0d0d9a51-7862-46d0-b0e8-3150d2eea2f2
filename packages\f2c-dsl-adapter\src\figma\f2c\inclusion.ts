import type {JsonAdapter} from '../jsonAdapter'
import {FigmaNodeType, NodeType, PostionalRelationship} from '../type'
import {orderAGreaterThanB} from './util'

// function addCondition(currentNode: JsonAdapter, targetNode: JsonAdapter) {
//   const currentOpacity =
//     currentNode.getACssAttribute('opacity') !== undefined && currentNode.getACssAttribute('opacity') !== 1
//   const currentRotation = currentNode.getACssAttribute('rotation') !== undefined
//   const notAutoLayout = currentNode.getAnnotation('AUTOLAYOUT') === undefined

//   const currentBlendMode = currentNode.getACssAttribute('mix-blend-mode') !== undefined

//   return (
//     currentNode.getType() !== NodeType.TEXT &&
//     notAutoLayout &&
//     !currentRotation &&
//     !currentOpacity &&
//     !currentBlendMode &&
//     // validation &&
//     orderAGreaterThanB(targetNode.getOrder(), currentNode.getOrder())
//   )
// }

// 相互包含的节点进行处理

function getOriginalDepth(node: JsonAdapter): number {
  const order = node.getOrder()
  if (order && typeof order === 'string') {
    return order.split('-').length
  }
  return Number.POSITIVE_INFINITY // 如果没有 order，给一个很大的深度，使其不容易包含其他节点
}

// 增强后的 addCondition 函数
function addCondition(
  potentialParentNode: JsonAdapter, // 当前节点，作为潜在的包含者 (父)
  potentialChildNode: JsonAdapter, // 目标节点，作为潜在的被包含者 (子)
): boolean {
  // 原有条件
  const notAutoLayout = potentialParentNode.getAnnotation('AUTOLAYOUT') === undefined
  const noRotation = potentialParentNode.getACssAttribute('rotation') === undefined
  const noOpacityEffect =
    potentialParentNode.getACssAttribute('opacity') === undefined ||
    Number.parseFloat(potentialParentNode.getACssAttribute('opacity')) === 1 // 仅在完全不透明时考虑
  const noBlendMode = potentialParentNode.getACssAttribute('mix-blend-mode') === undefined

  // 原有的层级顺序检查: potentialChildNode 的 order 必须大于 potentialParentNode 的 order
  // 这意味着在Figma图层列表中，child 在 parent 之上（视觉上 parent 覆盖 child）
  // 或者说，parent 的绘制顺序在 child 之后。
  // 这个条件通常用于确保背景元素能包含前景元素。
  const correctRenderOrder = orderAGreaterThanB(potentialChildNode.getOrder(), potentialParentNode.getOrder())

  //    检查原始深度关系，防止错误的“跨级”包含
  //    我们期望 potentialParentNode 的原始深度应该小于或等于 potentialChildNode 的原始深度。
  //    如果 potentialParentNode 是因扁平化而被提升的实例内部元素，其原始深度可能比 potentialChildNode (原实例的兄弟) 更深。
  //    getOrder() 返回如 "0-1" (深度2), "0-1-0" (深度3)
  const parentDepth = getOriginalDepth(potentialParentNode)
  const childDepth = getOriginalDepth(potentialChildNode)

  // 基本规则：父节点的原始深度不应显著大于子节点的原始深度。
  // 如果父节点是被扁平化提升上来的实例内部件，其 parentDepth 会较大。
  // 如果子节点是原实例的兄弟，其 childDepth 会较小。
  // 这种情况下，parentDepth > childDepth，不应发生包含。
  let reasonableDepthRelation = parentDepth <= childDepth

  // 特殊情况处理：如果父节点是被扁平化的实例的内部元素，而子节点是原实例的兄弟
  // 我们可以通过比较它们的 order 字符串的共同前缀来判断它们是否源自“非常不同”的原始分支。
  // 例如：parentOrder = "0-1-2-3" (原实例内部), childOrder = "0-2" (原实例的兄弟)
  // 共同前缀是 "0-"。
  // 如果父节点的 order 是子节点 order 的一个更深层级的延续，则可能是合理的。
  // 但如果它们在较高层级就分叉了，则不合理。

  const parentOrderParts = potentialParentNode.getOrder().split('-')
  const childOrderParts = potentialChildNode.getOrder().split('-')

  // 如果父节点的 order 长度大于子节点 order 长度，且子节点的 order 不是父节点 order 的前缀
  // 这通常意味着父节点来自一个更深的分支（比如实例内部），而子节点来自一个较浅的、平行的分支。
  if (parentOrderParts.length > childOrderParts.length) {
    let isChildPrefixOfParent = true
    for (let k = 0; k < childOrderParts.length; k++) {
      if (parentOrderParts[k] !== childOrderParts[k]) {
        isChildPrefixOfParent = false
        break
      }
    }
    if (!isChildPrefixOfParent) {
      // 例如：父 "0-1-2", 子 "0-2" -> 它们在第二层就不同了，父不应包含子
      reasonableDepthRelation = false
    }
  } else if (parentOrderParts.length === childOrderParts.length && parentOrderParts.length > 1) {
    // 同等深度，检查它们是否至少在倒数第二层是相同的父节点
    // 即除了最后一个数字，前面的路径都相同
    let sameBasePath = true
    for (let k = 0; k < parentOrderParts.length - 1; k++) {
      if (parentOrderParts[k] !== childOrderParts[k]) {
        sameBasePath = false
        break
      }
    }
    if (!sameBasePath) {
      // 如果它们在原始结构中不是紧密的兄弟（父路径不同），则不应轻易包含
      reasonableDepthRelation = false
    }
  }

  // --- 组合所有条件 ---
  const originalConditionsMet =
    potentialParentNode.getType() !== NodeType.TEXT && // 父节点不能是文本节点
    notAutoLayout && // 父节点不是 Auto Layout (因为 Auto Layout 有自己的子元素管理方式)
    noRotation && // 父节点没有旋转 (旋转的父容器内的包含关系可能复杂)
    noOpacityEffect && // 父节点没有部分透明 (部分透明的父包含不透明的子，视觉上可能不直观)
    noBlendMode && // 父节点没有混合模式
    correctRenderOrder // 满足原始的渲染顺序条件 (背景包含前景)

  // console.log(`addCondition for ${potentialParentNode.getName()} containing ${potentialChildNode.getName()}:`);
  // console.log(`  originalConditionsMet: ${originalConditionsMet}`);
  // console.log(`  isParentSuitableContainer: ${isParentSuitableContainer}`);
  // console.log(`  reasonableDepthRelation: ${reasonableDepthRelation} (ParentDepth: ${parentDepth}, ChildDepth: ${childDepth})`);

  return (
    originalConditionsMet && reasonableDepthRelation // 原始层级关系合理
  )
}

export const groupNodesByInclusion = (nodes: JsonAdapter[]): JsonAdapter[] => {
  const removedNodes = new Set<string>()
  const processed: JsonAdapter[] = []

  // console.log('before groupNodesByInclusion', nodes)

  // 从最后一个节点开始，因为最后一个节点的层级最深最右可能包含其它节点
  for (let i = nodes.length - 1; i >= 0; i--) {
    const currentNode = nodes[i]

    if (removedNodes.has(currentNode.getId())) {
      continue
    }

    for (let j = i + 1; j < nodes.length; j++) {
      const targetNode = nodes[j]

      if (removedNodes.has(targetNode.getId())) {
        continue
      }

      // console.log(
      //   '【groupNodesByInclusion】relationship: ',
      //   currentNode.getPositionalRelationship(targetNode),
      //   'currentNode: ',
      //   currentNode.name,
      //   'targetNode: ',
      //   targetNode.name,
      // )
      switch (currentNode.getPositionalRelationship(targetNode)) {
        case PostionalRelationship.COMPLETE_OVERLAP:
        case PostionalRelationship.INCLUDE:
          if (addCondition(currentNode, targetNode)) {
            // console.log('添加进子元素', currentNode, targetNode)
            removedNodes.add(targetNode.getId())
            currentNode.addChildren([targetNode])
          }
      }
    }

    for (let j = 0; j < i; j++) {
      const targetNode = nodes[j]

      if (removedNodes.has(targetNode.getId())) {
        continue
      }

      // console.log(currentNode.getPositionalRelationship(targetNode), currentNode, targetNode);

      switch (currentNode.getPositionalRelationship(targetNode)) {
        case PostionalRelationship.COMPLETE_OVERLAP:
        case PostionalRelationship.INCLUDE:
          if (addCondition(currentNode, targetNode)) {
            // console.log('添加进子元素', currentNode, targetNode);
            removedNodes.add(targetNode.getId())
            currentNode.addChildren([targetNode])
          }
      }
    }
  }

  for (let i = 0; i < nodes.length; i++) {
    const currentNode = nodes[i]

    if (removedNodes.has(currentNode.getId())) {
      continue
    }

    processed.push(currentNode)
  }

  // console.log('after groupNodesByInclusion', processed);

  return processed
}
