// import {Option} from './code/code'
import { CutDiagramItem } from '@/store/cutDiagramStore'
import base64js from 'base64-js'

function getWidthAndHeight(node: SceneNode) {
  let rect = { width: 0, height: 0 }
  if (node.hasOwnProperty('absoluteRenderBounds') && (node as any).absoluteRenderBounds !== null) {
    rect = (node as any).absoluteRenderBounds as Rect
  } else {
    rect = node.absoluteBoundingBox as Rect
  }
  return rect
}

export const exportImgs = async (figmaNodes: readonly SceneNode[], isSelecton = true) => {
  let files: {
    path: string
    content: string
    width: number
    height: number
    name: string
    scale: number
    format: string
  }[] = []
  for (const figmaNode of figmaNodes) {
    const rect = getWidthAndHeight(figmaNode)
    if (figmaNode.exportSettings.length > 0) {
      console.log('figmaNode', figmaNode.name, figmaNode)

      for (const exportSetting of figmaNode.exportSettings) {
        if (exportSetting.format === 'PNG' || exportSetting.format === 'JPG' || exportSetting.format === 'SVG') {
          const uint8Array = await figmaNode.exportAsync(exportSetting)
          const fileData = base64js.fromByteArray(uint8Array)
          const type = (exportSetting as ExportSettingsImage).constraint?.type
          const constraintValue = (exportSetting as ExportSettingsImage).constraint?.value
          files.push({
            path: `/${exportSetting.suffix ? exportSetting.suffix : figmaNode.id + figmaNode.name
              }.${exportSetting.format.toLocaleLowerCase()}`,
            content: fileData,
            format: exportSetting.format,
            // width: figmaNode.width,
            // height: figmaNode.height,
            name: figmaNode.name,
            ...getSize(
              type,
              rect.width,
              rect.height,
              type == 'HEIGHT' ? constraintValue : void 0,
              type == 'WIDTH' ? constraintValue : void 0,
              type == 'SCALE' ? constraintValue : 1,
            ),
          })
        }
      }
    } else if (isSelecton) {
      console.log('【exportImgs】走默认')
      // selection 图层默认没有添加 export 信息，则导出默认 x1.png
      const uint8Array = await figmaNode.exportAsync({ format: 'PNG' })
      const fileData = base64js.fromByteArray(uint8Array)
      files.push({
        path: `/${figmaNode.id + figmaNode.name}.png`,
        content: fileData,
        width: rect.width,
        height: rect.height,
        name: figmaNode.name,
        scale: 1,
        format: 'PNG',
      })
    }
    if (
      figmaNode.type === 'BOOLEAN_OPERATION' ||
      figmaNode.type === 'COMPONENT' ||
      figmaNode.type === 'COMPONENT_SET' ||
      figmaNode.type === 'FRAME' ||
      figmaNode.type === 'GROUP' ||
      figmaNode.type === 'INSTANCE' ||
      figmaNode.type === 'SECTION'
    ) {
      if (figmaNode.children.length > 0) {
        const returnFiles = await exportImgs(figmaNode.children, false)
        files = files.concat(returnFiles)
      }
    }
  }
  return files
}

function getSize(
  type: ExportSettingsConstraints['type'] | undefined,
  w: number,
  h: number,
  figmaH?: number,
  figmaW?: number,
  scale = 1,
) {
  let width
  let height
  switch (type) {
    case 'SCALE':
      width = w
      height = h
      break
    case 'HEIGHT':
      height = figmaH
      width = (figmaH / h) * height
      break
    case 'WIDTH':
      width = figmaW
      height = (figmaW / w) * width
      break
    default:
      width = w
      height = h
      break
  }

  return { width: width * scale, height: height * scale, scale }
}

export const getBase64ById = async (nodeId: string, scale = 1, format: 'PNG' | 'SVG' | 'JPG' = 'PNG') => {
  const node = (await figma.getNodeByIdAsync(nodeId)) as SceneNode
  if (node && 'exportAsync' in node) {
    const imgFormat: any = format.toUpperCase()
    const data = await node.exportAsync(
      imgFormat === 'PNG' || imgFormat === 'JPG'
        ? {
          format: imgFormat,
          constraint: {
            type: 'SCALE',
            value: scale,
          },
        }
        : {
          format: imgFormat,
        },
    )
    const base64 = base64js.fromByteArray(data)
    return base64
    // setImgSrc(`data:image/png;base64,${base64}`)
  }
}

export const makeExportInfo = async (cutDiagrams: CutDiagramItem[]) => {
  const res = await Promise.all(cutDiagrams.map(async (item) => {
    const content = await getBase64ById(item.id, item.scale, item.format)
    return {
      path: item.path,
      content: content || '',
      width: item.width,
      height: item.height,
      name: item.name,
      scale: item.scale,
      format: item.format,
      key: item.key
    }
  }))
  return res
}