import Text from './text'
import Frame from  './frame'
import InfoItem from '@/components/Dimension'
import dimensionStore from '@/store/dimensionStore'
const DimensionExt = () => {
    const {dimensionInfo,dimensionInfo: {nodeType}} = dimensionStore.state
    const [child, setChild] = useState<React.ReactNode | null>(null)
    useEffect(()=>{
      let child
      switch (nodeType) {
        case 'TEXT':
          child = <Text layerInfo={dimensionInfo} />
          break
        default:
          const fillInfo = dimensionInfo.css?.demisionFills
          if (fillInfo && fillInfo?.length > 0){
            child = <Frame fillInfo={fillInfo} layerInfo={dimensionInfo} />
          }
          break
      } 
      setChild(child)
    },[dimensionInfo, nodeType])
    const extTitle = useMemo((nodeType?: NodeType) => {
      switch (nodeType) {
        case 'TEXT':
          return '文本'
        default:
          return'填充'
      }
    }, [nodeType])


    return child ? (
      <>
        <InfoItem infoName={extTitle}>{child}</InfoItem>
      </>
    ) : null
}
export default DimensionExt