import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { usePluginExecutor } from '@/hooks/usePluginExecutor'
import { toast } from 'sonner'

export const PluginTest: React.FC = () => {
  const { activePlugin, executeCommand, isLoading } = usePluginExecutor()

  const testHelloCommand = async () => {
    if (!activePlugin) {
      toast.error('No active plugin')
      return
    }

    try {
      const result = await executeCommand('hello', 'Chrome Extension')
      toast.success(`Command result: ${result}`)
    } catch (error) {
      toast.error(`Command failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  const testGetTabCommand = async () => {
    if (!activePlugin) {
      toast.error('No active plugin')
      return
    }

    try {
      const result = await executeCommand('getTab')
      toast.success(`Current tab: ${result?.title || 'Unknown'}`)
    } catch (error) {
      toast.error(`Command failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Plugin Testing</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {activePlugin ? (
          <div>
            <div className="mb-4">
              <h3 className="font-medium">Active Plugin: {activePlugin.name}</h3>
              <p className="text-sm text-muted-foreground">{activePlugin.description}</p>
            </div>
            
            <div className="space-y-2">
              <Button 
                onClick={testHelloCommand} 
                disabled={isLoading}
                className="w-full"
              >
                Test Hello Command
              </Button>
              
              <Button 
                onClick={testGetTabCommand} 
                disabled={isLoading}
                variant="outline"
                className="w-full"
              >
                Test Get Tab Command
              </Button>
            </div>
            
            {activePlugin.commands && (
              <div className="mt-4">
                <h4 className="font-medium text-sm mb-2">Available Commands:</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  {Object.keys(activePlugin.commands).map(command => (
                    <li key={command} className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                      {command}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <p>No active plugin</p>
            <p className="text-xs mt-1">Go to Plugins tab to install and activate a plugin</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default PluginTest
