import { JSX } from "react"

interface IColumn {
  infoName: string
  children?: JSX.Element | JSX.Element[]
  grow?: boolean
  wrap?: boolean
}
const Column = (props: IColumn) => {
  return (
    <div className={`commonPos flex justify-between items-center mt-2.5`}>
      <span className="text-[12px] text-disable place-self-start mt-[6px]">{props.infoName}</span>
      <div className={`flex justify-between ${props.wrap && 'flex-wrap gap-y-2.5'} ${props.grow && 'grow'}  w-[216px]`}>
        {props.children}
      </div>
    </div>
  )
}
export default Column
