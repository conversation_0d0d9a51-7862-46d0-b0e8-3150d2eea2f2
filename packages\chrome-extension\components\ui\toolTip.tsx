import * as TooltipPrimitive from "@radix-ui/react-tooltip"
import type { ReactNode } from "react"
import { cn } from "../../lib/utils"

interface TooltipProps extends Omit<TooltipPrimitive.TooltipContentProps, "content"> {
    children: ReactNode;
    content: ReactNode;
    open?: boolean;
    defaultOpen?: boolean;
    onOpenChange?: (open: boolean) => void;
    theme?: "default" | "white";
    delayDuration?: number;
}

function Tooltip({
    children,
    content,
    open,
    defaultOpen,
    onOpenChange,
    theme = "default",
    delayDuration = 200,
    className,
    ...props
}: TooltipProps) {
    return (
        <TooltipPrimitive.Provider delayDuration={delayDuration}>
            <TooltipPrimitive.Root
                open={open}
                defaultOpen={defaultOpen}
                onOpenChange={onOpenChange}
            >
                <TooltipPrimitive.Trigger asChild>
                    {children}
                </TooltipPrimitive.Trigger>
                <TooltipPrimitive.Content 
                    side="top" 
                    align="center" 
                    className={cn(
                        "pt-1 pb-1 pl-2 pr-2 rounded-sm",
                        theme === "default" && "bg-black text-white",
                        theme === "white" && "bg-white text-black shadow-xl ring-1 ring-gray-200",
                        className
                    )} 
                    {...props}
                >
                    {content}
                    <TooltipPrimitive.Arrow  fill={theme === "white" ? "#fff" : "#000"} width={11} height={5} />
                </TooltipPrimitive.Content>
            </TooltipPrimitive.Root>
        </TooltipPrimitive.Provider>
    )
}
export default Tooltip