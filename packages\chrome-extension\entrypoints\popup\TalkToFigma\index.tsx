import {Button} from '@/components/ui/button'
import {handleCommand} from '@/lib/talk'
import {useCallback, useRef, useState} from 'react'

function generateChannelName() {
  const characters = 'abcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < 8; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length))
  }
  return result
}

function TalkToFigma() {
  const [connected, setConnected] = useState(false)
  const [statusDesc, setStatusDesc] = useState('')
  const socketRef = useRef<WebSocket | null>(null)
  const [currentChannel, setCurrentChannel] = useState<string>('')
  const currentChannelRef = useRef<string>('') // Use ref for immediate access

  function updateConnectionStatus(isConnected: boolean, message: string) {
    setConnected(isConnected)
    setStatusDesc(message)
  }

  // Send success response back to WebSocket
  function sendSuccessResponse(id: string, result: any) {
    if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
      console.error('Cannot send response: socket not connected')
      return
    }

    if (!currentChannelRef.current) {
      console.error('Cannot send response: no channel set')
      console.error('currentChannel state:', currentChannel)
      console.error('currentChannelRef.current:', currentChannelRef.current)
      return
    }

    const response = {
      id,
      type: 'message',
      channel: currentChannelRef.current,
      message: {
        id,
        result,
      },
    }

    console.log('Sending success response to channel:', currentChannelRef.current)
    console.log('Response data:', JSON.stringify(response))
    socketRef.current.send(JSON.stringify(response))
    console.log('Success response sent successfully')
  }

  // Send error response back to WebSocket
  const sendErrorResponse = useCallback(
    (id: string, errorMessage: string) => {
      if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
        console.error('Cannot send error response: socket not connected')
        return
      }

      if (!currentChannelRef.current) {
        console.error('Cannot send error response: no channel set')
        console.error('currentChannel state:', currentChannel)
        console.error('currentChannelRef.current:', currentChannelRef.current)
        return
      }

      const response = {
        id,
        type: 'message',
        channel: currentChannelRef.current,
        message: {
          id,
          error: errorMessage,
          result: {},
        },
      }

      console.log('Sending error response to channel:', currentChannelRef.current)
      console.log('Error response data:', JSON.stringify(response))
      socketRef.current.send(JSON.stringify(response))
      console.log('Error response sent successfully')
    },
    [currentChannel],
  )

  async function executeCommand(command: string, params?: any, commandId?: string) {
    console.log('Executing command:', command, params)
    console.log('Command ID:', commandId)
    console.log('Current channel state:', currentChannel)
    console.log('Current channel ref:', currentChannelRef.current)

    try {
      // For non-creation commands, execute and wait for completion
      const result = await handleCommand(command, params)
      console.log('Command executed successfully:', result)

      // Send response after completion for non-creation commands
      if (socketRef.current && commandId) {
        sendSuccessResponse(commandId, result)
      }

      return result
    } catch (error) {
      console.error('Error executing command:', error)

      // Send error response for non-creation commands
      if (socketRef.current && commandId) {
        sendErrorResponse(commandId, error instanceof Error ? error.message : 'Unknown error')
      }

      throw error
    }
  }

  async function handleSocketMessage(payload: any) {
    const data = payload.message
    console.log('handleSocketMessage', data)

    // If it's a new command
    if (data.command) {
      try {
        // Pass the command ID so we can send response back
        await executeCommand(data.command, data.params, data.id)
      } catch (error) {
        // Error is already handled in executeCommand
        console.log('Error executing command', error)
      }
    }
  }

  async function connectToServer(port: number) {
    try {
      const socket = new WebSocket(`ws://localhost:${port}`)
      socketRef.current = socket

      socket.onopen = () => {
        // Generate random channel name
        const channelName = generateChannelName()
        console.log('Joining channel:', channelName)
        setCurrentChannel(channelName)
        currentChannelRef.current = channelName // Set ref immediately

        // Join the channel using the same format as App.tsx
        socket.send(
          JSON.stringify({
            type: 'join',
            channel: channelName.trim(),
          }),
        )
      }

      socket.onmessage = event => {
        try {
          const data = JSON.parse(event.data)
          console.log('Received message:', data)

          if (data.type === 'system') {
            // Successfully joined channel
            if (data.message && data.message.result) {
              const channelName = data.channel
              console.log('Successfully joined channel:', channelName)
              console.log('Current channel state:', currentChannel)

              // Make sure currentChannel is set correctly
              if (channelName && channelName !== currentChannelRef.current) {
                console.log('Updating current channel from', currentChannelRef.current, 'to', channelName)
                setCurrentChannel(channelName)
                currentChannelRef.current = channelName // Set ref immediately
              }

              updateConnectionStatus(
                true,
                `Connected to server on port ${port} in channel: <strong>${channelName}</strong>`,
              )
            }
          } else if (data.type === 'error') {
            console.error('Error:', data.message)
            updateConnectionStatus(false, `Error: ${data.message}`)
            socket.close()
          }

          handleSocketMessage(data)
        } catch (error) {
          console.error('Error parsing message:', error)
        }
      }

      socket.onclose = () => {
        socketRef.current = null
        setCurrentChannel('')
        currentChannelRef.current = '' // Clear ref
        updateConnectionStatus(false, 'Disconnected from server')
      }

      socket.onerror = error => {
        console.error('WebSocket error:', error)
        socketRef.current = null
        setCurrentChannel('')
        currentChannelRef.current = '' // Clear ref
        updateConnectionStatus(false, 'Connection error')
      }
    } catch (error: any) {
      console.error('Connection error:', error)
      updateConnectionStatus(false, `Connection error: ${error.message || 'Unknown error'}`)
    }
  }

  return (
    <div>
      <div>
        <div>WebSocket Server Port</div>
        <div>
          <input type="number" id="port" placeholder="3055" value="3055" min="1024" max="65535" />
          <Button
            variant="default"
            size={'sm'}
            onClick={() => {
              const port = Number.parseInt('3055', 10) || 3055
              connectToServer(port)
            }}
          >
            Connect
          </Button>
          <Button
            variant="default"
            size={'sm'}
            onClick={() => {
              // Test command - get document info
              executeCommand('get_document_info', {})
            }}
          >
            测试获取信息
          </Button>
          <Button
            variant="default"
            size={'sm'}
            onClick={() => {
              // Test create_frame command with immediate response
              executeCommand(
                'create_frame',
                {
                  x: 100,
                  y: 100,
                  width: 200,
                  height: 150,
                  name: 'Test Frame',
                },
                'test-frame-' + Date.now(),
              )
            }}
          >
            测试创建Frame
          </Button>
        </div>
        <div>{statusDesc}</div>
      </div>
    </div>
  )
}
export default TalkToFigma
