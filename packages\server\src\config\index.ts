export {convertToCode} from '@baidu/f2c-dsl-adapter'
export const config = {
  port: process.env.PORT || 3000,
  enableHttps: process.env.ENABLE_HTTPS === 'true' || false,
  figma: {
    clientId: 'sSprZLQ4j6j0HITc81yh5G',
    clientSecret: 'sXGilh6Mu4ff2fq7qbG9brF968xxuB',
    redirectUri: 'https://f2c-figma-api.yy.com/oauth/callback', // 保持这个路径不变
    authBackUri: 'https://f2c-figma-api.yy.com/oauth/callback',
    astroBackUri: 'https://astro.yy.com/oauth/callback',
    oauthUrl: 'https://www.figma.com/oauth',
    tokenUrl: 'https://api.figma.com/v1/oauth/token',
  },
}
