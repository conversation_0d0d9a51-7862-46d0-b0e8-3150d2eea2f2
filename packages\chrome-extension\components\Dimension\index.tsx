import {type JSX, useState} from 'react'
import fold from './assets/fold.png'
const InfoItem = (props: {
  infoName?: string | JSX.Element | JSX.Element[]
  children?: any
  hideDividLine?: boolean
  className?: string
  fold?: boolean
}) => {
  const [foldInfo, setFoldInfo] = useState<boolean>(props.fold !== undefined ? props.fold : true)

  return (
    <div
      style={{height: foldInfo ? '38px' : 'auto'}}
      className={`overflow-hidden overflow-x-visible mb-2 w-full ${props.className || ''}`}
    >
      <div className="commontitle flex justify-between items-center h-[38px]">
        <div className="text-[13px] border-black font-semibold overflow-hidden max-w-full max-h-[100%] text-ellipsis pt-1 pb-1 pr-2">
          {props.infoName || ''}
        </div>

        <img
          width={20}
          src={fold}
          className={`cursor-pointer ${foldInfo && 'rotate-180'}`}
          onClick={() => {
            setFoldInfo(foldInfo => {
              return !foldInfo
            })
          }}
        />
      </div>
      {props.children}
    </div>
  )
}
export default InfoItem
