import {Download, Loader2, X} from 'lucide-react'
import type React from 'react'
import {useEffect, useRef, useState} from 'react'
import {Button} from '@/components/ui/button'
import {Input} from '@/components/ui/input'
import {type PluginData, usePluginInstall} from '@/hooks/usePluginInstall'

interface PluginInstallerProps {
  onInstalled: (pluginData: PluginData) => void
  onCancel: () => void
  className?: string
}

const BUILT_IN_SOURCE_RE = /@[a-z\d_-]+/

export const PluginInstaller: React.FC<PluginInstallerProps> = ({onInstalled, onCancel, className = ''}) => {
  const [source, setSource] = useState('')
  const inputRef = useRef<HTMLInputElement>(null)
  const {validity, installing, install, cancel, setValidity} = usePluginInstall()

  // Focus input when component mounts
  useEffect(() => {
    inputRef.current?.focus()
  }, [])

  // Show validation message
  useEffect(() => {
    if (validity && inputRef.current) {
      inputRef.current.setCustomValidity(validity)
      inputRef.current.reportValidity()
    }
  }, [validity])

  const validate = (): boolean => {
    if (!source) {
      setValidity('请输入插件地址')
      return false
    }

    if (!BUILT_IN_SOURCE_RE.test(source) && !isValidUrl(source)) {
      setValidity('请输入有效的插件地址')
      return false
    }

    setValidity('')
    return true
  }

  const isValidUrl = (string: string): boolean => {
    try {
      new URL(string)
      return true
    } catch (_) {
      return false
    }
  }

  const handleInstall = async () => {
    console.log('🎯 PluginInstaller: Starting installation process...')

    if (!validate()) {
      console.log('❌ PluginInstaller: Validation failed')
      return
    }

    const src = source
    console.log('🔗 PluginInstaller: Installing from source:', src)
    setSource('正在安装...')

    try {
      console.log('⏳ PluginInstaller: Calling install function...')
      const installed = await install(src)
      console.log('📦 PluginInstaller: Install function returned:', installed)

      setSource(src)

      if (installed) {
        console.log('✅ PluginInstaller: Installation successful, calling onInstalled callback...')
        onInstalled(installed)
        console.log('🎉 PluginInstaller: onInstalled callback completed')
      } else {
        console.log('❌ PluginInstaller: Installation failed - no result returned')
      }
    } catch (error) {
      console.error('💥 PluginInstaller: Installation threw an error:', error)
      setSource(src)
    }
  }

  const handleCancel = () => {
    cancel()
    onCancel()
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleInstall()
    } else if (e.key === 'Escape') {
      handleCancel()
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSource(e.target.value)
    setValidity('')
    if (inputRef.current) {
      inputRef.current.setCustomValidity('')
    }
  }

  return (
    <div className={`space-y-2 ${className}`}>
      {/* First row: Icon + Title */}
      <div className="flex items-center gap-2">
        <Download className="h-3 w-3 text-primary" />
        <div className="text-xs font-medium">安装插件</div>
      </div>

      {/* Second row: Input field */}
      <Input
        ref={inputRef}
        type="text"
        value={source}
        onChange={handleInputChange}
        onBlur={handleInstall}
        onKeyDown={handleKeyDown}
        disabled={installing}
        placeholder="https://... 或 @插件名称"
        className="text-xs h-8"
      />

      {/* Third row: Buttons */}
      <div className="flex items-center gap-2">
        <Button
          variant="default"
          size="sm"
          onClick={handleInstall}
          disabled={installing || !source.trim()}
          className="flex-1 h-7 text-xs"
        >
          {installing ? (
            <>
              <Loader2 className="h-3 w-3 animate-spin mr-1" />
              正在安装...
            </>
          ) : (
            <>
              <Download className="h-3 w-3 mr-1" />
              安装
            </>
          )}
        </Button>
        <Button variant="outline" size="sm" onClick={handleCancel} className="h-7 w-7 p-0">
          <X className="h-3 w-3" />
        </Button>
      </div>
    </div>
  )
}

export default PluginInstaller
