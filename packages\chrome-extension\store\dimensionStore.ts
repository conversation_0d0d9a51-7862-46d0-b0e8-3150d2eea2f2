import type {ModalProps} from '@/components/ui/dialog/Modal'
import type {CSSProperties} from 'react'
import {BaseStore} from './baseStore'
export interface LayerInfo {
  nodeName?: string
  nodeId?: string
  nodeType?: NodeType
  css?: Record<string, any>
  codeCss?: CSSProperties
  svg?: string
  isSvg?: boolean
}
export enum Platform {
  WEB = 'Web',
  ANDROID = 'Android',
  IOS = 'IOS'
}
export class DimensionStore extends BaseStore {
  constructor() {
    super()
  }
  dimensionInfo: LayerInfo = {}
  currentSelectUnit = 'px'
  currentRemBase = 1
  currentVwBase = 750
  currentMagnification = 1
  currentPlatform = Platform.WEB
  showSettingsModal: ModalProps | null = null
  globalSettings: {
    personalToken?: string
  } = JSON.parse(localStorage.getItem('f2c:globalSettings') || '{}') ?? {}

  setDimensionInfo(dimensionInfo: LayerInfo) {
    this.dimensionInfo = dimensionInfo || {}
  }
  setCurrentSelectUnit(currentSelectUnit: string) {
    this.currentSelectUnit = currentSelectUnit
  }
  setCurrentPlatform(currentPlatform: Platform) {
    this.currentPlatform = currentPlatform
  }

  setCurrentRemBase(currentRemBase: number | string) {
    this.currentRemBase = Number(currentRemBase)
  }

  setCurrentVwBase(currentVwBase: number | string) {
    this.currentVwBase = Number(currentVwBase)
  }
  setCurrentMagnification(currentMagnification: number | string) {
    this.currentMagnification = Number(currentMagnification)
  }

  setModalShow = (props: ModalProps | null) => {
    this.showSettingsModal = props
  }

  setGlobalSettings = (settings: any) => {
    this.globalSettings = {...this.globalSettings, ...settings}
    localStorage.setItem('f2c:globalSettings', JSON.stringify(this.globalSettings))
  }
}
const dimensionStore = new DimensionStore()
export default dimensionStore
