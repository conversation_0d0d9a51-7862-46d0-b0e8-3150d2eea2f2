import { ExtensionBridge } from '@/lib/auth'
import { toast } from 'sonner'
import { BaseStore } from './baseStore'

export class LoginStore extends BaseStore {
  constructor() {
    super()
  }

  // 状态定义
  isLoggedIn = false
  showLogin = false
  figmaIdentityInfo = ''
  figmaIdentitySign = ''
  email = ''
  userInfoOfFigma: PluginAPI['currentUser'] | null = null

  get hasLoginInfo() {
    return this.figmaIdentityInfo && this.figmaIdentitySign
  }

  // Setter方法
  setShowLogin(showLogin: boolean): void {
    this.showLogin = showLogin;
  }

  setIsLoggedIn(isLoggedIn: boolean): void {
    this.isLoggedIn = isLoggedIn;
  }

  setFigmaIdentityInfo(value: string): void {
    this.figmaIdentityInfo = value;
  }

  setFigmaIdentitySign(value: string): void {
    this.figmaIdentitySign = value;
  }

  setEmail(email: string): void {
    this.email = email;
  }

  setUserInfoOfFigma(userInfo: PluginAPI['currentUser'] | null): void {
    this.userInfoOfFigma = userInfo;
  }

  // 检查登录状态
  async checkLoginStatus(): Promise<boolean> {
    try {
      const {figmaIdentityInfo, figmaIdentitySign} = await ExtensionBridge.getInstance().sendMessage({
        type: 'getLoginInfo',
      })
      this.setFigmaIdentityInfo(figmaIdentityInfo)
      this.setFigmaIdentitySign(figmaIdentitySign)
      if(figmaIdentityInfo && figmaIdentitySign) {
        return this.getUserInfo(figmaIdentityInfo, figmaIdentitySign)
      }
      return false
    } catch {
      console.error('[checkLoginStatus]:获取登录信息失败')
      return false
    }
  }

  // 获取用户信息
  async getUserInfo(figmaIdentityInfo: string, figmaIdentitySign: string): Promise<boolean> {
    try {
      const response = await fetch('https://f2c-api.yy.com/user/loginInfo', {
        credentials: 'include',
        headers: {
          'figma-identity-info': figmaIdentityInfo,
          'figma-identity-sign': figmaIdentitySign,
        },
      });

      if(!response.ok) {
           toast.error(`网络错误: ${response.statusText}`)
           return false
      }

      const res = await response.json();

      if (res.code == 0) {
        this.setEmail(res.data.email);
        this.setIsLoggedIn(true);
        this.setShowLogin(false);
        this.setUserInfoOfFigma({...window.figma.currentUser} as PluginAPI['currentUser'])
        return true;
      }
      

      this.setIsLoggedIn(false);
      return false;
    } catch (error) {
      console.error('登录请求失败:', error);
      this.setIsLoggedIn(false);
      return false;
    }
  }

  async setLoginInfoToStorage({figmaIdentityInfo, figmaIdentitySign}: {figmaIdentityInfo: string, figmaIdentitySign: string}) {
    try {
      this.setFigmaIdentityInfo(figmaIdentityInfo)
      this.setFigmaIdentitySign(figmaIdentitySign)
      const condition = await ExtensionBridge.getInstance().sendMessage({
        type: 'storeLoginInfo',
        data: {figmaIdentityInfo, figmaIdentitySign},
      })
      if(condition) {
        console.log('登录信息figmaIdentityInfo、figmaIdentitySign已保存到扩展存储中')
      }
    } catch (error) {
      console.error('保存登录信息失败:', error);
    }
  }

  async clearLoginInfo() {
    this.setFigmaIdentityInfo('')
    this.setFigmaIdentitySign('')
    this.setEmail('')
    this.setUserInfoOfFigma(null)
    this.setIsLoggedIn(false)
    this.setShowLogin(false)
    const condition = await ExtensionBridge.getInstance().sendMessage({
      type: 'removeLoginInfo',
    })
    if(condition) {
      console.log('登录信息已从扩展存储中清除')
    }
  }

  // 退出登录
  async loginOut(): Promise<void> {
    await this.clearLoginInfo()
  }
}

const loginStore = new LoginStore()
export default loginStore