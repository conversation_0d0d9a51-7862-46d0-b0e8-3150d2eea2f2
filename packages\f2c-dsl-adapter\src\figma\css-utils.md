
1. 基础辅助函数:
   - `isMixed`
   - `blendColors`
   - `round`
   - `isEqualToZero`

2. 颜色相关:
   - `getRgbaFromPaints`
   - `getTextColor`
   - `analyseStrokeColor`

3. 文本样式相关:
   - `figmaLineHeightToCssString`
   - `figmaLetterSpacingToCssString`
   - `figmaFontNameToCssString`
   - `getFontStyles`
   - `getTextStyles`
   - `getMostCommonFieldInString`

4. 边框相关:
   - `getBorderRadius`
   - `analyseStrokeWidth`
   - `getBorderColor`
   - `getTextStrokeColor`
   - `getBorderStyle`
   - `HasBorderWidth`

5. 效果相关:
   - `hasShadow`
   - `setTextOverflow`

6. 变换相关:
   - `calculateGradientTransform`
   - `applyMatrixToPoint`

7. 类型判断:
   - `isTextNode`
   - `isFrameNode`
   - `isGroupNode`

