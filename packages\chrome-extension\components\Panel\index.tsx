import {motion} from 'motion/react'
import type React from 'react'
import {useEffect, useRef, useState} from 'react'
import {useLocation, useNavigate} from 'react-router-dom'
import config from '@/lib/config'
import Logo from '@/public/icons/icon32.png'
import {ROUTES} from '@/router/router'
import panelStore from '@/store/panelStore'
import {Menu} from './DropdownMenu'
import UserButton from './UserButton'

interface IPanelProps {
  children?: React.ReactNode
}

function Panel(props: IPanelProps) {
  const {children} = props
  const panelRef = useRef<HTMLElement>(null)
  const {isExpend, setIsExpend, position, setPosition} = panelStore.state
  const isExpendRef = useRef<boolean>(isExpend)
  const lastSizeRef = useRef<{with: number; height: number}>({with: 0, height: 0})
  const location = useLocation()
  const navigate = useNavigate()

  // 计算初始位置 - 使用保守的默认值确保插件可见
  const getInitialPosition = () => {
    if (position.x !== 0 || position.y !== 0) {
      // 有保存的位置，使用保存的位置
      console.log('🔍 使用保存的位置:', position)
      return position
    }
    // 没有保存的位置，使用保守的默认位置
    const defaultPosition = {x: 100, y: 50} // 简单的固定位置，确保可见
    console.log('🔍 使用默认位置:', defaultPosition)
    return defaultPosition
  }

  const [currentPosition, setCurrentPosition] = useState(getInitialPosition)

  // 根据当前路由获取视图类型
  const getCurrentViewType = () => {
    if (location.pathname === ROUTES.DESIGN) {
      return 'design'
    }
    // 所有以/code开头的路径都属于code视图
    if (location.pathname.startsWith('/code') || location.pathname === '/') {
      return 'code'
    }
    return 'code' // 默认为code视图
  }

  // 处理视图切换
  const handleViewChange = (value: string) => {
    if (value === 'design') {
      navigate(ROUTES.DESIGN)
    } else {
      // 切换到code视图时，默认导航到dimension子路由
      navigate(ROUTES.CODE_DIMENSION)
    }
  }

  function switchPanelSize() {
    setIsExpend(!isExpend)
    isExpendRef.current = !isExpendRef.current
  }

  // 处理拖拽结束事件，保存位置
  const handleDragEnd = (event: any, info: any) => {
    // 使用offset计算新位置，而不是point（鼠标绝对位置）
    const newPosition = {
      x: currentPosition.x + info.offset.x,
      y: currentPosition.y + info.offset.y,
    }
    console.log('🎯 拖拽结束 - 原位置:', currentPosition, '偏移量:', info.offset, '新位置:', newPosition)
    setCurrentPosition(newPosition)
    setPosition(newPosition.x, newPosition.y)
  }

  // 同步位置状态
  useEffect(() => {
    if (position.x !== 0 || position.y !== 0) {
      console.log('🔄 同步位置状态:', position)
      setCurrentPosition(position)
    }
  }, [position])

  // 保存默认位置（如果是第一次使用）
  useEffect(() => {
    if (position.x === 0 && position.y === 0) {
      const defaultPosition = getInitialPosition()
      console.log('💾 第一次使用，保存默认位置:', defaultPosition)
      setPosition(defaultPosition.x, defaultPosition.y)
    }
  }, [position, setPosition])

  useEffect(() => {
    // 在组件挂载后发送尺寸信息
    const sendSizeToParent = () => {
      if (panelRef.current) {
        const {offsetWidth, offsetHeight} = panelRef.current

        const newWidth = isExpendRef.current ? config.pannelWidth : config.pannelMinWidth
        if (lastSizeRef.current.with !== newWidth || lastSizeRef.current.height !== offsetHeight) {
          lastSizeRef.current.with = newWidth
          lastSizeRef.current.height = offsetHeight
        }
        // console.log('发送尺寸信息:', { width: offsetWidth, height: offsetHeight });
      }
    }

    // 初始挂载时发送一次
    sendSizeToParent()

    // 创建 ResizeObserver 监听元素大小变化
    const resizeObserver = new ResizeObserver(() => {
      sendSizeToParent()
    })

    // 开始观察
    if (panelRef.current) {
      resizeObserver.observe(panelRef.current)
    }

    // 监听窗口大小变化
    window.addEventListener('resize', sendSizeToParent)

    // 清理函数
    return () => {
      resizeObserver.disconnect()
      window.removeEventListener('resize', sendSizeToParent)
    }
  }, [])

  // 添加调试信息
  useEffect(() => {
    console.log('🚀 Panel组件挂载，位置:', currentPosition)

    // 延迟检查DOM元素状态
    setTimeout(() => {
      const panelElement = document.querySelector('[tabindex="-1"]')
      if (panelElement) {
        console.log('✅ Panel元素已正确渲染，bounds:', panelElement.getBoundingClientRect())
      }
    }, 100)
  }, [])

  console.log('🎨 Panel render - currentPosition:', currentPosition, 'isExpend:', isExpend)

  return (
    <motion.div
      drag
      dragMomentum={false}
      dragElastic={0}
      // 设置初始位置，避免拖拽跳跃
      initial={{
        x: currentPosition.x,
        y: currentPosition.y,
      }}
      animate={{
        x: currentPosition.x,
        y: currentPosition.y,
      }}
      transition={{duration: 0}} // 禁用动画，避免与拖拽冲突
      onDragEnd={handleDragEnd}
      style={{
        position: 'fixed', // 强制设置position
        top: 0,
        left: 0,
        zIndex: 10, // 确保在最顶层
        width: isExpend ? '300px' : '200px',
        maxHeight: 'calc(100vh - 40px)', // 添加最大高度约束，留出一些边距
        height: 'auto', // 确保高度自适应
      }}
      tabIndex={-1}
      className={`overflow-hidden flex flex-col bg-white rounded-sm transition-[width,height] ease-[cubic-bezier(0.78,0,0.17,1)] duration-100 shadow-[0_4px_6px_-1px_rgba(0,0,0,0.1),0_2px_4px_-2px_rgba(0,0,0,0.1)]`}
    >
      <article ref={panelRef} className="flex flex-col h-full min-h-0">
        <header
          className={`w-full flex items-center border-b border-gray-200 transition-padding flex-shrink-0 ${isExpend ? 'px-[16px] py-[7px]' : 'px-[14px] py-[5px]'}`}
        >
          <img src={Logo} className="object-contain h-[24px]" alt="logo" />
          <UserButton className="ml-auto mr-2" />
          {/* {isExpend && <React.Fragment>
            <Tabs 
              value={getCurrentViewType()} 
              onValueChange={handleViewChange}
              className="mx-4 cursor-pointer"
            >
              <TabsList>
                <TabsTrigger value="code">Code</TabsTrigger>
                <TabsTrigger value="design">Design</TabsTrigger>
              </TabsList>
            </Tabs>
          </React.Fragment>} */}
          {isExpend && <Menu />}
          <span
            className={`${isExpend ? 'ml-[10px]' : ''} cursor-pointer text-gray-500 hover:text-gray-700`}
            onClick={switchPanelSize}
          >
            {isExpend ? (
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path
                  fill-rule="evenodd"
                  d="M.172 15.828a.5.5 0 0 0 .707 0l4.096-4.096V14.5a.5.5 0 1 0 1 0v-3.975a.5.5 0   0 0-.5-.5H1.5a.5.5 0 0 0 0 1h2.768L.172 15.121a.5.5 0 0 0 0 .707zM15.828.172a.5.5 0 0 0-.707 0l-4.096 4.096V1.5a.5.5 0 1 0-1 0v3.975a.5.5 0 0 0 .5.5H14.5a.5.5 0 0 0 0-1h-2.768L15.828.879a.5.5 0 0 0 0-.707z"
                />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path
                  fill-rule="evenodd"
                  d="M5.828 10.172a.5.5 0 0 0-.707 0l-4.096 4.096V11.5a.5.5 0 0 0-1 0v3.975a.5.5 0 0 0 .5.5H4.5a.5.5 0 0 0 0-1H1.732l4.096-4.096a.5.5 0 0 0 0-.707zm4.344-4.344a.5.5 0 0 0 .707 0l4.096-4.096V4.5a.5.5 0 1 0 1 0V.525a.5.5 0 0 0-.5-.5H11.5a.5.5 0 0 0 0 1h2.768l-4.096 4.096a.5.5 0 0 0 0 .707z"
                />
              </svg>
            )}
          </span>
        </header>

        {/* Panel内容区域，直接渲染Layout传来的分层children */}
        <div className={`flex-1 min-h-0 flex flex-col ${!isExpend && 'hidden'}`}>{children}</div>
      </article>
    </motion.div>
  )
}

export default Panel
