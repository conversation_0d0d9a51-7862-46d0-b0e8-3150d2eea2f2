import {<PERSON>Down, <PERSON><PERSON>, <PERSON>an<PERSON>ye} from 'lucide-react'
import {ActionType, useAppLogic} from '@/components/AppProvider'
import {Button} from '@/components/ui/button'
import {Modal} from '@/components/ui/dialog/Modal'
import {DropdownMenu, DropdownMenuContent, DropdownMenuTrigger} from '@/components/ui/dropdown-menu'
import {Popover, PopoverContent, PopoverTrigger} from '@/components/ui/popover'
import {Skeleton} from '@/components/ui/skeleton'
import Tooltip from '@/components/ui/toolTip'
import Auth from '@/lib/auth'
import dimensionStore from '@/store/dimensionStore'

const Header = () => {
  const {
    currentNodeId,
    previewUrl,
    isLoading,
    clearTokenTooltip,
    popoverOpen,
    drapDwonOpen,
    setDrapDwonOpen,
    curUrl,
    handleZulu,
    handlePopover,
    handleAction,
    getCurUrl,
    handleClearToken,
  } = useAppLogic()

  return (
    <>
      <header className="flex items-center mb-2 px-3 pt-4">
        <div className={`text-sm flex-auto`}>
          当前选中节点：
          <Popover onOpenChange={open => open && getCurUrl()}>
            <PopoverTrigger>
              <span className={`font-bold`}>{currentNodeId ? currentNodeId : '暂无'}</span>
            </PopoverTrigger>
            <PopoverContent className="break-words overflow-y-auto max-h-[400px]">{curUrl}</PopoverContent>
          </Popover>
        </div>

        <Popover open={popoverOpen}>
          <PopoverTrigger>
            {currentNodeId && (
              <DropdownMenu open={drapDwonOpen}>
                <DropdownMenuTrigger asChild>
                  <div
                    className="cursor-pointer relative z-10 flex items-center justify-center gap-1.5"
                    title="生成代码"
                    onClick={async event => {
                      event.stopPropagation()
                      const hasAuth = await Auth.impl.checkAuthStatus()
                      console.log(hasAuth, 'hasAuth')
                      if (!hasAuth.isAuthenticated) {
                        setDrapDwonOpen(true)
                        return
                      } else {
                        dimensionStore.setGlobalSettings({personalToken: hasAuth.token})
                      }
                      handlePopover()
                    }}
                  >
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {/* 生成代码 */}
                  </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56 bg-gray-100 flex">
                  <div className="px-3 py-2 text-sm text-black">获取Token以访问设计稿</div>
                  <div className="p-2">
                    <button
                      onClick={async () => {
                        setDrapDwonOpen(false)
                        Auth.impl.gotoAuth().then((authRes: {token: string}) => {
                          if (authRes?.token) {
                            dimensionStore.setGlobalSettings({personalToken: authRes.token})
                            handlePopover()
                          }
                        })
                      }}
                      className="cursor-pointer"
                    >
                      <span className="relative z-10 flex items-center justify-center gap-1.5">
                        <svg className="w-3.5 h-3.5 animate-pulse" fill="currentColor" viewBox="0 0 20 20">
                          <path
                            fillRule="evenodd"
                            d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z"
                            clipRule="evenodd"
                          />
                        </svg>
                        {/* 获取Token
                        <svg
                          className="w-3.5 h-3.5 transition-transform group-hover:translate-x-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M13 7l5 5m0 0l-5 5m5-5H6"
                          />
                        </svg> */}
                      </span>
                    </button>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </PopoverTrigger>
          <PopoverContent>
            <div className="mb-2 flex justify-between items-center">
              <span>选中预览</span>
              <Tooltip open={clearTokenTooltip !== ''} theme="white" delayDuration={0} content={clearTokenTooltip}>
                <Button
                  variant="ghost"
                  size="sm"
                  className="cursor-pointer text-red-500 hover:text-red-600"
                  onClick={handleClearToken}
                  title="清除Token"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    className="lucide lucide-brush-cleaning-icon lucide-brush-cleaning"
                  >
                    <path d="m16 22-1-4" />
                    <path d="M19 13.99a1 1 0 0 0 1-1V12a2 2 0 0 0-2-2h-3a1 1 0 0 1-1-1V4a2 2 0 0 0-4 0v5a1 1 0 0 1-1 1H6a2 2 0 0 0-2 2v.99a1 1 0 0 0 1 1" />
                    <path d="M5 14h14l1.973 6.767A1 1 0 0 1 20 22H4a1 1 0 0 1-.973-1.233z" />
                    <path d="m8 22 1-4" />
                  </svg>
                  {/* 清除Token */}
                </Button>
              </Tooltip>
            </div>
            <div className={'max-h-50 overflow-auto bg-gray-100'}>
              {isLoading && (
                <Skeleton className="h-[125px] w-[250px] rounded-xl flex items-center justify-center">
                  加载预览图中
                </Skeleton>
              )}
              {!isLoading && (
                <img crossOrigin="anonymous" src={previewUrl} className={'min-h-30 object-contain align-middle'}></img>
              )}
            </div>
            <div className="flex justify-center mt-2">
              <Tooltip content="下载文件到本地">
                <Button
                  variant={'secondary'}
                  type={'button'}
                  className="mr-2 text-gray-600 cursor-pointer"
                  onClick={() => handleAction(ActionType.DOWNLOAD_FILE)}
                >
                  <FileDown />
                </Button>
              </Tooltip>
              <Tooltip content="预览HTML">
                <Button
                  variant={'secondary'}
                  type={'button'}
                  className="mr-2 text-gray-600 cursor-pointer"
                  onClick={() => handleAction(ActionType.PREVIEW_HTML)}
                >
                  <ScanEye />
                </Button>
              </Tooltip>
              <Tooltip content="跳转到Comate生成代码">
                {/* <Button
                  variant={'secondary'}
                  type={"button"}
                  className="mr-2 text-gray-600 cursor-pointer"
                  onClick={handleZulu}
                > */}
                <img
                  className="cursor-pointer"
                  src="https://zhuiya.bs2cdn.yy.com/f2c/10000/18479/10491/db022da25157445d8d04b57417a3f86a.png"
                  height={36}
                  width={36}
                  onClick={handleZulu}
                />
                {/* </Button> */}
              </Tooltip>
              {/* <Tooltip
                theme="white"
                delayDuration={0}
                content={
                  <div className="flex flex-col gap-2 py-1 w-full min-w-[120px]">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-center cursor-pointer bg-blue-400/10 hover:bg-blue-400/20 text-blue-600 border border-blue-200 shadow-sm w-full"
                      onClick={() => handleAction(ActionType.DOWNLOAD_FILE)}
                    >
                      下载文件到本地
                    </Button>
                  </div>
                }
              >

              </Tooltip> */}
              {/* <Button className={'bg-blue-400 cursor-pointer'} onClick={() => handleZulu()}>
                跳转到Comate
              </Button> */}
              {/* <Tooltip
                theme="white"
                delayDuration={0}
                content={
                  <div className="flex flex-col gap-2 py-1 w-full min-w-[120px]">
                    <h4 className="text-xs text-gray-500 mb-1 font-medium text-center">选择生成模式</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-center cursor-pointer bg-blue-400/10 hover:bg-blue-400/20 text-blue-600 border border-blue-200 shadow-sm w-full"
                      onClick={() => handleZulu('accuracy')}
                    >
                      还原度优先
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-center cursor-pointer bg-green-400/10 hover:bg-green-400/20 text-green-600 border border-green-200 shadow-sm w-full"
                      onClick={() => handleZulu('structure')}
                    >
                      结构优先
                    </Button>
                  </div>
                }
              >
                <Button className={'bg-blue-400 cursor-pointer'} onClick={() => handleZulu()}>
                  跳转到Comate
                </Button>
              </Tooltip> */}
            </div>
          </PopoverContent>
        </Popover>
      </header>
      <Modal
        open={!!dimensionStore.state.showSettingsModal}
        onOpenChange={open => !open && dimensionStore.setModalShow(null)}
        {...dimensionStore.state.showSettingsModal}
      ></Modal>
    </>
  )
}

export default Header
