import type {Node} from '@figma/rest-api-spec'
import {Direction} from './f2c/direction'
import {Line} from './f2c/line'
import type {RestApiAdapter} from './restApiAdapter'
import {type Attributes, type BoxCoordinates, PostionalRelationship} from './type'

export const computePositionalRelationship = (
  currentCoordinates: BoxCoordinates,
  targetCoordinates: BoxCoordinates,
): PostionalRelationship => {
  if (
    targetCoordinates.leftTop.y === currentCoordinates.leftTop.y &&
    targetCoordinates.leftTop.x === currentCoordinates.leftTop.x &&
    targetCoordinates.rightBot.x === currentCoordinates.rightBot.x &&
    targetCoordinates.rightBot.y === currentCoordinates.rightBot.y
  ) {
    return PostionalRelationship.COMPLETE_OVERLAP
  }

  if (
    targetCoordinates.leftTop.y >= currentCoordinates.leftTop.y &&
    targetCoordinates.leftTop.x >= currentCoordinates.leftTop.x &&
    targetCoordinates.rightBot.x <= currentCoordinates.rightBot.x &&
    targetCoordinates.rightBot.y <= currentCoordinates.rightBot.y
  ) {
    return PostionalRelationship.INCLUDE
  }

  if (doOverlap(currentCoordinates, targetCoordinates)) {
    return PostionalRelationship.OVERLAP
  }

  return PostionalRelationship.OUTSIDE
}

export const doOverlap = (currentCoordinate: BoxCoordinates, targetCoordinates: BoxCoordinates): boolean => {
  return findIntersection(currentCoordinate, targetCoordinates)
}

function findIntersection(rectangle1: BoxCoordinates, rectangle2: BoxCoordinates): boolean {
  const xOverlap = Math.max(
    0,
    Math.min(rectangle1.rightBot.x, rectangle2.rightBot.x) - Math.max(rectangle1.leftTop.x, rectangle2.leftTop.x),
  )
  const yOverlap = Math.max(
    0,
    Math.min(rectangle1.rightBot.y, rectangle2.rightBot.y) - Math.max(rectangle1.leftTop.y, rectangle2.leftTop.y),
  )

  return xOverlap !== 0 && yOverlap !== 0
}

export const areThereOverlappingByDirection = (nodes: Node[], direction: Direction): boolean => {
  for (let i = 0; i < nodes.length; i++) {
    const currentNode = nodes[i]
    const currentLine = getLineBasedOnDirection(currentNode, direction)

    for (let j = 0; j < nodes.length; j++) {
      if (i === j) continue
      const targetNode = nodes[j]
      const targetLine = getLineBasedOnDirection(targetNode, direction)

      if (currentLine.overlap(targetLine, -4)) {
        return true
      }
    }
  }
  return false
}

export const getLineBasedOnDirection = (node: Node, direction: Direction) => {
  const coordinates = getBoxCoordinatesFromNode(node)

  if (direction === Direction.HORIZONTAL) {
    return new Line(coordinates.leftTop.y, coordinates.rightBot.y)
  }
  return new Line(coordinates.leftTop.x, coordinates.rightBot.x)
}

export const getBoxCoordinatesFromNode = (node: Node): BoxCoordinates => {
  const boundingBox = node.absoluteBoundingBox!

  return {
    leftTop: {
      x: boundingBox.x,
      y: boundingBox.y,
    },
    leftBot: {
      x: boundingBox.x,
      y: boundingBox.y + boundingBox.height,
    },
    rightTop: {
      x: boundingBox.x + boundingBox.width,
      y: boundingBox.y,
    },
    rightBot: {
      x: boundingBox.x + boundingBox.width,
      y: boundingBox.y + boundingBox.height,
    },
  }
}

export const isAutoLayout = (node: Node): boolean => {
  return !!(node.type === 'FRAME' && 'layoutMode' in node && node.layoutMode !== 'NONE')
}

export const getPositionalCssAttributes = (node: RestApiAdapter): Attributes => {
  const restNode = node.node!
  const attributes: Attributes = {}
  const height = node.getComputedCoordinates()?.height + 'px'
  const width = node.getComputedCoordinates()?.width + 'px'

  if (restNode.type === 'FRAME' && isAutoLayout(restNode)) {
    node.addAnnotations('AUTOLAYOUT', true)
    attributes['display'] = 'flex'
    attributes['width'] = width
    attributes['height'] = height

    if ('maxWidth' in restNode && restNode.maxWidth) {
      attributes['max-width'] = restNode.maxWidth + 'px'
      delete attributes['width']
    }

    if ('minWidth' in restNode && restNode.minWidth) {
      attributes['min-width'] = restNode.minWidth + 'px'
      delete attributes['width']
    }

    if ('maxHeight' in restNode && restNode.maxHeight) {
      attributes['max-height'] = restNode.maxHeight + 'px'
      delete attributes['height']
    }

    if ('minHeight' in restNode && restNode.minHeight) {
      attributes['min-height'] = restNode.minHeight + 'px'
      delete attributes['height']
    }

    if ('layoutMode' in restNode) {
      if (restNode.layoutMode === 'HORIZONTAL') {
        attributes['flex-direction'] = 'row'

        if (restNode.primaryAxisSizingMode === 'AUTO') {
          delete attributes['width']
        }

        if (restNode.counterAxisSizingMode === 'AUTO') {
          delete attributes['height']
        }
      }

      if (restNode.layoutMode === 'VERTICAL') {
        attributes['flex-direction'] = 'column'

        if (restNode.primaryAxisSizingMode === 'AUTO') {
          delete attributes['height']
        }

        if (restNode.counterAxisSizingMode === 'AUTO') {
          delete attributes['width']
        }
      }
    }

    if ('layoutWrap' in restNode) {
      switch (restNode.layoutWrap) {
        case 'WRAP':
          attributes['flex-wrap'] = 'wrap'
          break
        case 'NO_WRAP':
          attributes['flex-wrap'] = 'nowrap'
          break
      }
    }

    if ('primaryAxisAlignItems' in restNode) {
      switch (restNode.primaryAxisAlignItems) {
        case 'MIN':
          attributes['justify-content'] = 'flex-start'
          break
        case 'CENTER':
          attributes['justify-content'] = 'center'
          break
        case 'SPACE_BETWEEN':
          if ('children' in restNode && restNode.children.length > 1) {
            attributes['justify-content'] = 'space-between'
          } else {
            attributes['justify-content'] = 'center'
          }
          break
        case 'MAX':
          attributes['justify-content'] = 'flex-end'
          break
      }
    }

    if ('counterAxisAlignItems' in restNode) {
      switch (restNode.counterAxisAlignItems) {
        case 'MIN':
          attributes['align-items'] = 'flex-start'
          break
        case 'CENTER':
          attributes['align-items'] = 'center'
          break
        case 'MAX':
          attributes['align-items'] = 'flex-end'
          break
      }
    }

    if ('itemSpacing' in restNode && restNode.itemSpacing) {
      attributes['gap'] = `${restNode.itemSpacing}px`
    }

    // 处理padding
    const padding = {
      top: restNode.paddingTop,
      right: restNode.paddingRight,
      bottom: restNode.paddingBottom,
      left: restNode.paddingLeft,
    }

    Object.entries(padding).forEach(([key, value]) => {
      if (value) {
        attributes[`padding-${key}`] = `${value}px`
        attributes['box-sizing'] = 'border-box'
      }
    })

    // 居中时处理padding
    if ('layoutMode' in restNode) {
      if (restNode.layoutMode === 'HORIZONTAL') {
        if (restNode.primaryAxisAlignItems === 'CENTER' && padding.left === padding.right) {
          delete attributes['padding-left']
          delete attributes['padding-right']
        }
        if (restNode.counterAxisAlignItems === 'CENTER' && padding.top === padding.bottom) {
          delete attributes['padding-top']
          delete attributes['padding-bottom']
        }
      }
      if (restNode.layoutMode === 'VERTICAL') {
        if (restNode.primaryAxisAlignItems === 'CENTER' && padding.top === padding.bottom) {
          delete attributes['padding-top']
          delete attributes['padding-bottom']
        }
        if (restNode.counterAxisAlignItems === 'CENTER' && padding.left === padding.right) {
          delete attributes['padding-left']
          delete attributes['padding-right']
        }
      }
    }
  }

  return attributes
}

export const getVisibleChildrenRenderingBox = (children: RestApiAdapter[]): BoxCoordinates => {
  let xl = Number.POSITIVE_INFINITY
  let xr = Number.NEGATIVE_INFINITY
  let yt = Number.POSITIVE_INFINITY
  let yb = Number.NEGATIVE_INFINITY

  for (const child of children) {
    const coordinates = child.getComputedCoordinates()!

    if (coordinates.leftTop.x < xl) {
      xl = coordinates.leftTop.x
    }

    if (coordinates.rightBot.x > xr) {
      xr = coordinates.rightBot.x
    }

    if (coordinates.leftTop.y < yt) {
      yt = coordinates.leftTop.y
    }

    if (coordinates.rightBot.y > yb) {
      yb = coordinates.rightBot.y
    }
  }

  return {
    leftTop: {x: xl, y: yt},
    leftBot: {x: xl, y: yb},
    rightTop: {x: xr, y: yt},
    rightBot: {x: xr, y: yb},
  }
}
