// import {JsonAdapter} from '../adapter'
// import {Option} from '../../code/code'
import type {Option} from '@baidu/f2c-plugin-base/dist/types'
// import {SharedPluginDataKeyVarId, SharedPluginDataNamespace} from '../../const'
import type {JsonAdapter} from '../jsonAdapter'
import {type Attributes, type BoxCoordinates, type FilterOption, NodeType, VariableFieldMap} from '../type'
import {isEmpty} from '../utils/utils'

const toOneDecimal = (num: number): number => Math.round(num * 100) / 100

// 过滤background-color属性
export const backgroundColorFilter = (key: string, _: string): boolean => {
  if (key === 'background-color') {
    return false
  }

  return true
}

// 过滤掉与非绝对定位相关的属性
export const absolutePositioningFilter = (key: string, _: string): boolean => {
  const absolutePositioningFilters: string[] = ['position', 'right', 'top', 'left', 'bottom']

  if (absolutePositioningFilters.includes(key)) {
    return true
  }

  return false
}

//  过滤掉与边距无关的属性
export const marignFilter = (key: string, _: string): boolean => {
  const marginFilter: string[] = ['margin-left', 'margin-right', 'margin-top', 'margin-bottom']

  if (marginFilter.includes(key)) {
    return true
  }

  return false
}

// 把小数做处理
export const truncateNumbers = (value: string, key: string): string => {
  if (value.endsWith('px')) {
    const num = Number(value.slice(0, -2))
    if (isNaN(num)) {
      return value
    } else {
      return `${toOneDecimal(num)}px`
    }
  }
  if (key == 'opacity') {
    const num = Number(value)
    if (isNaN(num)) {
      return value
    } else {
      return `${toOneDecimal(num)}`
    }
  }

  // 过滤background-size的情况
  if (value.endsWith('%') && value.split('%').length === 2) {
    const num = Number.parseFloat(value.slice(0, -1))
    if (isNaN(num)) {
      return value
    } else {
      return `${toOneDecimal(num)}%`
    }
  }

  return value
}

// 防止类似 0px、0%、0.05px 这样的值出现在生成的代码中
export const zeroValueFilter = (_: string, value: string): boolean => {
  if (isEmpty(value)) {
    return false
  }

  if (['top', 'left', 'right', 'bottom'].includes(_)) {
    return true
  }

  let nonNegativeNum: string = value
  if (value.startsWith('-')) {
    nonNegativeNum = value.substring(1)
  }

  if (nonNegativeNum.endsWith('px')) {
    const num = Number(nonNegativeNum.slice(0, -2))

    if (!isNaN(num) && toOneDecimal(num) === 0) {
      return false
    }

    return true
  }

  if (value.endsWith('%')) {
    const num = Number(value.slice(0, -2))
    if (!isNaN(num) && toOneDecimal(num) === 0) {
      return false
    }

    return true
  }

  return true
}

export const imgPropsFilter = (key: string, _: string): boolean => {
  const imgPropsFilter: string[] = ['opacity', 'transform', 'border-width', 'border-style', 'border-color']

  if (imgPropsFilter.includes(key)) {
    return false
  }

  return true
}

type FilterFunction = (key: string, value: string) => boolean
type ModifierFunction = (value: string, key: string) => string

// 过滤和修改css属性
export const filterAttributes = (attributes: Attributes, option: FilterOption): Attributes => {
  const copy: Attributes = {}
  const filters: FilterFunction[] = []
  const modifiers: ModifierFunction[] = []

  if (!option.zeroValueAllowed) {
    filters.push(zeroValueFilter)
  }

  if (option.truncateNumbers) {
    modifiers.push(truncateNumbers)
  }

  if (option.excludeBackgroundColor) {
    filters.push(backgroundColorFilter)
  }

  if (option.absolutePositioningFilter) {
    filters.push(absolutePositioningFilter)
  }

  if (option.marginFilter) {
    filters.push(marignFilter)
  }

  if (option.imgPropsFilter) {
    filters.push(imgPropsFilter)
  }

  if (isEmpty(modifiers) && isEmpty(filters)) {
    return attributes
  }

  Object.entries(attributes).forEach(([key, value]) => {
    let pass = true
    for (const filterFunction of filters) {
      pass = pass && filterFunction(key, value)
    }

    if (!pass) {
      return
    }

    let updated: string = value
    for (const modifierFunction of modifiers) {
      updated = modifierFunction(updated, key)
    }

    copy[key] = updated
  })

  return copy
}

// 过滤和修改css值
export const filterCssValue = (cssValue: string, option: FilterOption): string => {
  const modifiers: ModifierFunction[] = []
  const filters: FilterFunction[] = []

  if (!option.zeroValueAllowed) {
    filters.push(zeroValueFilter)
  }

  if (option.absolutePositioningFilter) {
    filters.push(absolutePositioningFilter)
  }

  if (option.truncateNumbers) {
    modifiers.push(truncateNumbers)
  }

  if (isEmpty(modifiers)) {
    return cssValue
  }

  if (isEmpty(cssValue)) {
    return cssValue
  }

  let pass = true
  for (const filterFunction of filters) {
    pass = pass || filterFunction('', cssValue)
  }

  if (!pass) {
    return ''
  }

  let updated: string = cssValue
  for (const modifierFunction of modifiers) {
    updated = modifierFunction(updated)
  }

  return updated
}

//需要合并 getPropsFromNode in src\core\code\generator\cssmodule\generator.ts
export const getCssAttributes = (node: JsonAdapter): Attributes => {
  switch (node.getType()) {
    case NodeType.TEXT:
      return {
        ...{
          ...filterAttributes(node.getPositionalCssAttributes(), {
            absolutePositioningFilter: true,
          }),
          ...filterAttributes(node.getPositionalCssAttributes(), {
            marginFilter: true,
          }),
        },
        ...node.getCssAttributes(),
      }
    case NodeType.GROUP:
      return {
        ...node.getPositionalCssAttributes(),
        ...node.getCssAttributes(),
      }
    case NodeType.VISIBLE:
      return {
        ...node.getCssAttributes(),
        ...node.getPositionalCssAttributes(),
      }
    case NodeType.IMAGE:
      if (isEmpty(node.getChildren())) {
        return {
          ...filterAttributes(node.getCssAttributes(), {
            excludeBackgroundColor: true,
          }),
          ...filterAttributes(node.getPositionalCssAttributes(), {
            absolutePositioningFilter: true,
          }),
          ...filterAttributes(node.getPositionalCssAttributes(), {
            marginFilter: true,
          }),
        }
      }

      return {
        ...node.getPositionalCssAttributes(),
        ...filterAttributes(node.getCssAttributes(), {
          excludeBackgroundColor: true,
        }),
      }
    case NodeType.IMAGEBACKGROUND: {
      return {
        ...node.getPositionalCssAttributes(),
        ...node.getCssAttributes(),
      }
    }
  }
  return {}
}

export const getClassNameForNode = (node: JsonAdapter | string, option: Option) => {
  const id = typeof node === 'string' ? node : node.getId()
  const name = typeof node === 'string' ? '' : node.getName()
  const nameSplitArr = name.split('-')

  const stylesClassName = typeof node === 'string' ? node : node.getStylesClassName()
  const className = option.layerNameAsClassName
    ? typeof node === 'string'
      ? node
      : nameSplitArr[nameSplitArr.length - 1]
    : stylesClassName
      ? stylesClassName
      : id
  return className
}

export const shouldUseAsBackgroundImage = (node: JsonAdapter): boolean => {
  if (node.getType() === NodeType.IMAGE && !isEmpty(node.getChildren())) {
    return true
  }
  // if (
  //   isBackgroundGradient(node)
  //   // &&
  //   // !isEmpty(node.getChildren())
  // ) {
  //   return true;
  // }
  return false
}
export const isBackgroundGradient = (node: JsonAdapter) => {
  return (
    (node.getType() === NodeType.VISIBLE || node.getType() === NodeType.GROUP) &&
    node.getFills()?.some(fill => fill.type !== 'SOLID')
    // && node.areThereOverflowingChildren()
  )
}

export const orderAGreaterThanB = (a: string, b: string) => {
  const aArr = a.split('-')
  const bArr = b.split('-')
  if (aArr.length === bArr.length) {
    let temp = true
    for (let i = 0; i < aArr.length; i++) {
      if (Number(aArr[i]) < Number(bArr[i])) {
        temp = false
      }
    }
    return temp
  } else if (aArr.length < bArr.length) {
    /* if (Number(aArr[aArr.length - 1]) < Number(bArr[aArr.length - 1])) {
      return false;
    } else {
      return true;
    } */
    return true
  } else {
    /* if (Number(aArr[bArr.length - 1]) < Number(bArr[bArr.length - 1])) {
      return false;
    } else {
      return true;
    } */
    return false
  }
}

export const computeArrOrderList = (nodes: JsonAdapter[]) => {
  const orderList: {index: number; order: string}[] = []
  if (!Array.isArray(nodes) || nodes.length === 0) {
    return nodes
  }
  for (let i = 0; i < nodes.length; i++) {
    const order = nodes[i].getOrder()
    orderList[i] = {
      index: i,
      order,
    }
  }

  const n = orderList.length

  for (let i = 0; i < n - 1; i++) {
    for (let j = 0; j < n - i - 1; j++) {
      if (orderAGreaterThanB(orderList[j].order, orderList[j + 1].order)) {
        // 交换相邻的两个元素
        const temp = {...orderList[j]}
        orderList[j] = {...orderList[j + 1]}
        orderList[j + 1] = temp
      }
    }
  }

  // // console.log(orderList);
  return orderList
}

export const computeChildrenOrderList = (node: JsonAdapter) => {
  const orderList: {index: number; order: string}[] = []
  const children = node.getChildren()
  for (let i = 0; i < children.length; i++) {
    const order = children[i].getOrder()
    orderList[i] = {
      index: i,
      order,
    }
  }

  const n = orderList.length

  for (let i = 0; i < n - 1; i++) {
    for (let j = 0; j < n - i - 1; j++) {
      if (orderAGreaterThanB(orderList[j].order, orderList[j + 1].order)) {
        // 交换相邻的两个元素
        const temp = {...orderList[j]}
        orderList[j] = {...orderList[j + 1]}
        orderList[j + 1] = temp
      }
    }
  }

  // // console.log(orderList);
  return orderList
}

// export function isThemeBgNode(node: SceneNode) {
//   const variableID = node.getSharedPluginData(SharedPluginDataNamespace, SharedPluginDataKeyVarId)
//   return !!variableID
// }
// export function addThemeVariable(adNode: JsonAdapter) {
//   const figmaNode = adNode.node
//   if (!figmaNode) {
//     return
//   }

//   const boundVariables = figmaNode.boundVariables
//   const isBgNode = shouldUseAsBackgroundImage(adNode)
//   const cssAttributes = isBgNode || boundVariables ? adNode.getCssAttributes() : {}

//   if (boundVariables) {
//     // // console.log('[Theme] addThemeVariable CssAttributes', cssAttributes)
//     const boundVariableKeys = Object.keys(boundVariables)
//     boundVariableKeys.forEach(variableKey => {
//       const boundVariableValue = boundVariables[variableKey]
//       if (variableKey == 'fills') {
//         const variableID = boundVariableValue[0].id
//         addThemeProperty(adNode, cssAttributes, 'color', variableID)
//         addThemeProperty(adNode, cssAttributes, 'background-color', variableID)
//       } else if (variableKey == 'strokes') {
//         const variableID = boundVariableValue[0].id
//         addThemeProperty(adNode, cssAttributes, 'border-color', variableID)
//       } else {
//         const propertyName = VariableFieldMap[variableKey]
//         // // console.log('[Theme] Radius key', variableKey, propertyName)
//         if (propertyName) {
//           const variableID = boundVariableValue.id
//           const isExist = addThemeProperty(adNode, cssAttributes, propertyName, variableID)
//           if (!isExist && variableKey.endsWith('Radius')) {
//             addThemeProperty(adNode, cssAttributes, 'border-radius', variableID)
//           }
//         }
//       }
//     })
//   }

//   if (isBgNode && cssAttributes['background-image'] != undefined) {
//     const variableID = figmaNode.getSharedPluginData(SharedPluginDataNamespace, SharedPluginDataKeyVarId)
//     if (variableID) {
//       addThemeProperty(adNode, cssAttributes, 'background-image', variableID)
//     }
//   }

//   // if (boundVariableKeys.length > 0) {
//   //   // console.log('[Theme] node boundVariables', boundVariableKeys, 'propertys', Object.keys(adNode.boundVariables))
//   // }
// }

export const selectBox = (node: JsonAdapter): BoxCoordinates => {
  return node.getComputedCoordinates()!
}
