export interface Bucket {
  id: number;
  name: string;
  bucket: string;
  cdnDomain: string;
  pathPrefix?: string;
}

export interface F2cBusiness {
  id: number;
  name: string;
  remark: string;
  createUid: number;
  createUsername: string;
  createTime: string;
  lastOpUid: number;
  lastOpUsername: string;
  lastOpTime: string;
  buckets: Bucket[];
  baseUrl: string[];
  sysFontFamily: string[];
}