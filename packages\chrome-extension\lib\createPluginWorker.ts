// Plugin Worker factory - Chrome Extension compatible version
import type {PendingRequest, RequestMessage, ResponseMessage} from './workerTypes'

// Types for plugin evaluation
export interface PluginEvaluationPayload {
  pluginCode: string
}

export interface PluginEvaluationResponse {
  pluginName?: string
}

let id = 0

const pending = new Map<number, PendingRequest<any>>()

// Get worker code as string - but keep it organized
async function getWorkerCode(): Promise<string> {
  try {
    // Try to import the worker file as text
    const workerModule = await import('./pluginWorker?raw')
    return workerModule.default
  } catch (error) {
    console.warn('Failed to load worker as module, falling back to fetch:', error)

    // Fallback: fetch the worker file
    try {
      const response = await fetch(new URL('./pluginWorker.ts', import.meta.url))
      return await response.text()
    } catch (fetchError) {
      console.error('Failed to fetch worker file:', fetchError)
      throw new Error('Cannot load worker code')
    }
  }
}

// Create plugin worker requester - Chrome Extension compatible
export function createPluginWorkerRequester() {
  console.log('🔧 Creating worker using Chrome Extension compatible method...')

  return {
    evaluate: async (payload: PluginEvaluationPayload): Promise<PluginEvaluationResponse> => {
      console.log('🔧 Starting worker evaluation...')

      let worker: Worker | null = null
      let workerUrl: string | null = null

      // Helper function to cleanup resources
      const cleanup = () => {
        if (worker) {
          console.log('🔧 Terminating worker...')
          worker.terminate()
        }
        if (workerUrl) {
          URL.revokeObjectURL(workerUrl)
        }
      }

      try {
        // Get worker code
        console.log('🔧 Loading worker code...')
        const workerCode = await getWorkerCode()

        // Create worker from blob
        console.log('🔧 Creating worker from blob...')
        const workerBlob = new Blob([workerCode], {type: 'application/javascript'})
        workerUrl = URL.createObjectURL(workerBlob)
        worker = new Worker(workerUrl)

        console.log('🔧 Worker created successfully')

        return new Promise((resolve, reject) => {
          const currentId = id++
          const timeoutId = setTimeout(() => {
            console.log('🔧 Worker evaluation timeout, cleaning up...')
            cleanup()
            reject(new Error('Plugin evaluation timeout'))
          }, 10000)

          worker!.onmessage = ({data}: MessageEvent<ResponseMessage<PluginEvaluationResponse>>) => {
            console.log('🔧 Received worker message:', data)
            clearTimeout(timeoutId)

            if (data.id === currentId) {
              // Clean up after receiving response
              cleanup()

              if (data.error) {
                console.error('🔧 Worker returned error:', data.error)
                const errorMessage =
                  data.error instanceof Error
                    ? data.error.message
                    : typeof data.error === 'object' && data.error !== null && 'message' in data.error
                      ? (data.error as any).message
                      : 'Plugin evaluation failed'
                reject(new Error(errorMessage))
              } else {
                console.log('🔧 Worker returned success:', data.payload)
                resolve(data.payload!)
              }
            }
          }

          worker!.onerror = error => {
            console.error('🔧 Worker error event:', error)
            clearTimeout(timeoutId)
            cleanup()

            // Try to get more detailed error information
            let errorMessage = 'Unknown worker error'
            if (error instanceof ErrorEvent) {
              errorMessage = error.message || error.error?.message || 'Worker execution error'
              console.error('🔧 Detailed error:', {
                message: error.message,
                filename: error.filename,
                lineno: error.lineno,
                colno: error.colno,
                error: error.error,
              })
            }

            reject(new Error(`Worker error: ${errorMessage}`))
          }

          worker!.onmessageerror = error => {
            console.error('🔧 Worker message error:', error)
            clearTimeout(timeoutId)
            cleanup()
            reject(new Error('Worker message serialization error'))
          }

          const message: RequestMessage<PluginEvaluationPayload> = {id: currentId, payload}
          console.log('🔧 Sending message to worker:', message)

          try {
            worker!.postMessage(message)
            console.log('🔧 Message sent successfully, waiting for response...')
          } catch (postError) {
            console.error('🔧 Failed to post message to worker:', postError)
            clearTimeout(timeoutId)
            cleanup()
            reject(new Error(`Failed to send message to worker: ${postError}`))
          }
        })
      } catch (error) {
        console.error('🔧 Failed to create worker:', error)
        cleanup()
        throw new Error(`Failed to create worker: ${error}`)
      }
    },

    terminate: () => {
      console.log('🔧 Worker requester cleanup completed')
      pending.clear()
    },
  }
}
