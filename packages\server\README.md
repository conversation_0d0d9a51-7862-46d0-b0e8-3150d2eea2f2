# figma-restful-server

## API 文档
> 获取 转换后 的 html
`http://localhost:3000/api/nodes?fileKey=DkzGbKo09kf2w1ytMPALxd&nodeIds=80-1956&format=html`

### test case
https://www.figma.com/design/q40bzrJSSMlqgGHYxJVXXP/2025-%E4%B8%8A%E5%8D%8A%E5%B9%B4-%7C-%E4%BA%92%E5%8A%A8%E7%8E%A9%E6%B3%95?node-id=526-12275&t=XqNBBGH8Xi5fflHU-4


# RestAPI 发布工作流
workspace根目录执行
```
npm run build
```
3. 上传到治理服务发布
将 `server/dist/app`产物上传到
https://s.sysop.yy.com/service/overview/3@friend@i2c-server/release/container

1. 镜像Tab - 新建镜像 - 文件管理 - 上传文件 - loading完毕后 - Git构建
2. Gitlab-CI 点击刚刚创建的pipeline,执行
3. 镜像Tab - 镜像构建成功
4. 部署Tab - 选择最新构建的镜像 - 发布