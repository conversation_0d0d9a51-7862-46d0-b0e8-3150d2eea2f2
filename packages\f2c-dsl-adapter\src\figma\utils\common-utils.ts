export const withTimeout = (
  onSuccess: {
    (response: {status: string; data: Record<string, string>; error?: string | undefined}): void
    // (response: { status: string; data: Record<string, string>; error?: string | undefined }): void
    // (response: { error: any }): void
    // (response: { error: any }): void
    // (response: any): void
    apply?: any
  },
  onTimeout: {(): void; (): void; (): void; (): void; (): void; (): void},
  timeout: number,
) => {
  let called = false

  const timer = setTimeout(() => {
    if (called) {
      return
    }
    called = true
    onTimeout()
  }, timeout)

  return (...args: any) => {
    if (called) {
      return
    }
    called = true
    clearTimeout(timer)
    onSuccess.apply(this, args)
  }
}

export const getObjType = (obj: any) => {
  return Object.prototype.toString.call(obj).slice(8, -1)
}

export function getImgFormat(format?: 'PNG' | 'SVG' | 'JPG') {
  switch (format?.toLocaleUpperCase()) {
    case 'JPG':
      return 'jpeg'
    case 'PNG':
      return 'png'
    case 'SVG':
      return 'svg+xml'
    default:
      return 'png'
  }
}
