import { Button } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import loginStore from "@/store/loginStore"
import { zodResolver } from "@hookform/resolvers/zod"
import { Shield, User } from "lucide-react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { z } from "zod"

const formSchema = z.object({
  username: z.string()
    .min(1, { message: "请输入账号" })
    .or(z.string().email({ message: "请输入有效的邮箱地址" })),
    // .or(z.string().regex(/^90\d+$/, { message: "请输入90开头的YY号" })),
    // .or(z.string().regex(/^[^\s@]+$/, { message: "请输入有效的工号" })),
  dynamicToken: z.string()
    .min(6, { message: "动态口令必须是6位数字" })
    .max(6, { message: "动态口令必须是6位数字" })
    .regex(/^\d{6}$/, { message: "动态口令只能包含数字" }),
})

export function YYLoginForm() {
  const {setLoginInfoToStorage,getUserInfo} = loginStore.state
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: "",
      dynamicToken: "",
    },
    mode: 'onChange', // 启用实时验证
  })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    const response = await fetch('https://figma-plugin.yy.com/figma-plugin/login',{
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        username: values.username,
        dynamicToken: values.dynamicToken,
      }),
    })
    if(!response.ok) {
      return toast.error(`网络错误: ${response.statusText}`)
    }
    const res = await response.json()
    if(res.code !== "0" || !res.data) {
      return toast.error(`登录失败: ${res.message}`)
    }

    const {figmaIdentityInfo = '', figmaIdentitySign = ''} = res.data
    setLoginInfoToStorage({figmaIdentityInfo, figmaIdentitySign})
    toast.promise(getUserInfo(figmaIdentityInfo, figmaIdentitySign), {
      loading: '登录中...',
      success: '登录成功!',
      error: '登录失败',
    })
  }

  // 获取表单状态
  const { isSubmitting } = form.formState

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input placeholder="yy邮箱/90开头YY号/工号" className="pl-10 h-11 box-border" {...field} />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="dynamicToken"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="relative">
                  <Shield className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    type="text"
                    placeholder="动态口令"
                    className="pl-10 h-11 box-border"
                    maxLength={6}
                    {...field}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          type="submit"
          className="w-full mt-8 h-11 text-base font-semibold"
          disabled={isSubmitting}
        >
          {isSubmitting ? "登录中..." : "登录"}
        </Button>
      </form>
    </Form>
  )
}
