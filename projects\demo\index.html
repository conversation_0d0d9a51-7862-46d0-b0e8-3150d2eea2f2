<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Figma Design Implementation</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
  <div class="container mx-auto p-4">
    <div class="bg-black bg-opacity-60 rounded-xl p-4 relative">
      <!-- 顶部装饰元素 -->
      <div class="flex justify-center items-center opacity-60 mb-4">
        <div class="w-4 h-4 bg-gradient-to-r from-transparent to-white rounded"></div>
        <div class="w-4 h-4 bg-white rounded-full mx-1"></div>
        <div class="w-4 h-4 bg-gradient-to-l from-transparent to-white rounded"></div>
      </div>
      
      <!-- 标题 -->
      <h1 class="text-white font-medium text-center text-lg tracking-wider mb-4">第一章：重生的觉醒</h1>
      
      <!-- 内容区域 -->
      <div class="text-white text-sm mb-4">
        <p>娜娜是一个十足的恋爱脑，最近她在网上认识并喜欢上一个男生。但是一星期过去了，男生从没有回复过聊天，娜娜还帮他找理由。恋爱脑太严重，容易被男人骗，恋爱脑太严重，很容易被男人骗，真的快劝娜娜分手...</p>
      </div>
      
      <!-- 底部展开按钮 -->
      <div class="flex justify-between items-center">
        <span class="text-rose-200 text-sm">展开</span>
        <div class="w-4 h-4 opacity-30">
          <!-- 箭头图标 -->
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
            <path d="M9 18l6-6-6-6"/>
          </svg>
        </div>
      </div>
      
      <!-- 底部装饰线 -->
      <div class="absolute bottom-0 left-0 right-0 h-px bg-rose-200 opacity-60"></div>
    </div>
  </div>
</body>
</html>