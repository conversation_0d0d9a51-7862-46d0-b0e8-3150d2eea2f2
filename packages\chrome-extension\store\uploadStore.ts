import { getF2cBusinessList } from '@/api/api'
import { BaseStore } from './baseStore'
import loginStore from './loginStore'
import { Bucket, F2cBusiness } from '@/types/BusinessType'
import { ExtensionBridge } from '@/lib/auth'

export class BusinessStore extends BaseStore {
    constructor() {
        super()
    }
    selectedBusiness: F2cBusiness | undefined = void 0
    seletedBucket: Bucket | undefined = void 0
    businessList: F2cBusiness[] = []
    getBuisinessList() {
        if (loginStore.isLoggedIn) {
            if (this.businessList.length > 0) {
                return this.businessList
            }
            getF2cBusinessList(loginStore.figmaIdentityInfo, loginStore.figmaIdentitySign).then(res => {
                if (res && res.length > 0) {
                    this.businessList = res
                    // if (!this.selectedBusiness) {
                    //     // 初始化
                    //     this.selectedBusiness = res[0]
                    //     if (!this.seletedBucket) {
                    //         this.seletedBucket = this.selectedBusiness.buckets?.[0]
                    //     }
                    // }
                }
            })
        }
    }

    async getLocalBuisSettings() {
        const resp = await ExtensionBridge.getInstance().sendMessage({
            type: 'getLocalBuisSettings',
            data: {},
        })
        if (resp) {
            this.selectedBusiness = JSON.parse(resp.selectedBusiness)
            this.seletedBucket = JSON.parse(resp.seletedBucket)
        }
    }
    async setLocalBusiSettings() {
        ExtensionBridge.getInstance().sendMessage({
            type: 'setLocalBuisSettings',
            data: {
                selectedBusiness: JSON.stringify(this.selectedBusiness),
                seletedBucket: JSON.stringify(this.seletedBucket)
            }
        })
    }
    setSelectedBusiness(id: string) {
        if (this.businessList.length > 0) {
            this.selectedBusiness = this.businessList.find(item => String(item.id) == id)
            if (this.selectedBusiness?.buckets?.[0].id) {
                this.setSeletedBucket(String(this.selectedBusiness?.buckets?.[0].id))
                this.setLocalBusiSettings()
            }
        }
    }
    setSeletedBucket(id: string) {
        if (this.selectedBusiness) {
            this.seletedBucket = this.selectedBusiness.buckets.find(item => String(item.id) == id)
        } else {
            this.seletedBucket = this.businessList?.[0]?.buckets.find(item => String(item.id) == id)
        }
            this.setLocalBusiSettings()
    }
}

const businessStore = new BusinessStore()
export default businessStore
