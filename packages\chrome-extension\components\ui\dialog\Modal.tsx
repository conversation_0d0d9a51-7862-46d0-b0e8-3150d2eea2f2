import { Button } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import dimensionStore from "@/store/dimensionStore"
import type { DialogProps } from "@radix-ui/react-dialog"
import { useForm } from "react-hook-form"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "../form"
import { Input } from "../input"
import { Label } from "../label"
export type ModalProps ={
  title?: any
  desc?: any
  content?: any
  okText?: any
  onOk?: () => void
} & DialogProps 
export function Modal(props: ModalProps) {
  const onSubmit = (res: any) => {
    console.log(res)
    dimensionStore.setGlobalSettings(res)
    dimensionStore.setModalShow(null)
    props.onOk?.()
  }
  const form = useForm({
    defaultValues: dimensionStore.globalSettings
  })
  return (
    <Dialog {...props}>
      <DialogContent className="sm:max-w-[425px] bg-white">
        <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <DialogHeader>
            <DialogTitle>{props.title || '全局设置'}</DialogTitle>
            <DialogDescription>
              {props.desc || 'F2C Chrome插件的全局配置'}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <FormField
              control={form.control}
              name="personalToken"
              render={({ field }) => (
        
                  <FormItem>
                    <FormLabel className="text-right">personalToken 
                      <span className={'cursor-pointer text-[10px] underline text-blue-300 shrink-0 pl-2'} onClick={async ()=> form.setValue('personalToken', await navigator.clipboard.readText())}>点击粘贴Token</span>
                    </FormLabel>
                    <div className="flex items-center relative">
                      <FormControl>
                        <Input placeholder="输入你的个人token" {...field} className="col-span-3" />
                      </FormControl>
                    </div>

                    <FormDescription>
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
 
              )}
            />
            {props.content}
          </div>
          <DialogFooter>
            <Button type="submit">{props.okText || '保存修改'}</Button>
          </DialogFooter>
        </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
