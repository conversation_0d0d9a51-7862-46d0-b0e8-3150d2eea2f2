# image: harbor.yy.com/front_end/emp:node20.11pnpm8.15
# image: harbor.yy.com/front_end/bun:v2.5pnpm8
image: harbor.yy.com/front_end/bun:1.2.16_nodejs22


stages:
#- build
#- deploy

before_script:

deploy-master:
  tags:
    - webfe
  artifacts:
    expire_in: 1 week
    paths:
      - packages/server/src/
      - packages/server/dist/
  script:
    - echo -e "\n10.18.111.211 registry.npmjs.org registry.yarnpkg.com\n172.29.68.161 registry.npm.baidu-int.com" >> /etc/hosts
    - rm -rf packages/chrome-extension
    - pnpm i
    - pnpm add tsup -w
    - pnpm build
    
  when: manual
