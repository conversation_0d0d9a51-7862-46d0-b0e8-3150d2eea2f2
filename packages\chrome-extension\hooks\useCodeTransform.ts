import { type TransformParams, pluginExecutor } from '@/lib/pluginExecutor'
import pluginStore from '@/store/pluginStore'
import { useMemo } from 'react'

interface UseCodeTransformOptions {
  useRem?: boolean
  rootFontSize?: number
  scale?: number
}

export function useCodeTransform(options: UseCodeTransformOptions = {}) {
  const { activePluginSource } = pluginStore.state
  
  const transformOptions: TransformParams['options'] = useMemo(() => ({
    useRem: options.useRem ?? false,
    rootFontSize: options.rootFontSize ?? 16,
    scale: options.scale ?? 1
  }), [options.useRem, options.rootFontSize, options.scale])

  const transformCSS = useMemo(() => {
    return (code: string, style: Record<string, string> = {}) => {
      return pluginExecutor.transformCSS(code, style, transformOptions)
    }
  }, [transformOptions, activePluginSource])

  const transformJS = useMemo(() => {
    return (code: string, style: Record<string, string> = {}) => {
      return pluginExecutor.transformJS(code, style, transformOptions)
    }
  }, [transformOptions, activePluginSource])

  const hasActivePlugin = useMemo(() => {
    return !!activePluginSource && !!pluginExecutor.getActivePlugin()
  }, [activePluginSource])

  const getActivePluginInfo = useMemo(() => {
    const plugin = pluginExecutor.getActivePlugin()
    return plugin ? {
      name: plugin.name,
      version: plugin.version,
      description: plugin.description,
      hasCSSTransform: !!plugin.code?.css && plugin.code.css !== false,
      hasJSTransform: !!plugin.code?.js && plugin.code.js !== false
    } : null
  }, [activePluginSource])

  return {
    transformCSS,
    transformJS,
    hasActivePlugin,
    getActivePluginInfo,
    transformOptions
  }
}
