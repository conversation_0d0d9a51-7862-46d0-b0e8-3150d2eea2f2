export type CornerRadiusNode =
  | BooleanOperationNode
  | ComponentNode
  | ComponentSetNode
  | EllipseNode
  | FrameNode
  | HighlightNode
  | InstanceNode
  | PolygonNode
  | RectangleNode
  | StarNode
  | VectorNode

export type StrokesNode =
  | BooleanOperationNode
  | ComponentNode
  | ComponentSetNode
  | ConnectorNode
  | EllipseNode
  | FrameNode
  | HighlightNode
  | InstanceNode
  | LineNode
  | PolygonNode
  | RectangleNode
  | ShapeWithTextNode
  | StampNode
  | StarNode
  | TextNode
  | VectorNode
  | WashiTapeNode

export type DashPatternNode =
  | BooleanOperationNode
  | ComponentNode
  | ComponentSetNode
  | ConnectorNode
  | EllipseNode
  | FrameNode
  | HighlightNode
  | InstanceNode
  | LineNode
  | PolygonNode
  | RectangleNode
  | ShapeWithTextNode
  | StampNode
  | StarNode
  | TextNode
  | VectorNode
  | WashiTapeNode

export type FillsNode =
  | BooleanOperationNode
  | ComponentNode
  | ComponentSetNode
  | EllipseNode
  | FrameNode
  | HighlightNode
  | InstanceNode
  | LineNode
  | PolygonNode
  | RectangleNode
  | SectionNode
  | ShapeWithTextNode
  | StampNode
  | StarNode
  | StickyNode
  | TableCellNode
  | TableNode
  | TextNode
  | TextSublayerNode
  | VectorNode
  | WashiTapeNode

export function hasExportSettings(node: DocumentNode | PageNode | SceneNode): node is PageNode | SceneNode {
  return 'exportSettings' in node
}

export function hasChildren(
  node: DocumentNode | PageNode | SceneNode,
): node is
  | FrameNode
  | GroupNode
  | ComponentSetNode
  | ComponentNode
  | InstanceNode
  | BooleanOperationNode
  | SectionNode {
  return 'children' in node
}

export function isSceneNode(node: DocumentNode | PageNode | SceneNode): node is SceneNode {
  return node.type != 'DOCUMENT' && node.type != 'PAGE'
}
