const url = 'https://statistcs-hdpt.yy.com/reportGeneralStatistics'

export enum ReportType {
  codeCount = 'f2c_gen_code_count', // 代码生成次数
  lineCount = 'f2c_code_line_count', // 代码生成行数
  aiCount = 'f2c_ai_count',
  copyCount = 'f2c_copy_count', // 使用复制功能次数
  downloadCutCount = 'f2c_download_cut_count', // 使用切图下载功能次数
  uploadCutCount = 'f2c_upload_cut_count', // 上传切图数
  loginType = 'f2c_login_type', // 登录方式
  uploadCount = 'f2c_upload_count', // 上传/更新次数
  pluginOpenCount = 'f2c_plugin_open_count', // 插件打开数
  userHeartbeat = 'f2c_user_heartbeat', // 用户心跳
  useDSL = 'f2c_use_dsl', // 使用dsl生成
  useTag = 'f2c_use_tag', // 打tag
  useHandoff = 'f2c_hand_off', // 离线生成
  useHandoffFile = 'f2c_hand_off_file', // 使用离线生成文件
}

export async function f2cDataReport(type: ReportType, uiframework: string, count: number, expandObj?: any) {
  const userName = window.INITIAL_OPTIONS?.user_data?.handle || figma?.currentUser?.name || 'chrome_匿名'
  const email = window.INITIAL_OPTIONS?.user_data?.email || ''
  console.log('f2cDataReport', type, email, expandObj)
  const reqData: any = {
    ...expandObj,
    account: userName,
    appid: 1,
    dataType: type,
    dim1: `${userName}-${email}`,
    dim3: uiframework,
    value1: count
  }
  try {
    const resp = await createFetch(3000)(url, {
      method: 'POST',
      mode: 'cors',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json;charset=utf-8',
      },
      body: JSON.stringify(reqData),
    })
    const res = await resp.json()
    // console.log('f2cDataReport', res)
    if (res && res.result != 200) {
      console.log('上报失败', res.reason, res.result)
    }
  } catch (e: any) {
    console.error(e)
  }
}

function createFetch(timeout: number) {
  return (resource: any, options: any) => {
    const controller = new AbortController()
    options = options || {}
    options.signal = controller.signal
    const timerId = setTimeout(() => {
      clearTimeout(timerId)
      controller.abort()
    }, timeout)
    return fetch(url, options)
  }
}