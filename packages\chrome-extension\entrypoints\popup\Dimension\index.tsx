import {useMemo} from 'react'
import CodeSection from '@/components/CodeSection'
import InfoItem from '@/components/Dimension'
import CopyItem from '@/components/Dimension/copyItem'
import {changeUnitDeep} from '@/lib/dimension'
import dimensionStore from '@/store/dimensionStore'
import BoxModel from './boxModel'
import DimensionExt from './Ext'
import ExportCom from './exportCom'

const Dimension = () => {
  const dimensionInfo = dimensionStore.clone(dimensionStore.state.dimensionInfo) || {}
  const {currentMagnification, currentRemBase, currentVwBase, currentSelectUnit} = dimensionStore.state

  // Prepare options for code generation
  const codeOptions = useMemo(
    () => ({
      useRem: currentSelectUnit === 'rem',
      rootFontSize: currentRemBase,
      scale: currentMagnification,
    }),
    [currentSelectUnit, currentRemBase, currentMagnification],
  )

  if (dimensionInfo) {
    changeUnitDeep(dimensionInfo.css, currentSelectUnit, currentMagnification, currentRemBase, currentVwBase)
    changeUnitDeep(dimensionInfo.codeCss, currentSelectUnit, currentMagnification, currentRemBase, currentVwBase)
  }
  const {nodeName, nodeId, css, codeCss} = dimensionInfo

  return (
    <div className="flex flex-wrap justify-center content-start">
      <div className="w-full px-2">
        {css && (
          <InfoItem infoName={nodeName} fold={false}>
            <BoxModel />
          </InfoItem>
        )}
        {codeCss && <CodeSection style={codeCss} options={codeOptions} />}
        {css && (
          <InfoItem infoName={'属性'}>
            {[
              {title: '宽度', value: 'width', unit: true},
              {title: '高度', value: 'height', unit: true},
              {title: '顶部距离', value: 'top', unit: true},
              {title: '左侧距离', value: 'left', unit: true},
              {title: '不透明度', value: 'opacity', unit: false},
            ].map(item => {
              return (
                <CopyItem
                  title={item.title}
                  value={Number.parseFloat(css?.[item.value])}
                  copyValue={Number.parseFloat(css?.[item.value]) + (item.unit ? currentSelectUnit : '')}
                  unit={item.unit ? currentSelectUnit : false}
                  key={item.value}
                />
              )
            })}
          </InfoItem>
        )}
        <DimensionExt />
        {/* {nodeId && (
        <InfoItem infoName={'Export'}>
          <ExportCom />
        </InfoItem>
      )} */}
      </div>
    </div>
  )
}
export default Dimension
