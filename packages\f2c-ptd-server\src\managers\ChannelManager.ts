import type { JoinChannelMessage, SocketMessage } from '../types/messages';
import { logger } from '../log/logger';

export interface ChannelClient {
  ws: WebSocket;
  userId?: string;
  joinedAt: Date;
}

export class ChannelManager {
  private channels: Map<string, Map<string, ChannelClient>> = new Map();
  private clientChannels: Map<WebSocket, Set<string>> = new Map();

  /**
   * 客户端加入频道
   */
  joinChannel(ws: WebSocket, message: JoinChannelMessage): boolean {
    try {
      const { channel, userId } = message;
      const clientId = this.getClientId(ws);

      // 初始化频道
      if (!this.channels.has(channel)) {
        this.channels.set(channel, new Map());
        logger.info(`频道 ${channel} 已创建`);
      }

      // 初始化客户端频道列表
      if (!this.clientChannels.has(ws)) {
        this.clientChannels.set(ws, new Set());
      }

      // 添加客户端到频道
      const channelClients = this.channels.get(channel)!;
      channelClients.set(clientId, {
        ws,
        userId,
        joinedAt: new Date()
      });

      // 记录客户端加入的频道
      this.clientChannels.get(ws)!.add(channel);

      logger.info(`客户端 ${clientId} 加入频道 ${channel}`);
      return true;
    } catch (error) {
      logger.error('加入频道失败:', error);
      return false;
    }
  }

  /**
   * 客户端离开频道
   */
  leaveChannel(ws: WebSocket, channel: string): boolean {
    try {
      const clientId = this.getClientId(ws);
      
      // 从频道中移除客户端
      const channelClients = this.channels.get(channel);
      if (channelClients) {
        channelClients.delete(clientId);
        
        // 如果频道为空，删除频道
        if (channelClients.size === 0) {
          this.channels.delete(channel);
          logger.info(`频道 ${channel} 已删除（无客户端）`);
        }
      }

      // 从客户端频道列表中移除
      const clientChannels = this.clientChannels.get(ws);
      if (clientChannels) {
        clientChannels.delete(channel);
      }

      logger.info(`客户端 ${clientId} 离开频道 ${channel}`);
      return true;
    } catch (error) {
      logger.error('离开频道失败:', error);
      return false;
    }
  }

  /**
   * 客户端断开连接时清理
   */
  removeClient(ws: WebSocket): void {
    try {
      const clientId = this.getClientId(ws);
      const clientChannels = this.clientChannels.get(ws);

      if (clientChannels) {
        // 从所有频道中移除客户端
        clientChannels.forEach((channel) => {
          this.leaveChannel(ws, channel);
        });
        
        // 清理客户端频道记录
        this.clientChannels.delete(ws);
      }

      logger.info(`客户端 ${clientId} 已断开连接并清理`);
    } catch (error) {
      logger.error('清理客户端失败:', error);
    }
  }

  /**
   * 向频道广播消息
   */
  broadcastToChannel(channel: string, message: SocketMessage, excludeWs?: WebSocket): number {
    const channelClients = this.channels.get(channel);
    if (!channelClients) {
      logger.warn(`频道 ${channel} 不存在`);
      return 0;
    }

    let sentCount = 0;
    const messageStr = JSON.stringify(message);

    channelClients.forEach((client, clientId) => {
      if (excludeWs && client.ws === excludeWs) {
        return; // 排除发送者
      }

      if (client.ws.readyState === WebSocket.OPEN) {
        try {
          client.ws.send(messageStr);
          sentCount++;
        } catch (error) {
          logger.error(`向客户端 ${clientId} 发送消息失败:`, error);
          // 移除无效连接
          this.removeClient(client.ws);
        }
      }
    });

    logger.debug(`向频道 ${channel} 广播消息，成功发送给 ${sentCount} 个客户端`);
    return sentCount;
  }

  /**
   * 获取频道信息
   */
  getChannelInfo(channel: string) {
    const channelClients = this.channels.get(channel);
    if (!channelClients) {
      return null;
    }

    return {
      name: channel,
      clientCount: channelClients.size,
      clients: Array.from(channelClients.entries()).map(([id, client]) => ({
        id,
        userId: client.userId,
        joinedAt: client.joinedAt,
        connected: client.ws.readyState === WebSocket.OPEN
      }))
    };
  }

  /**
   * 获取所有频道列表
   */
  getAllChannels() {
    return Array.from(this.channels.keys()).map(channel => this.getChannelInfo(channel));
  }

  /**
   * 获取客户端加入的频道列表
   */
  getClientChannels(ws: WebSocket): string[] {
    const channels = this.clientChannels.get(ws);
    return channels ? Array.from(channels) : [];
  }

  /**
   * 生成客户端ID
   */
  private getClientId(ws: WebSocket): string {
    // 使用 WebSocket 对象的内存地址作为唯一标识
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取统计信息
   */
  getStats() {
    const totalChannels = this.channels.size;
    const totalClients = this.clientChannels.size;
    const activeConnections = Array.from(this.clientChannels.keys())
      .filter(ws => ws.readyState === WebSocket.OPEN).length;

    return {
      totalChannels,
      totalClients,
      activeConnections,
      channels: this.getAllChannels()
    };
  }
}