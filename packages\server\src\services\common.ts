import {logger} from './logger'

// CORS 头信息
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, F2c-Api-Platform',
  'Access-Control-Allow-Credentials': 'true',
}

export class CommonService {
  private static instance: CommonService

  private constructor() {}

  // 单例模式
  static getInstance(): CommonService {
    if (!CommonService.instance) {
      CommonService.instance = new CommonService()
    }
    return CommonService.instance
  }

  // 添加 CORS 头到响应
  addCorsHeaders(response: Response): Response {
    const headers = new Headers(response.headers)
    Object.entries(corsHeaders).forEach(([key, value]) => headers.set(key, value))

    return new Response(response.body, {
      status: response.status,
      headers,
    })
  }

  // 创建JSON响应
  jsonResponse(data: any, status = 200): Response {
    return new Response(JSON.stringify(data), {
      status,
      headers: {'Content-Type': 'application/json'},
    })
  }

  // 创建HTML响应
  htmlResponse(html: string): Response {
    return new Response(html, {
      headers: {'Content-Type': 'text/html; charset=utf-8'},
    })
  }

  // 创建错误响应
  errorResponse(message: string, status = 400): Response {
    return new Response(message, {status})
  }

  // 获取CORS头
  getCorsHeaders(): HeadersInit {
    return corsHeaders
  }

  // 解析 Figma 链接的辅助函数
  parseFigmaLink(link: string) {
    const fileKeyMatch = link.match(/\/design\/([^\/\?]+)/)
    const nodeIdMatch = link.match(/node-id=([^&]+)/)

    return {
      fileKey: fileKeyMatch ? fileKeyMatch[1] : '',
      nodeIds: nodeIdMatch ? nodeIdMatch[1] : '',
    }
  }

  // 添加模板渲染方法
  async renderTemplate(templateName: string, data: Record<string, any>): Promise<string> {
    try {
      // 读取模板文件 - 使用相对于执行目录的路径
      const templatePath = `${process.cwd()}/src/templates/${templateName}.html`
      const templateContent = await Bun.file(templatePath).text()

      // 简单的模板替换
      let renderedContent = templateContent
      for (const [key, value] of Object.entries(data)) {
        renderedContent = renderedContent.replace(new RegExp(`{{\\s*${key}\\s*}}`, 'g'), String(value))
      }

      return renderedContent
    } catch (error) {
      logger.error(`模板渲染失败 (${templateName}):`, error)
      return `<html><body><h1>模板渲染错误</h1><p>${error instanceof Error ? error.message : String(error)}</p></body></html>`
    }
  }
}

// 导出单例实例
export const commonService = CommonService.getInstance()
