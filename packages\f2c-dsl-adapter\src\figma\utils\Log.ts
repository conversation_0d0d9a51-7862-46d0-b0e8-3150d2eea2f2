const Log = {
  // 调试的时候将level设置为0，生产环境设置为-1
  // 如果通过pnpm run test 或者 npm run test 就判定为测试环境，level设置为0
  level: process.env.TEST ? 0 : -1,
  // level: 0,
  name: '[YYF2C]',
  debug(...params) {
    this.level >= 0 && global.console.log(this.name, ...params)
  },

  info(simple: any, ...more) {
    if (this.level >= 1) {
      global.console.log(this.name, simple, ...more)
    }
  },
  infoL(level: number, simple: any, ...more) {
    if (this.level >= level) {
      global.console.log(this.name, simple, ...more)
    } else {
      global.console.log(this.name, simple)
    }
  },
}
export default Log
