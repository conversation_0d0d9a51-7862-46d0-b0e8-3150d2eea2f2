import type {FigmaNodeAdapter} from '../adapter'
import type {BoxCoordinates} from '../type'
import {getContainerLineFromNodes, getLineBasedOnDirection} from './line'
import {absolutePositioningAnnotation} from './overlap'
import {selectBox} from './util'

// Direction表示元素在F2C节点中的定位方式
// VERTICAL 方向表示元素按行排列。它与CSS属性flex-direction: row相对应
// HORIZONTAL 方向表示元素按列组织。它对应于CSS属性flex-direction: column
export enum Direction {
  VERTICAL = 'VERTICAL',
  HORIZONTAL = 'HORIZONTAL',
  ABSOLUTE = 'ABSOLUTE',
}

// 计算节点是用行定位还是用列定位
export const getDirection = (node: FigmaNodeAdapter): Direction => {
  const children: FigmaNodeAdapter[] = node.getChildren()
  if (children.length <= 1) {
    const targetLine = getContainerLineFromNodes(children, Direction.HORIZONTAL)
    const parentLine = getContainerLineFromNodes([node], Direction.HORIZONTAL)

    const counterTargetLine = getContainerLineFromNodes(children, Direction.VERTICAL)
    const counterParentLine = getContainerLineFromNodes([node], Direction.VERTICAL)

    const useHorizontal: boolean =
      Math.abs(parentLine.upper - parentLine.lower - (targetLine.upper - targetLine.lower)) >
      Math.abs(counterParentLine.upper - counterParentLine.lower - (counterTargetLine.upper - counterTargetLine.lower))

    if (useHorizontal) {
      return Direction.HORIZONTAL
    }

    return Direction.VERTICAL
  }

  let noVerticalOverlap = true
  let noHorizontalOverlap = true
  for (let i = 0; i < children.length; i++) {
    const currentLine = getLineBasedOnDirection(children[i], Direction.HORIZONTAL)
    for (let j = 0; j < children.length; j++) {
      if (i === j) {
        continue
      }
      const targetLine = getLineBasedOnDirection(children[j], Direction.HORIZONTAL)
      noVerticalOverlap = noVerticalOverlap && !currentLine.overlap(targetLine, 1)
    }
  }

  for (let i = 0; i < children.length; i++) {
    const currentLine = getLineBasedOnDirection(children[i], Direction.VERTICAL)
    for (let j = 0; j < children.length; j++) {
      if (i === j) {
        continue
      }
      const targetLine = getLineBasedOnDirection(children[j], Direction.VERTICAL)
      noHorizontalOverlap = noHorizontalOverlap && !currentLine.overlap(targetLine, 2)
    }
  }

  if (noVerticalOverlap) {
    return Direction.HORIZONTAL
  }

  if (noHorizontalOverlap) {
    return Direction.VERTICAL
  }

  return Direction.ABSOLUTE
}

// 根据方向对node的位置进行重排序
export const reorderNodesBasedOnDirection = (node, direction: Direction) => {
  if (node.hasAnnotation(absolutePositioningAnnotation)) {
    return
  }

  const children: FigmaNodeAdapter[] = node.getChildren()

  if (direction === Direction.VERTICAL) {
    children.sort((a: FigmaNodeAdapter, b: FigmaNodeAdapter): number => {
      const abox: BoxCoordinates = selectBox(a)
      const bbox: BoxCoordinates = selectBox(b)

      const xa = abox.leftTop.x
      const xb = bbox.leftTop.x

      return xa - xb
    })
    return
  }

  if (direction === Direction.HORIZONTAL) {
    children.sort((a: FigmaNodeAdapter, b: FigmaNodeAdapter): number => {
      const abox: BoxCoordinates = selectBox(a)
      const bbox: BoxCoordinates = selectBox(b)

      const ya = abox.leftTop.y
      const yb = bbox.leftTop.y

      return ya - yb
    })
  }
}

export const getOppositeDirection = (direction: Direction) => {
  if (direction === Direction.VERTICAL) {
    return Direction.HORIZONTAL
  }

  return Direction.VERTICAL
}
