import { useState, useCallback, useRef, useEffect } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { createPortal } from 'react-dom';

interface ISelectItem {
	value: string
	options: { value: string; label: string; disabled?: boolean }[]
	onChange?: (value: string) => void
	variant?: string
}

const Select = (props: ISelectItem) => {
	const { options, onChange } = props;
	const [isOpen, setIsOpen] = useState(false);
	const selectRef = useRef<HTMLDivElement>(null);
	const [position, setPosition] = useState({ top: 0, left: 0, width: 0 });
	const [value, setValue] = useState(props.value);
	const selectedOption = options.find(option => option.value === value) || options[0];

	const handleChange = useCallback((value: string) => {
		console.log(value,)
		setValue(value)
		onChange?.(value);
		setIsOpen(false);
	}, [onChange]);
    useEffect(()=>{
		setValue(props.value)
	},[props.value])
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
				setIsOpen(false);
				event.stopPropagation()
			}
		};
		document.addEventListener('click', handleClickOutside);
		return () => document.removeEventListener('click', handleClickOutside);
	}, []);

	useEffect(() => {
		if (isOpen && selectRef.current) {
			const rect = selectRef.current.getBoundingClientRect();
			setPosition({
				top: rect.bottom + window.scrollY,
				left: rect.left + window.scrollX,
				width: rect.width
			});
		}
	}, [isOpen]);

	return (
		<div className="relative w-full" ref={selectRef}>
			<div
				className="flex items-center justify-between h-6 px-2 py-1 text-sm  bg-gray-50 rounded cursor-pointer hover:bg-gray-100"
				onClick={() => setIsOpen(!isOpen)}
			>
				<span>{selectedOption?.label}</span>
				{isOpen ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
			</div>
			
			{isOpen && createPortal(
				<div 
					className="fixed z-50 overflow-auto bg-white rounded shadow-lg max-h-60"
					style={{
						top: `${position.top}px`,
						left: `${position.left}px`,
						width: `${position.width}px`
					}}
					onClick={(e) => e.stopPropagation()} // 添加这行防止事件冒泡问题
				>
					{options.map((option) => (
						<div
							key={option.value}
							className={`px-2 py-1 text-sm cursor-pointer hover:bg-gray-200 ${
								option.value === value ? 'bg-gray-200' : ''
							} ${option.disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
							onClick={(e) => {
								e.stopPropagation(); // 阻止事件冒泡
								if (!option.disabled) {
									handleChange(option.value);
								}
							}}
						>
							{option.label}
						</div>
					))}
				</div>,
				document.body
			)}
		</div>
	);
};

export default Select;