const unsecuredCopyToClipboard = (text: string) => {
  const textArea = document.createElement('textarea')
  textArea.value = text
  document.body.appendChild(textArea)
  textArea.focus()
  textArea.select()
  return new Promise((resolve, reject) => {
    try {
      document.execCommand('copy')
      document.body.removeChild(textArea)

      resolve('复制成功')
    } catch (err) {
      console.error('Unable to copy to clipboard', err)
      document.body.removeChild(textArea)
      resolve('Unable to copy to clipboard')
    }
  })
}

/**
 * Copies the text passed as param to the system clipboard
 * Check if using HTTPS and navigator.clipboard is available
 * Then uses standard clipboard API, otherwise uses fallback
 */
export const copyToClipboard = (content: string) => {
  return new Promise<string>((resolve, reject) => {
    // 检查是否支持现代的Clipboard API
    if (navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
      navigator.clipboard.writeText(content)
        .then(() => resolve('复制成功'))
        .catch(error => {
          console.error('Clipboard API failed:', error);
          // 如果现代API失败，回退到旧方法
          unsecuredCopyToClipboard(content).then(res => resolve(res));
        });
    } else {
      // 回退到旧方法
      unsecuredCopyToClipboard(content).then(res => resolve(res));
    }
  });
}
