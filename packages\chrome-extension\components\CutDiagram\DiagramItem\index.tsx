import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Loader, Trash2} from 'lucide-react'
import {useEffect, useState} from 'react'
import {toast} from 'sonner'
import {Button} from '@/components/ui/button'
import {Checkbox} from '@/components/ui/checkbox'
import {HoverCard, HoverCardContent, HoverCardTrigger} from '@/components/ui/hover-card'
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/radix-select'
import Tooltip from '@/components/ui/toolTip'
import {getBase64ById} from '@/lib/exportImgs'
import cutDiagramStore, {type CutDiagramItem} from '@/store/cutDiagramStore'

export default function DiagramItem({
  item,
  // isSelected,
  onSelectionChange,
}: {
  item: CutDiagramItem
  // isSelected: boolean
  onSelectionChange: (checked: boolean) => void
}) {
  const {cutDiagrams, setCutDiagrams} = cutDiagramStore.state
  const [imgSrc, setImgSrc] = useState<string>('')

  const handleFormatChange = (newFormat: 'PNG' | 'JPG' | 'SVG') => {
    const newCutDiagrams = cutDiagrams.map(d =>
      d.key === item.key ? {...d, format: newFormat, bosUrl: '', key: `${item.id}${newFormat}${item.scale}`} : d,
    )
    setCutDiagrams(newCutDiagrams)
  }

  const handleScaleChange = (newScale: string) => {
    const newCutDiagrams = cutDiagrams.map(d =>
      d.key === item.key ? {...d, scale: Number(newScale), bosUrl: '', key: `${item.id}${item.format}${newScale}`} : d,
    )
    setCutDiagrams(newCutDiagrams)
  }
  useEffect(() => {
    const fetchImage = async () => {
      if (item.id) {
        try {
          const base64 = await getBase64ById(item.id, 1, 'PNG')
          setImgSrc(`data:image/png;base64,${base64}`)
        } catch (e) {
          console.error('Failed to fetch node image:', e)
        }
      }
    }
    fetchImage()
  }, [item.id])

  return (
    <div className="group flex items-center space-x-3 py-2 px-1 hover:bg-gray-50 rounded cursor-pointer">
      <Checkbox checked={item.isSelected} onCheckedChange={onSelectionChange} />

      <HoverCard openDelay={300} closeDelay={150}>
        <HoverCardTrigger asChild>
          <div className="w-10 h-10 flex items-center justify-center rounded cursor-pointer overflow-hidden border border-gray-200 box-border group relative">
            {imgSrc ? (
              <img src={imgSrc} alt={item.name} className="max-w-8 max-h-8 object-contain" />
            ) : (
              <Loader className="h-5 w-5 text-gray-400 animate-spin" />
            )}
            {item.bosUrl && (
              <div
                className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center cursor-pointer opacity-70 group-hover:opacity-100 transition-opacity"
                onClick={() => {
                  if (item.bosUrl) {
                    navigator.clipboard.writeText(item.bosUrl)
                    toast.success('复制成功')
                  }
                }}
              >
                <Copy className="h-4 w-4 text-white" />
              </div>
            )}
          </div>
        </HoverCardTrigger>
        {/* z-index 问题 */}
        <HoverCardContent
          side="right"
          className="w-auto p-1 bg-white shadow-lg relative z-99999999 border-none box-border"
          sideOffset={12}
        >
          {imgSrc && <img src={imgSrc} alt={item.name} className="max-w-[232px] max-h-[232px] object-contain" />}
        </HoverCardContent>
      </HoverCard>

      <div
        className="flex-1 min-w-0"
        onClick={() => {
          if (item.bosUrl) {
            navigator.clipboard.writeText(item.bosUrl)
            toast.success('复制成功')
          }
        }}
      >
        <Tooltip content={item.name} className="max-w-[200px]">
          <div className="font-medium text-sm text-gray-900 truncate">{item.name}</div>
        </Tooltip>
        <div className="text-xs text-gray-500 flex items-center space-x-2">
          <span className="shrink-0 text-[12px] max-w-[70px] tracking-tight">
            {Math.floor(item.width) * item.scale} x {Math.floor(item.height) * item.scale}
          </span>
          <Select value={item.format.toLocaleUpperCase()} onValueChange={handleFormatChange}>
            <SelectTrigger className="!w-10 !h-4 text-xs !p-1 flex justify-center items-center" needIcon={false}>
              <SelectValue placeholder="Format" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="PNG">PNG</SelectItem>
              <SelectItem value="JPG">JPG</SelectItem>
              <SelectItem value="SVG">SVG</SelectItem>
            </SelectContent>
          </Select>
          <Select
            value={item.format.toLocaleUpperCase() === 'SVG' ? '1' : String(item.scale)}
            onValueChange={handleScaleChange}
          >
            <SelectTrigger
              className="!w-10 !h-4 text-xs !p-1 flex justify-center items-center"
              needIcon={false}
              disabled={item.format.toLocaleUpperCase() === 'SVG'}
            >
              <SelectValue placeholder="Scale" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">1x</SelectItem>
              <SelectItem value="2">2x</SelectItem>
              <SelectItem value="3">3x</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* <Button variant="ghost" size="sm" className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity">
        <Copy className="h-4 w-4 text-gray-400" />
      </Button> */}
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
        onClick={e => {
          setCutDiagrams(cutDiagrams.filter(i => i.key !== item.key))
        }}
      >
        <Trash2 className="h-4 w-4 text-red-500" />
      </Button>
    </div>
  )
}
