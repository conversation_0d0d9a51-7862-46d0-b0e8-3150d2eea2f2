import type {JsonAdapter} from '@baidu/f2c-plugin-base/dist/types'
import {isEmpty} from '../utils/utils'

type IdToNameMap = {
  [id: string]: string
}
type IdToNameListMap = {
  [id: string]: Array<string>
}

interface StylesClassNameItem {
  num: number
  value: string
  orderLength: number
}

export class NameStore {
  // id和图层名映射
  idToNameMap: IdToNameMap
  // 创建默认命名映射
  idToClassname: IdToNameMap
  // 保存图片alt
  altIdToNameMap: IdToNameMap
  // deprecated
  cssToNameMap: IdToNameMap
  // deprecate 图片命名映射
  imageToNameMap: IdToNameMap
  // module tag 命名映射
  idToComponentNames: IdToNameListMap = {}
  numberOfSvgs: number
  numberOfProps: number
  numberOfDataArr: number
  numberOfDataField: number
  numberOfImages: number
  numberOfComponents: number
  cssClassNameToStyle: Record<string, Record<string, string>>
  divStylesClassName: Array<StylesClassNameItem>
  otherStylesClassName: Array<StylesClassNameItem>
  rootNode: SceneNode

  constructor() {
    this.reset()
  }

  reset() {
    this.idToNameMap = {}
    this.idToClassname = {}
    this.altIdToNameMap = {}
    this.cssToNameMap = {}
    this.imageToNameMap = {}
    this.numberOfSvgs = 1
    this.numberOfProps = 1
    this.numberOfImages = 1
    this.numberOfDataArr = 1
    this.numberOfDataField = 1
    this.numberOfComponents = 1
    this.cssClassNameToStyle = {}
    this.divStylesClassName = [
      {num: 0, value: 'page', orderLength: 0},
      {num: 0, value: 'wrap', orderLength: 0},
      {num: 0, value: 'container', orderLength: 0},
      {num: 0, value: 'content', orderLength: 0},
      {num: 0, value: 'main', orderLength: 0},
      {num: 0, value: 'section', orderLength: 0},
      {num: 0, value: 'subSection', orderLength: 0},
      {num: 0, value: 'block', orderLength: 0},
      {num: 0, value: 'subBlock', orderLength: 0},
    ]
    this.otherStylesClassName = [
      {num: 0, value: 'image', orderLength: 0},
      {num: 0, value: 'text', orderLength: 0},
      {num: 0, value: 'div', orderLength: 0},
    ]
  }

  getOtherClassName(type: string, id: string, ai: boolean): string {
    const valueIndex = this.otherStylesClassName.findIndex(
      (item: StylesClassNameItem) => item.value === type.toLowerCase(),
    )
    const returnClassName = this.otherStylesClassName[valueIndex].num
      ? `${this.otherStylesClassName[valueIndex].value}${this.otherStylesClassName[valueIndex].num}`
      : this.otherStylesClassName[valueIndex].value
    this.otherStylesClassName[valueIndex].num++
    this.setIdToClassname(id, returnClassName)
    return ai ? id : returnClassName
  }

  getDivStylesClassName(order: Array<string>, id: string, ai: boolean, name: string): string {
    try {
      const isAnonymous = name === '匿名'
      const orderLength = order.length
      if (orderLength > this.divStylesClassName.length) {
        const valueIndex = this.otherStylesClassName.findIndex((item: StylesClassNameItem) => item.value === 'div')
        const returnClassName = this.otherStylesClassName[valueIndex].num
          ? `${this.otherStylesClassName[valueIndex].value}${this.otherStylesClassName[valueIndex].num}`
          : this.otherStylesClassName[valueIndex].value
        this.otherStylesClassName[valueIndex].num++
        this.setIdToClassname(id, returnClassName)
        return ai && !isAnonymous ? id : returnClassName
      }
      const orderLengthIdx = this.divStylesClassName.findIndex(
        (item: StylesClassNameItem) => item.orderLength === orderLength,
      )
      if (orderLengthIdx === -1) {
        // 说明这个层级数量没有使用关键字，找num为0的使用
        const firstNumZeroIdx = this.divStylesClassName.findIndex((item: StylesClassNameItem) => item.num === 0)
        if (firstNumZeroIdx === -1) {
          // 所有层级已经被占满，返回默认div
          const valueIndex = this.otherStylesClassName.findIndex((item: StylesClassNameItem) => item.value === 'div')
          const returnClassName = this.otherStylesClassName[valueIndex].num
            ? `${this.otherStylesClassName[valueIndex].value}${this.otherStylesClassName[valueIndex].num}`
            : this.otherStylesClassName[valueIndex].value
          this.otherStylesClassName[valueIndex].num++
          this.setIdToClassname(id, returnClassName)
          return ai && !isAnonymous ? id : returnClassName
        } else {
          const {num, value} = this.divStylesClassName[firstNumZeroIdx]
          const res = `${value}${num ? num : ''}`
          this.divStylesClassName[firstNumZeroIdx].num++
          this.divStylesClassName[firstNumZeroIdx].orderLength = orderLength
          this.setIdToClassname(id, res)
          return ai && !isAnonymous ? id : res
        }
      } else {
        const {num, value} = this.divStylesClassName[orderLengthIdx]
        const res = `${value}${num ? num : ''}`
        this.divStylesClassName[orderLengthIdx].num++
        this.setIdToClassname(id, res)
        return ai && !isAnonymous ? id : res
      }
    } catch (error) {
      // console.log('getStylesClassName error: ' + error)
    }
  }

  setIdToName(id: string, name: string) {
    this.idToNameMap[id] = name
  }

  getIdToNameMap(): IdToNameMap {
    return this.idToNameMap
  }

  setIdToClassname(id: string, name: string) {
    this.idToClassname[id] = name
  }

  getIdToClassname(): IdToNameMap {
    return this.idToClassname
  }

  getAltIdToNameMap(): IdToNameMap {
    return this.altIdToNameMap
  }

  getAltName(id: string): string {
    return this.altIdToNameMap[id]
  }

  getDataFieldName(): string {
    const name = 'dataField' + this.numberOfDataField
    this.numberOfDataField++
    return name
  }

  getComponentName(): string {
    const name = 'Component' + this.numberOfComponents
    this.numberOfComponents++
    return name
  }

  getNumberOfSvgs(): number {
    return this.numberOfSvgs
  }

  getNumberOfImages(): number {
    return this.numberOfImages
  }

  getDataArrName(id: string): string {
    let name: string = this.idToNameMap[id]
    if (!isEmpty(name)) {
      return name
    }

    name = 'data' + this.numberOfDataArr
    this.idToNameMap[id] = name
    this.numberOfDataArr++
    return name
  }

  getPropName(id: string): string {
    let name: string = this.idToNameMap[id]
    if (!isEmpty(name)) {
      return name
    }

    name = 'prop' + this.numberOfProps
    this.idToNameMap[id] = name
    this.numberOfProps++
    return name
  }

  getVectorName(id: string): string {
    let name: string = this.idToNameMap[id]
    if (!isEmpty(name)) {
      return name
    }

    const altName: string = 'Svg Asset ' + this.numberOfSvgs
    this.altIdToNameMap[id] = altName

    name = 'SvgAsset' + this.numberOfSvgs
    this.idToNameMap[id] = name
    this.numberOfSvgs++
    return name
  }

  getImageName(id: string, suffix = ''): string {
    let name: string = this.idToNameMap[id]
    if (name) {
      return name
    }

    const altName: string = 'Image Asset ' + this.numberOfImages
    this.altIdToNameMap[id] = altName

    name = 'ImageAsset' + this.numberOfImages + suffix
    this.idToNameMap[id] = name
    this.numberOfImages++
    // console.log('[dsl] getImageName', name)
    return name
  }

  // getImageUrl(node: JsonAdapter) {
  //   return node.imageUrl
  // }

  setImageToName(id: string, name: string) {
    this.imageToNameMap[`f2c_${id}`] = name
  }

  addComponentName(id: string, name: string) {
    let componentNames = this.idToComponentNames[id]
    if (componentNames === undefined) {
      componentNames = new Array<string>()
      this.idToComponentNames[id] = componentNames
    }
    if (componentNames.indexOf('name') === -1) {
      componentNames.push(name)
    }
  }
}
export const nameStore = new NameStore()
