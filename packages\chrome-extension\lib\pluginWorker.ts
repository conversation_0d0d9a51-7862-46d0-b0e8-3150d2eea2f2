// Plugin evaluation worker - Pure JavaScript for Web Worker execution
// This file will be loaded as raw text and executed in a Worker environment
// @ts-nocheck - This file runs as raw JavaScript in Worker context

console.log('🔧 Worker: Starting initialization...')

// Import pattern regex
const IMPORT_RE = /^\s*import\s+(([^'"\n]+|'[^']*'|"[^"]*")|\s*\(\s*[^)]*\s*\))/gm

// Save postMessage before cleaning globals
const originalPostMessage = globalThis.postMessage

// Safe globals that plugins can access
const safeGlobals = new Set([
  'Object',
  'Function',
  'Array',
  'Number',
  'parseFloat',
  'parseInt',
  'Infinity',
  'NaN',
  'undefined',
  'Boolean',
  'String',
  'Symbol',
  'Date',
  'Promise',
  'RegExp',
  'Error',
  'AggregateError',
  'EvalError',
  'RangeError',
  'ReferenceError',
  'SyntaxError',
  'TypeError',
  'URIError',
  'globalThis',
  'JSON',
  'Math',
  'Intl',
  'ArrayBuffer',
  'Atomics',
  'Uint8Array',
  'Int8Array',
  'Uint16Array',
  'Int16Array',
  'Uint32Array',
  'Int32Array',
  'Float32Array',
  'Float64Array',
  'Uint8ClampedArray',
  'BigUint64Array',
  'BigInt64Array',
  'DataView',
  'Map',
  'BigInt',
  'Set',
  'WeakMap',
  'WeakSet',
  'Proxy',
  'Reflect',
  'FinalizationRegistry',
  'WeakRef',
  'decodeURI',
  'decodeURIComponent',
  'encodeURI',
  'encodeURIComponent',
  'escape',
  'unescape',
  'isFinite',
  'isNaN',
  'console',
  'URLSearchParams',
  'URLPattern',
  'URL',
  'Blob',
  'onmessage',
])

// Main message handler
globalThis.onmessage = async function ({data}) {
  console.log('🔧 Worker: Received message:', data)
  const {id, payload} = data
  const {pluginCode} = payload

  // Inline evaluate function to avoid scoping issues
  async function evaluate(code) {
    console.log('🔧 Worker: Starting evaluate function...')
    const blob = new Blob([code], {type: 'text/javascript'})
    const url = URL.createObjectURL(blob)

    try {
      console.log('🔧 Worker: Dynamic import starting...')
      const module = await import(url)
      console.log('🔧 Worker: Dynamic import successful:', Object.keys(module))
      return module
    } finally {
      URL.revokeObjectURL(url)
      console.log('🔧 Worker: URL cleaned up')
    }
  }

  try {
    if (!pluginCode) {
      throw new Error('No plugin code provided')
    }

    console.log('🔧 Worker: Checking for imports...')
    if (IMPORT_RE.test(pluginCode)) {
      throw new Error('`import` is not allowed in plugins.')
    }

    console.log('🔧 Worker: Evaluating plugin code...')
    const exports = await evaluate(pluginCode)

    console.log('🔧 Worker: Extracting plugin from exports...')
    const plugin = exports.default || exports.plugin

    if (!plugin || typeof plugin !== 'object') {
      throw new Error('Invalid plugin format: Plugin must be an object')
    }

    const pluginName = plugin.name
    console.log('🔧 Worker: Plugin name extracted:', pluginName)

    const message = {
      id: id,
      payload: {pluginName: pluginName},
    }
    console.log('🔧 Worker: Sending success message:', message)
    originalPostMessage(message)
  } catch (e) {
    console.error('🔧 Worker: Plugin evaluation error:', e)
    const message = {
      id: id,
      error: e,
    }
    console.log('🔧 Worker: Sending error message:', message)
    originalPostMessage(message)
  }
}

// Clean up global scope - only expose safe APIs to plugins
console.log('🔧 Worker: Cleaning up global scope...')
Object.getOwnPropertyNames(globalThis)
  .filter(function (key) {
    return !safeGlobals.has(key)
  })
  .forEach(function (key) {
    try {
      globalThis[key] = undefined
    } catch (e) {
      // Some properties might be non-configurable
    }
  })

// Lock down critical properties
Object.defineProperties(globalThis, {
  name: {value: 'pluginWorker', writable: false, configurable: false},
  onmessage: {value: undefined, writable: false, configurable: false},
  onmessageerror: {value: undefined, writable: false, configurable: false},
  postMessage: {value: undefined, writable: false, configurable: false},
})

console.log('🔧 Worker: Setup complete, ready to receive messages')
