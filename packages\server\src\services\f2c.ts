import {convertToCode} from 'src/config'
import {type DataReportPlatform, ReportType, f2cDataReport} from 'src/libs/dataReport'
import {figmaAPI} from './api'
export type CodeRes = {
  files: {content: string; path: string}[]
  importStatements: {importPath?: string; id: string; name: string; url?: string; fileExt?: string; nodeType?: string}[]
}

export class F2CService {
  private static instance: F2CService

  private constructor() {}

  // 单例模式
  static getInstance(): F2CService {
    if (!F2CService.instance) {
      F2CService.instance = new F2CService()
    }
    return F2CService.instance
  }

  async convertToHtml(nodeIds: string, nodesResult: any, option?: any): Promise<CodeRes | null> {
    // 处理多个节点ID的情况，并增加容错处理
    const nodeIdList = nodeIds.split(',')
    const nodes = []

    for (const nodeId of nodeIdList) {
      const formattedNodeId = nodeId.replace(/-/g, ':')
      if (nodesResult.nodes && nodesResult.nodes[formattedNodeId] && nodesResult.nodes[formattedNodeId].document) {
        nodes.push(nodesResult.nodes[formattedNodeId].document)
      }
    }

    // 如果没有找到有效节点，返回null
    if (nodes.length === 0) {
      return null
    }

    try {
      const res = await convertToCode(nodes, option)
      return res
    } catch (error) {
      console.error('转换代码失败:', error)
      return null
    }
  }
  replaceImageUrl = (content: string, imageResult: any, importStatements: CodeRes['importStatements']) => {
    try {
      // logger.debug(JSON.stringify(imageResult.images))

      // 批量替换所有图片路径
      if (imageResult.images) {
        importStatements.forEach(({id, importPath}) => {
          const imageUrl = imageResult.images[id]
          if (imageUrl) {
            // 使用简单的字符串替换
            content = content.replaceAll(`.${importPath}`, `${imageUrl}`)
          } else {
            content = content.replaceAll(`.${importPath}`, ``)
          }
        })
      }

      return content
    } catch (error) {
      console.error('处理图片失败:', error)
      return content // 出错时返回原始HTML
    }
  }

  // 修改方法签名，添加可选的 figmaApiInstance 参数
  async processCodeWithImages(res: CodeRes, fileKey: string): Promise<string> {
    const figmaApiInstance = figmaAPI
    const html = res.files[0].content
    if (!res.importStatements.length) {
      // 如果没有图片导入语句，直接返回原始HTML
      return html
    }
    // 批量获取所有图片的URL
    const allImageIds = res.importStatements.map(stmt => stmt.id)
    const imageResult = await figmaApiInstance.getImage(fileKey, allImageIds)

    return this.replaceImageUrl(html, imageResult, res.importStatements)
  }

  async processFilesWithImages(res: CodeRes, fileKey: string, option?: any): Promise<CodeRes['files']> {
    const figmaApiInstance = figmaAPI
    const {scaleSize = 1, imgFormat = 'png'} = option || {}

    // 批量获取所有图片的URL
    const allImageIds = res.importStatements.map(stmt => stmt.id)
    const imageResult = await figmaApiInstance.getImage(fileKey, allImageIds, {
      format: imgFormat,
      scale: scaleSize,
    })

    return res.files.map(item => {
      return {
        ...item,
        content: this.replaceImageUrl(item.content, imageResult, res.importStatements),
      }
    })
  }

  async processAllFilesWithImages(
    res: CodeRes,
    fileKey: string,
    option?: any,
  ): Promise<{files: CodeRes['files']; images: {[url: string]: CodeRes['importStatements'][0]}}> {
    const {scaleSize = 1, imgFormat = 'png'} = option || {}
    const figmaApiInstance = figmaAPI
    // 批量获取所有图片的URL
    const allImageIds = res.importStatements.map(stmt => stmt.id)
    const imageResult: {images: {[id: string]: string}} = await figmaApiInstance.getImage(fileKey, allImageIds, {
      format: imgFormat,
      scale: scaleSize,
    })
    const result = res.importStatements.reduce((acc, item) => {
      const key = imageResult.images[item.id]
      // 创建新对象，避免修改原数据
      const {url, importPath, ...rest} = item
      rest.fileExt = importPath?.split('.').pop() || ''
      acc[key] = rest
      return acc
    }, {} as any)
    return {
      files: res.files.map(item => {
        return {
          ...item,
          content: this.replaceImageUrl(item.content, imageResult, res.importStatements),
        }
      }),
      images: result,
    }
  }
}

// 导出单例实例
export const f2cService = F2CService.getInstance()

// 为了保持向后兼容，保留原来的函数
export const convertToHtml = async (nodeIds: string, nodesResult: any, option?: any): Promise<CodeRes | null> => {
  return f2cService.convertToHtml(nodeIds, nodesResult, option)
}

interface ConvertToHtmlWithReportParams {
  nodeIds: string
  nodesResult: any
  option?: any
  params?: Record<string, any>
  req?: Request
}

const calculateLineCount = (res: CodeRes | null): number => {
  if (!res || !res.files || res.files.length === 0) {
    return 1
  }

  let totalLines = 0
  for (const file of res.files) {
    if (file.content) {
      // 计算换行符数量
      const newlineCount = (file.content.match(/\n/g) || []).length

      // 计算HTML标签数量（开始标签和结束标签）
      const htmlTagCount = (file.content.match(/<\/?[a-zA-Z][^>]*>/g) || []).length

      // 总行数 = 换行符数量 + HTML标签数量 + 1（基础行数）
      const lineCount = newlineCount + htmlTagCount + 1
      totalLines += lineCount
    }
  }

  return totalLines > 0 ? totalLines : 1
}

export const convertToHtmlWithReport = async (config: ConvertToHtmlWithReportParams): Promise<CodeRes | null> => {
  const {nodeIds, nodesResult, option, params, req} = config
  const res = await convertToHtml(nodeIds, nodesResult, option)
  const userName = (params?.personal_token || params?.access_token || '').substring(0, 10)
  const totalLines = calculateLineCount(res)
  // console.log('convertToHtmlWithReport:', totalLines, userName, JSON.stringify(params))
  f2cDataReport({
    type: ReportType.codeCount,
    userName,
    count: 1,
    uiframework: option?.cssFramework || 'inlinecss',
    platform: req?.headers.get('F2c-Api-Platform') as DataReportPlatform,
  })
  f2cDataReport({
    type: ReportType.lineCount,
    userName,
    count: totalLines,
    uiframework: option?.cssFramework || 'inlinecss',
    platform: req?.headers.get('F2c-Api-Platform') as DataReportPlatform,
  })
  return res
}
