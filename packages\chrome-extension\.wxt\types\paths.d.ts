// Generated by wxt
import "wxt/browser";

declare module "wxt/browser" {
  export type PublicPath =
    | "/"
    | "/background.js"
    | "/content-scripts/content.js"
    | "/entry.html"
    | "/figma.js"
    | "/icons/icon128.png"
    | "/icons/icon16.png"
    | "/icons/icon32.png"
    | "/icons/icon48.png"
    | "/rules/figma.json"
    | "/tokenScript.js"
    | "/ui.js"
  type HtmlPublicPath = Extract<PublicPath, `${string}.html`>
  export interface WxtRuntime {
    getURL(path: PublicPath): string;
    getURL(path: `${HtmlPublicPath}${string}`): string;
  }
}
