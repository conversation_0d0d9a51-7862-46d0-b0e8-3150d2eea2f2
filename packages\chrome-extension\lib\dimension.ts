import dimensionStore from '@/store/dimensionStore'
import type {DashPatternNode, StrokesNode} from '@/types/NodeType'
import {getObjType} from '@/utils/common-utils'
import {getFloat} from '@/utils/format-number-utils'

export const TransformUnit = (text: string, originUnit: string, toUnit: string, magnification: number) => {
  // 使用单次正则完成查找+替换
  const regex = new RegExp(`(-?\\d+\\.?\\d*)${originUnit}\\b`, 'g')
  return text.replace(regex, (_, numStr) => {
    const num = Number.parseFloat(numStr)
    const result = num * magnification

    // 优化数字格式化：去除多余末尾0
    return result % 1 === 0
      ? `${result}${toUnit}` // 整数省略小数
      : `${result.toFixed(3)}${toUnit}`
          .replace(/(\.\d*?)0+$/, '$1') // 删除末尾0
          .replace(/\.$/, '') // 删除末尾小数点
  })
}
export function removeUnit(inputStr: string, unit: string) {
  const reg = new RegExp(`(.*)${unit}$`)
  const match = inputStr.match(reg)
  if (match) {
    return match[1]
  } else {
    return inputStr
  }
}
export const changeUnitDeep = (
  originObj: Record<string, any>,
  currentSelectUnit: string,
  currentMagnification: number,
  currentRemBase: number,
  currentVwBase: number,
): any => {
  const originObjType = getObjType(originObj)
  if (originObjType == 'Object') {
    for (const key in originObj) {
      const type = getObjType(originObj[key])
      if (type == 'String') {
        originObj[key] = String(
          getFloat(
            removeUnit(
              TransformUnit(
                originObj[key],
                'px',
                currentSelectUnit,
                (function () {
                  switch (currentSelectUnit) {
                    case 'rem':
                      return currentMagnification / currentRemBase
                    case 'px':
                    case 'dp':
                      return currentMagnification
                    case 'vw':
                      return (100 / currentVwBase) * currentMagnification
                    default:
                      return currentMagnification
                  }
                })(),
              ),
              '',
            ),
          ),
        )
      } else if (type == 'Object' || type == 'Array') {
        changeUnitDeep(originObj[key], currentSelectUnit, currentMagnification, currentRemBase, currentVwBase)
      }
    }
  }
}
function isSVG(node: SceneNode, depth = 0): boolean {
  // if (depth >= 4) return true // Max depth reached, assume it's SVG

  // if ('children' in node && node.children.length > 0) {
  //   return node.children.every((child) => isSVG(child, depth + 1))
  // } else {
  return ['VECTOR', 'ELLIPSE', 'RECTANGLE', 'POLYGON', 'POLYLINE', 'STAR', 'LINE'].includes(node.type)
  // }
}
export const getDimensionBaseInfo = async (node: SceneNode) => {
  const css: any = await node.getCSSAsync()
  const codeCss = JSON.parse(JSON.stringify(css))
  if (codeCss['font-family']) {
    codeCss['font-family'] = `"${codeCss['font-family'].replace(/\'/g, '').replace(/\"/g, '')}"`
  }
  if (codeCss.fill) {
    codeCss.background = codeCss.fill
    delete codeCss.fill
  }
  if (!css.height) {
    css.height = String((node as SceneNode).height) + 'px'
    codeCss.height = String((node as SceneNode).height) + 'px'
  }
  if (!css.width) {
    css.width = String((node as SceneNode).width) + 'px'
    codeCss.width = String((node as SceneNode).width) + 'px'
  }
  ;(css as any).demisionFills = getFillInfos(node)
  ;(css as any).fontFills = getFontFills(node)
  css.borderRadius = getRadius(node as RectangleNode)
  css.border = getBorder(node as RectangleNode)
  css.padding = getPadding(node as FrameNode)
  const res = getRenderDistanceToParent(node as SceneNode)
  const [top, right, bottom, left] = res
  css.top = String(top)
  css.right = String(right)
  css.bottom = String(bottom)
  css.left = String(left)
  css.opacity = String((node as any).opacity)
  const demisionCode: {layout?: Record<string, string>; style?: Record<string, string>} = {}
  if (getDemisionLayoutStyleCode(codeCss, 'layout')) {
    demisionCode.layout = getDemisionLayoutStyleCode(codeCss, 'layout')
  }
  if (getDemisionLayoutStyleCode(codeCss, 'style')) {
    demisionCode.style = getDemisionLayoutStyleCode(codeCss, 'style')
  }
  let svg = ''
  const isSvg = isSVG(node)
  if (isSvg) {
    // 获取figma Svg代码
    const svgData = await node.exportAsync({
      format: 'SVG',
      svgIdAttribute: true, // 可选：为 SVG 元素添加 ID 属性
      svgOutlineText: true, // 可选：将文本转换为轮廓
    })

    // 将 Uint8Array 转换为字符串
    const decoder = new TextDecoder('utf-8')
    svg = decoder.decode(svgData)
  }

  // css 标注用 codeCss 展示代码用
  return {nodeName: node.name, nodeId: node.id, nodeType: node.type, css, codeCss, svg, isSvg}
}

export function getDemisionLayoutStyleCode(css: Record<string, string>, type: 'layout' | 'style') {
  const obj = JSON.parse(JSON.stringify(css))
  const code = {}
  const keyArr = ['position', 'width', 'height', 'left', 'right', 'top', 'bottom']
  keyArr.map(item => {
    if (type == 'layout') {
      if (obj.hasOwnProperty(item)) {
        ;(code as Record<string, string>)[item] = obj[item]
      }
    } else {
      if (obj.hasOwnProperty(item)) {
        delete obj[item]
      }
    }
  })
  const resObj = type == 'layout' ? code : obj

  return Object.keys(resObj).length > 0 ? resObj : null
}

function getFillInfos(node: BaseNode | FrameNode | TextNode) {
  try {
    const type = node.type
    const fillRes: any[] = []
    if (type == 'TEXT') {
      return []
    }
    const fillsInfo = (node as any).fills?.filter((fill: {visible: boolean; type: string}) => {
      if (fill && fill.visible && fill.type !== 'IMAGE') {
        return fill
      }
    })
    fillRes.push(...(fillsInfo || []))
    return fillRes
  } catch (error) {
    return []
  }
}

export function getRenderDistanceToParent(node: SceneNode) {
  let currentCoordinate
  //@ts-ignore
  if (node.absoluteRenderBounds !== undefined && node.absoluteRenderBounds) {
    //@ts-ignore
    currentCoordinate = node.absoluteRenderBounds
  } else {
    //@ts-ignore
    currentCoordinate = node.absoluteBoundingBox
  }

  let targetCoordinate
  if (node.parent !== undefined && node.parent) {
    //@ts-ignore
    if (node.parent.absoluteBoundingBox !== undefined && node.parent.absoluteBoundingBox) {
      //@ts-ignore
      targetCoordinate = node.parent.absoluteBoundingBox
    } else {
      //@ts-ignore
      targetCoordinate = node.parent.absoluteBoundingBox
    }
    if (!targetCoordinate) {
      targetCoordinate = {x: 0, y: 0, width: 0, height: 0}
    }
    if (!currentCoordinate) {
      currentCoordinate = {x: 0, y: 0, width: 0, height: 0}
    }
    return [
      currentCoordinate.y - targetCoordinate.y,
      targetCoordinate.x + targetCoordinate.width - (currentCoordinate.x + currentCoordinate.width),
      targetCoordinate.y + targetCoordinate.height - (currentCoordinate.y + currentCoordinate.height),
      currentCoordinate.x - targetCoordinate.x,
    ]
  } else {
    // return
    return [0, 0, 0, 0]
  }
}

function getFontFills(node: BaseNode | FrameNode | TextNode) {
  const type = node.type
  if (type !== 'TEXT') {
    return []
  }
  return node.getStyledTextSegments([
    'fontSize',
    'fontName',
    'fontWeight',
    'textDecoration',
    'textCase',
    'fills',
    'letterSpacing',
    'listOptions',
    'indentation',
    'hyperlink',
    'lineHeight',
  ])
}

export function getRadius(node: RectangleNode | BaseNode | VectorNode) {
  const type = node.type
  if (type == 'VECTOR') {
    const borderRadius = node.cornerRadius
    // corner radius
    if (node.cornerRadius !== figma.mixed && node.cornerRadius) {
      return {
        bottomLeftRadius: borderRadius,
        bottomRightRadius: borderRadius,
        topLeftRadius: borderRadius,
        topRightRadius: borderRadius,
      }
    } else if (node.cornerRadius === figma.mixed) {
      const {bottomLeftRadius, bottomRightRadius, topLeftRadius, topRightRadius} = node as any
      return {bottomLeftRadius, bottomRightRadius, topLeftRadius, topRightRadius}
    }
  } else {
    const {bottomLeftRadius, bottomRightRadius, topLeftRadius, topRightRadius} = node as RectangleNode
    return {bottomLeftRadius, bottomRightRadius, topLeftRadius, topRightRadius}
  }
}

export function getBorder(node: RectangleNode) {
  if (node.strokes?.length == 0) {
    return {
      borderTop: 0,
      borderRight: 0,
      borderBottom: 0,
      borderLeft: 0,
    }
  }
  const {strokeTopWeight, strokeRightWeight, strokeBottomWeight, strokeLeftWeight} = node
  const borderStyle = getBorderStyle(node)
  return {
    borderTop: strokeTopWeight,
    borderRight: strokeRightWeight,
    borderBottom: strokeBottomWeight,
    borderLeft: strokeLeftWeight,
    strokes: node.strokes,
    borderStyle,
  }
}

export function getBorderStyle(node: SceneNode) {
  let borderStyle = undefined
  if ((node as StrokesNode).strokes !== undefined && (node as StrokesNode).strokes.length > 0 && HasBorderWidth(node)) {
    // border-style 仅分析solid、dashed
    const formatStrokes = (node as StrokesNode).strokes.filter(stroke => stroke.visible && stroke.type === 'SOLID')
    if (formatStrokes.length > 0) {
      borderStyle =
        (node as DashPatternNode).dashPattern !== undefined && (node as DashPatternNode).dashPattern.length === 0
          ? 'solid'
          : 'dashed'
    }
  }
  return borderStyle
}

export function getPadding(node: FrameNode) {
  const {paddingTop, paddingRight, paddingBottom, paddingLeft} = node
  return {
    paddingTop,
    paddingRight,
    paddingBottom,
    paddingLeft,
  }
}

export function HasBorderWidth(node: SceneNode) {
  if ((node as StrokesNode).strokeWeight !== figma.mixed) {
    return !((node as StrokesNode).strokeWeight === 0)
  } else {
    const {strokeTopWeight = 0, strokeRightWeight = 0, strokeBottomWeight = 0, strokeLeftWeight = 0} = node as any
    return !(strokeTopWeight === 0 && strokeRightWeight === 0 && strokeBottomWeight === 0 && strokeLeftWeight === 0)
  }
}
