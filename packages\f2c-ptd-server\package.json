{"name": "@i2c/f2c_ptd_server", "module": "index.ts", "type": "module", "devDependencies": {"@types/bun": "latest", "cross-env": "^7.0.3"}, "scripts": {"dev": "bun --watch run src/index.ts", "start": "bun run src/index.ts", "lint": "biome check . --fix", "build": "bun build --target=node ./src/index.ts --outdir ./dist", "compile:linux": "bun build --compile --target=bun-linux-x64 ./src/index.ts --outfile ./dist/app", "compile": "bun build --compile --minify --sourcemap ./src/index.ts --outfile ./dist/app", "serve": "./dist/app", "build:prod": "bun build src/index.ts --outdir dist --target bun --minify --bundle --sourcemap"}, "dependencies": {}}