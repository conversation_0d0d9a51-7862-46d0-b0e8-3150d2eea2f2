import {Button} from '@/components/ui/button'
import {Card, CardContent, CardHeader, CardTitle} from '@/components/ui/card'
import {usePluginExecutor} from '@/hooks/usePluginExecutor'
import {ROUTES} from '@/router/router'
import pluginStore from '@/store/pluginStore'
import type React from 'react'
import {useEffect, useState} from 'react'
import {useLocation, useNavigate} from 'react-router-dom'
import {toast} from 'sonner'

/**
 * Test component to verify the plugin loading timing fix
 * This tests both initial loading and page switching scenarios
 */
export const PluginLoadingTest: React.FC = () => {
  const {activePlugin, isLoading} = usePluginExecutor()
  const {activePluginSource, plugins} = pluginStore.state
  const [testResults, setTestResults] = useState<string[]>([])
  const [mountCount, setMountCount] = useState(0)
  const navigate = useNavigate()
  const location = useLocation()

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`])
  }

  useEffect(() => {
    // Track component mounts to detect page switching
    setMountCount(prev => prev + 1)
    addTestResult(`🔄 Component mounted (mount #${mountCount + 1})`)
    addTestResult(`📍 Current route: ${location.pathname}`)
    addTestResult(`Initial state - activePluginSource: ${activePluginSource || 'none'}`)
    addTestResult(`Initial state - plugins count: ${Object.keys(plugins).length}`)
    addTestResult(`Initial state - activePlugin: ${activePlugin?.name || 'none'}`)
  }, [])

  useEffect(() => {
    if (activePlugin) {
      addTestResult(`✅ Active plugin loaded: ${activePlugin.name}`)
      toast.success(`Plugin ${activePlugin.name} is now active!`)
    } else if (activePluginSource) {
      addTestResult(`⏳ Waiting for plugin ${activePluginSource} to load...`)
    }
  }, [activePlugin, activePluginSource])

  const testPageSwitching = () => {
    addTestResult(`🔄 Testing page switching...`)
    addTestResult(`📍 Current route: ${location.pathname}`)

    if (location.pathname === ROUTES.CODE_PLUGINS) {
      // If we're on plugins page, go back to dimension
      addTestResult(`🔙 Switching back to dimension page`)
      navigate(ROUTES.CODE_DIMENSION)
    } else {
      // If we're on dimension page, go to plugins
      addTestResult(`🔧 Switching to plugins page`)
      navigate(ROUTES.CODE_PLUGINS)
    }
  }

  const simulatePageRefresh = () => {
    // Simulate having a plugin in localStorage by adding a test plugin
    const testPlugin = {
      source: '@test-plugin',
      pluginName: 'Test Plugin',
      code: `
export default {
  name: 'Test Plugin',
  version: '1.0.0',
  description: 'A test plugin for timing verification',
  code: {
    css: {
      title: 'Test CSS',
      transform: ({ code }) => \`/* Test Plugin Transform */\\n\${code}\`
    }
  },
  commands: {
    hello: (context, name) => \`Hello \${name} from Test Plugin!\`
  }
}
      `.trim(),
    }

    // Clear previous test results
    setTestResults([])
    addTestResult('🔄 Simulating page refresh with saved plugin...')

    // Add plugin to store (simulating localStorage restore)
    pluginStore.addPlugin(testPlugin)
    pluginStore.setActivePlugin(testPlugin.source)

    addTestResult(`📦 Added plugin to store: ${testPlugin.pluginName}`)
    addTestResult(`🎯 Set active plugin source: ${testPlugin.source}`)
  }

  const clearTest = () => {
    pluginStore.clearAllPlugins()
    setTestResults([])
    addTestResult('🧹 Cleared all plugins and test results')
  }

  const testTransform = () => {
    if (!activePlugin) {
      toast.error('No active plugin to test')
      return
    }

    const testStyle = {
      'background-color': '#ff0000',
      padding: '10px',
      margin: '5px',
    }

    try {
      // Test CSS transformation
      const result = activePlugin.code?.css?.transform?.({
        code: Object.entries(testStyle)
          .map(([k, v]) => `${k}: ${v};`)
          .join('\n'),
        style: testStyle,
        options: {useRem: false, rootFontSize: 16, scale: 1},
      })

      if (result) {
        addTestResult(`🎨 CSS Transform test successful`)
        toast.success('CSS transformation works!')
      } else {
        addTestResult(`❌ CSS Transform test failed - no result`)
        toast.error('CSS transformation failed')
      }
    } catch (error) {
      addTestResult(`❌ CSS Transform test failed: ${error}`)
      toast.error(`CSS transformation error: ${error}`)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Plugin Loading Timing Test</CardTitle>
        <p className="text-sm text-muted-foreground">
          This test verifies that plugins are properly loaded and activated on page refresh and page switching
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current State */}
        <div className="p-4 bg-muted rounded-lg">
          <h3 className="font-medium mb-2">Current State</h3>
          <div className="text-sm space-y-1">
            <div>
              Active Plugin Source: <code>{activePluginSource || 'none'}</code>
            </div>
            <div>
              Active Plugin: <code>{activePlugin?.name || 'none'}</code>
            </div>
            <div>
              Plugins in Store: <code>{Object.keys(plugins).length}</code>
            </div>
            <div>
              Loading: <code>{isLoading ? 'true' : 'false'}</code>
            </div>
          </div>
        </div>

        {/* Test Controls */}
        <div className="space-y-2">
          <Button onClick={simulatePageRefresh} disabled={isLoading} className="w-full">
            🔄 Simulate Page Refresh with Saved Plugin
          </Button>

          <Button onClick={testPageSwitching} disabled={isLoading} variant="secondary" className="w-full">
            🔀 Test Page Switching (Current: {location.pathname.split('/').pop()})
          </Button>

          <Button onClick={testTransform} disabled={!activePlugin || isLoading} variant="outline" className="w-full">
            🎨 Test CSS Transform
          </Button>

          <Button onClick={clearTest} disabled={isLoading} variant="destructive" className="w-full">
            🧹 Clear Test
          </Button>
        </div>

        {/* Test Results */}
        {testResults.length > 0 && (
          <div className="p-4 bg-muted rounded-lg">
            <h3 className="font-medium mb-2">Test Results</h3>
            <div className="text-xs space-y-1 max-h-40 overflow-y-auto">
              {testResults.map((result, index) => (
                <div key={index} className="font-mono">
                  {result}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Success Indicator */}
        {activePlugin && activePluginSource && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="text-green-800 font-medium">✅ Success! Plugin loaded and activated correctly</div>
            <div className="text-green-600 text-sm mt-1">
              The timing issue has been resolved. The plugin is now properly loaded before activation.
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default PluginLoadingTest
