import {type Plugin, pluginExecutor} from '@/lib/pluginExecutor'
import pluginStore from '@/store/pluginStore'
import {useEffect, useState} from 'react'

export function usePluginExecutor() {
  const {activePluginSource, plugins} = pluginStore.state
  const [activePlugin, setActivePlugin] = useState<Plugin | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  // Track which plugins have been successfully loaded
  const [loadedPlugins, setLoadedPlugins] = useState<Set<string>>(new Set())

  // Initialize loadedPlugins with existing plugins on component mount
  useEffect(() => {
    const syncExistingPlugins = () => {
      const existingPlugins = pluginExecutor.getAllPlugins()
      if (existingPlugins.length > 0) {
        const sources = existingPlugins.map(({source}) => source)
        setLoadedPlugins(new Set(sources))
        console.log('Synced existing plugins on mount:', sources)
      }
    }

    syncExistingPlugins()
  }, []) // Empty dependency array - only run on mount

  // Update active plugin when store changes or plugins are loaded
  useEffect(() => {
    const plugin =
      activePluginSource && loadedPlugins.has(activePluginSource) ? pluginExecutor.getPlugin(activePluginSource) : null
    setActivePlugin(plugin)
    console.log('Active plugin updated:', {
      activePluginSource,
      isLoaded: activePluginSource ? loadedPlugins.has(activePluginSource) : false,
      plugin: plugin?.name || null,
    })
  }, [activePluginSource, loadedPlugins])

  // Load plugin when it's added to store
  useEffect(() => {
    const loadNewPlugins = async () => {
      for (const [source, pluginInfo] of Object.entries(plugins)) {
        if (!pluginExecutor.getPlugin(source)) {
          try {
            setIsLoading(true)
            console.log(`Loading plugin ${pluginInfo.name} from ${source}...`)
            await pluginExecutor.loadPlugin(source, pluginInfo.code)

            // Update loaded plugins set
            setLoadedPlugins(prev => {
              const newSet = new Set(prev)
              newSet.add(source)
              console.log(`Plugin ${pluginInfo.name} loaded successfully, updating loaded plugins:`, Array.from(newSet))
              return newSet
            })

            console.log(`Plugin ${pluginInfo.name} loaded successfully`)
          } catch (error) {
            console.error(`Failed to load plugin ${pluginInfo.name}:`, error)
          } finally {
            setIsLoading(false)
          }
        }
      }
    }

    loadNewPlugins()
  }, [plugins])

  // Activate/deactivate plugin when active source changes and plugin is loaded
  useEffect(() => {
    const handleActivation = async () => {
      try {
        setIsLoading(true)

        if (activePluginSource) {
          // Only try to activate if the plugin is loaded
          if (loadedPlugins.has(activePluginSource)) {
            console.log(`Activating plugin: ${activePluginSource}`)
            await pluginExecutor.activatePlugin(activePluginSource)
          } else {
            console.log(`Plugin ${activePluginSource} not yet loaded, skipping activation`)
          }
        } else {
          // Deactivate current plugin if no active source
          const currentActive = pluginExecutor.getActivePlugin()
          if (currentActive) {
            const activeSource = Object.entries(plugins).find(([, info]) => info.name === currentActive.name)?.[0]
            if (activeSource) {
              console.log(`Deactivating plugin: ${activeSource}`)
              await pluginExecutor.deactivatePlugin(activeSource)
            }
          }
        }
      } catch (error) {
        console.error('Failed to handle plugin activation:', error)
      } finally {
        setIsLoading(false)
      }
    }

    handleActivation()
  }, [activePluginSource, loadedPlugins, plugins])

  const executeCommand = async (command: string, ...args: any[]) => {
    if (!activePluginSource) {
      throw new Error('No active plugin')
    }

    try {
      setIsLoading(true)
      return await pluginExecutor.executeCommand(activePluginSource, command, ...args)
    } catch (error) {
      console.error('Failed to execute plugin command:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const unloadPlugin = async (source: string) => {
    try {
      setIsLoading(true)
      await pluginExecutor.unloadPlugin(source)

      // Remove from loaded plugins set
      setLoadedPlugins(prev => {
        const newSet = new Set(prev)
        newSet.delete(source)
        console.log(`Plugin ${source} unloaded, updating loaded plugins:`, Array.from(newSet))
        return newSet
      })
    } catch (error) {
      console.error('Failed to unload plugin:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  return {
    activePlugin,
    isLoading,
    executeCommand,
    unloadPlugin,
    getAllPlugins: () => pluginExecutor.getAllPlugins(),
    getPlugin: (source: string) => pluginExecutor.getPlugin(source),
  }
}
