import type {Direction} from './direction'
import {getLineBasedOnDirection} from './line'
import {computeArrOrderList, orderAGreaterThanB} from './util'

// import {isItemTagNode, isListTagNode} from '../../code/list-registry'
import type {JsonAdapter} from '../jsonAdapter'
import {RestApiAdapter} from '../restApiAdapter'

// 该方向有重叠的节点进行组合
export const groupNodesByDirectionalOverlap = (
  nodes: JsonAdapter[],
  direction: Direction,
  parentNode: JsonAdapter,
): JsonAdapter[] => {
  // console.log('groupNodesByDirectionalOverlap', nodes)
  if (nodes.length < 2) {
    return nodes
  }

  const skippable = new Set<string>()
  const segmentedNodes: JsonAdapter[] = []

  // const itemNode = nodes.find(item => isItemTagNode(item)) || nodes[0]
  // console.log('groupNodesByDirectionalOverlap', itemNode)

  for (let i = 0; i < nodes.length; i++) {
    const currentNode = nodes[i]

    if (skippable.has(currentNode.getId())) {
      continue
    }

    const nodesToBeMerged = findDirectionalOverlappingNodes(currentNode, nodes, new Set<string>(), direction)

    if (nodesToBeMerged.length <= 1) {
      segmentedNodes.push(currentNode)
      continue
    }

    nodesToBeMerged.forEach(node => {
      skippable.add(node.getId())
    })

    const groupNode = new RestApiAdapter(null, nodesToBeMerged)
    // if (isListTagNode(parentNode)) {
    //   // console.log("itemNode", itemNode);
    //   if (itemNode) {
    //     groupNode.addAnnotations('tags', [...(groupNode.getAnnotation('tags') || []), 'list'])
    //   }
    // }

    segmentedNodes.push(groupNode)
  }

  return segmentedNodes
}

// 决定哪个方向的集合是否再次分组
export const decideBetweenDirectionalOverlappingNodes = (
  horizontalSegmentedNodes: JsonAdapter[],
  verticalSegmentedNodes: JsonAdapter[],
): JsonAdapter[] => {
  // 如果方向上的节点数大于2，则说明该方向上需要再次分组
  if (horizontalSegmentedNodes.length < 2 && verticalSegmentedNodes.length >= 2) {
    return verticalSegmentedNodes
  }

  if (verticalSegmentedNodes.length < 2 && horizontalSegmentedNodes.length >= 2) {
    return horizontalSegmentedNodes
  }

  // 如果都大于2则选择节点数较少的方向再次分组
  if (horizontalSegmentedNodes.length >= 2 && verticalSegmentedNodes.length >= 2) {
    return horizontalSegmentedNodes.length <= verticalSegmentedNodes.length
      ? horizontalSegmentedNodes
      : verticalSegmentedNodes
  }

  return []
}

// 找到targetNodes中与startingNode以及其重叠节点重叠的节点
export const findDirectionalOverlappingNodes = (
  startingNode: JsonAdapter,
  targetNodes: JsonAdapter[],
  currentPath: Set<string>,
  direction: Direction,
): JsonAdapter[] => {
  const line = getLineBasedOnDirection(startingNode, direction)

  let completePath: JsonAdapter[] = []
  for (let i = 0; i < targetNodes.length; i++) {
    const targetNode = targetNodes[i]
    if (currentPath.has(targetNode.getId())) {
      continue
    }

    if (targetNode.getId() === startingNode.getId()) {
      continue
    }

    const targetLine = getLineBasedOnDirection(targetNode, direction)
    // console.log('【findDirectionalOverlappingNodes】startingNode', startingNode, 'targetNode', targetNode)
    if (line.overlap(targetLine, 2)) {
      // console.log('【findDirectionalOverlappingNodes】is 【line.overlap(targetLine, 2)】')
      if (orderAGreaterThanB(startingNode.getOrder(), targetNode.getOrder())) {
        // console.log('【findDirectionalOverlappingNodes】startingNode G targetNode')
        completePath.push(targetNode)
        currentPath.add(targetNode.getId())
      }

      if (!currentPath.has(startingNode.getId())) {
        completePath.push(startingNode)
        currentPath.add(startingNode.getId())
      }

      if (orderAGreaterThanB(targetNode.getOrder(), startingNode.getOrder())) {
        // console.log('【findDirectionalOverlappingNodes】targetNode G startingNode')
        completePath.push(targetNode)
        currentPath.add(targetNode.getId())
      }
    }
  }

  // console.log('【findDirectionalOverlappingNodes】completePath', completePath)

  for (const overlappingNode of completePath) {
    const result = findDirectionalOverlappingNodes(overlappingNode, targetNodes, currentPath, direction)
    // 有bug，需要重排序
    const concatCompletePath = completePath.concat(...result)
    completePath = computeArrOrderList(concatCompletePath).map(item => concatCompletePath[item.index])
  }

  if (completePath.length !== 0) {
    return completePath
  }

  return []
}
