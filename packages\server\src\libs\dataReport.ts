import { figmaAPI } from "src/services/api"

// 缓存配置
const CACHE_TTL = 24 * 60 * 60 * 1000 // 24小时
const cache = new Map<string, { data: any; timestamp: number }>()

// 清理过期缓存
function cleanExpiredCache() {
  const now = Date.now()
  for (const [key, value] of cache.entries()) {
    if (now - value.timestamp > CACHE_TTL) {
      cache.delete(key)
    }
  }
}

const reportUrl = 'https://statistcs-hdpt.yy.com/reportGeneralStatistics'

export enum ReportType {
  codeCount = 'f2c_gen_code_count', // 代码生成次数
  lineCount = 'f2c_code_line_count', // 代码生成行数
  aiCount = 'f2c_ai_count',
  copyCount = 'f2c_copy_count', // 使用复制功能次数
  downloadCutCount = 'f2c_download_cut_count', // 使用切图下载功能次数
  uploadCutCount = 'f2c_upload_cut_count', // 上传切图数
  loginType = 'f2c_login_type', // 登录方式
  uploadCount = 'f2c_upload_count', // 上传/更新次数
  pluginOpenCount = 'f2c_plugin_open_count', // 插件打开数
  userHeartbeat = 'f2c_user_heartbeat', // 用户心跳
  useDSL = 'f2c_use_dsl', // 使用dsl生成
  useTag = 'f2c_use_tag', // 打tag
  useHandoff = 'f2c_hand_off', // 离线生成
  useHandoffFile = 'f2c_hand_off_file', // 使用离线生成文件
  errorReport = 'f2c_error_report', // 错误上报
}

export enum DataReportPlatform {
  zulu = 'zulu',
  mcp = 'mcp',
  astro = 'astro',
  comatestack = 'comatestack', // 数字员工
}

interface DataReportParams {
  type: ReportType
  uiframework?: string
  count: number
  expandObj?: any
  userName?: string
  platform?: DataReportPlatform
}

export const f2cDataReport = async (params: DataReportParams) => {
  const {type, uiframework = 'tailwindcss', count, expandObj, userName} = params
  // 获取缓存key
  const cacheKey = figmaAPI.headers['X-Figma-Token']
  
  // 检查缓存
  cleanExpiredCache()
  let data
  if (cache.has(cacheKey)) {
    console.log('f2cDataReport', '缓存命中', cacheKey)
    data = cache.get(cacheKey)?.data
  } else {
    console.log('f2cDataReport', '缓存未命中', cacheKey)
    data = await figmaAPI.getMe()
    // 设置缓存
    cache.set(cacheKey, {
      data,
      timestamp: Date.now()
    })
  }
  
  const reportUserName = data?.email ?? userName
  const handle = data?.handle ?? reportUserName ?? userName
  // console.log('f2cDataReport', type, userName, expandObj, params.platform, reportUserName, handle)
  const reqData: any = {
    ...expandObj,
    account: handle,
    appid: 1,
    dataType: type,
    dim1: `${handle}-${reportUserName}`,
    dim3: uiframework,
    value1: count,
    // dim5: 'f2cRestApi-zulu', zulu
    // dim5: 'f2cRestApi-mcp', mcp
    // dim5: 'f2cRestApi-astro', astro
    // dim5: 'f2cRestApi-comatestack', 数字员工
    dim5: params.platform ? `f2cRestApi-${params.platform}` : 'f2cRestApi',
  }
  try {
    const resp = await createFetch(3000)(reportUrl, {
      method: 'POST',
      mode: 'cors',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json;charset=utf-8',
      },
      body: JSON.stringify(reqData),
    })
    const res = await resp.json()
    // console.log('f2cDataReport', res)
    if (res && res.result != 200) {
      console.log('上报失败', res.reason, res.result)
    }
  } catch (e: any) {
    console.error(e)
  }
}

export const createFetch = (timeout: number) => {
  return (resource: any, options: any) => {
    const controller = new AbortController()
    options = options || {}
    options.signal = controller.signal
    const timerId = setTimeout(() => {
      clearTimeout(timerId)
      controller.abort()
    }, timeout)
    return fetch(reportUrl, options)
  }
}
