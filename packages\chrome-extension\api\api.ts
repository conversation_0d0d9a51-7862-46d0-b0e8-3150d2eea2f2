import type { F2cBusiness } from '../types/BusinessType'

const domain = 'https://f2c-api.yy.com'
export const getF2cBusinessList = async (figmaIdentityInfo: string, figmaIdentitySign: string): Promise<F2cBusiness[] | ''> => {
  const response = await fetch(`${domain}/manage/config/businessList`, {
    method: 'get',
    headers: {
      'figma-identity-info': figmaIdentityInfo,
      'figma-identity-sign': figmaIdentitySign,
    },
  })
  if (response.ok) {
    const res = await response.json()
    if (res.code === 0) {
      return res.data
    }
    return []
  }
  return []
}