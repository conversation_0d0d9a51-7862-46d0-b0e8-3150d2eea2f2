import {JSX, type ReactNode, useState} from 'react'
import {copyToClipboard} from '@/utils/copyClipboard'
import Tooltip from '../ui/toolTip'
import { f2cDataReport, ReportType } from '@/report/dataReport'

interface ICopyItem {
  title?: string | number | boolean | JSX.Element | Iterable<ReactNode>
  disabledPropagation?: boolean
  copyValue?: string
  prefix?: string | number | boolean | JSX.Element | Iterable<ReactNode>
  afterfix?: string | number | boolean | JSX.Element | Iterable<ReactNode> | null
  value?: string | number
  unit?: string | boolean
}
function CopyItem(props: ICopyItem) {
  const [tooltipInfo, setToolTipInfo] = useState<{open: boolean; title: string}>({open: false, title: ''})
  const [coping, setCoping] = useState<boolean>(false)
  return (
    <Tooltip open={tooltipInfo.open} content={tooltipInfo.title}>
      <div
        className={
          'flex items-center justify-start nowrap h-[30px] cursor-pointer hover:bg-[#F7F8FA] pl-[5px] pr-[5px] hover:rounded'
        }
        onClick={(e: any) => {
          if (props.disabledPropagation) {
            e.stopPropagation()
          }
          if (coping) {
            return
          }
          setCoping(true)
          copyToClipboard(`${props.copyValue || props.value}`).then((res: string) => {
            setToolTipInfo({open: true, title: res})
            const id = window.setTimeout(() => {
              clearTimeout(id)
              setToolTipInfo({open: false, title: ''})
              setCoping(false)
            }, 1000)
            f2cDataReport(ReportType.copyCount, '', 1, {
              dim4: 'chrome'
            })
          })
        }}
      >
        <div className="mr-1 text-[12px] flex flex-norwap items-center flex-start overflow-hidden">
          {props.title && <div className="w-20 text-disable">{props.title}</div>}
          {props.prefix ? props.prefix : null}
          <div className="min-w-[80px] flex flex-nowrap items-center justify-start">
            <div className="mr-1 text-s text-ellipsis line-clamp-1" title={String(props.value)}>
              {props.value}
            </div>
            {props.unit && <span className="text-s6">{props.unit}</span>}
          </div>
          {props.afterfix ? props.afterfix : null}
        </div>
      </div>
    </Tooltip>
  )
}
export default CopyItem
