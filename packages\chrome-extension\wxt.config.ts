import tailwindcss from '@tailwindcss/vite'
import {defineConfig} from 'wxt'
// import pkg from './package.json'

// See https://wxt.dev/api/config.html
export default defineConfig({
  extensionApi: 'chrome',
  modules: ['@wxt-dev/module-react'],
  vite: () => ({
    plugins: [tailwindcss()],
    // define: {
    //   '__APP_VERSION__': JSON.stringify(pkg.version),
    // }
  }),
  dev: {
    server: {
      port: 3004,
    },
  },
  runner: {
    disabled: true,
  },
  manifest: {
    name: 'F2C',
    version: '1.4.0',
    description: '为开发提供便捷的Figma扩展能力的工具',
    action: {
      default_title: 'F2C',
      // default_popup: "/popup/index.html",
    },
    icons: {
      '16': 'icons/icon16.png',
      '48': 'icons/icon48.png',
      '128': 'icons/icon128.png',
    },
    permissions: [
      'storage',
      'tabs',
      'identity',
      'activeTab',
      'scripting',
      'declarativeNetRequest',
      'declarativeNetRequestWithHostAccess',
      'clipboardRead',
      'clipboardWrite',
    ],
    host_permissions: ['https://www.figma.com/*', 'https://f2c-figma-api.yy.com/*'],
    web_accessible_resources: [
      {
        resources: ['icons/*', 'figma.js', 'ui.js', '/assets/ui.css', 'tokenScript.js'],
        matches: ['https://www.figma.com/*'],
      },
    ],
    // content_scripts: [
    //   {
    //     matches: ['<all_urls>'],
    //     js: ['bridge.content.js'],
    //     run_at: 'document_start',
    //     all_frames: true
    //   }
    // ],
    declarative_net_request: {
      rule_resources: [
        {
          id: 'figma',
          enabled: true,
          path: 'rules/figma.json',
        },
      ],
    },
  },
})
