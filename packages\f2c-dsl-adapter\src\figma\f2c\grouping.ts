import {isMask} from '../css-util'
import type {JsonAdapter} from '../jsonAdapter'
import Log from '../utils/Log'
import {isEmpty} from '../utils/utils'
import {Direction} from './direction'
import {decideBetweenDirectionalOverlappingNodes, groupNodesByDirectionalOverlap} from './directional-overlap'
import {groupNodesByInclusion} from './inclusion'
import {absolutePositioningAnnotation} from './overlap'
// import { NodeType } from "../type";

export const groupNodes = (parentNode: JsonAdapter) => {
  // console.log('start groupNodes', parentNode);
  const children = parentNode.getChildren()
  const isAutoLayout = parentNode.getAnnotation('AUTOLAYOUT')
  if (isEmpty(children)) {
    // console.log('groupNodes children empty');
    return
  }

  let groupedNodes = children
  // if (!checkTagTypeByNode(parentNode, TagEnum.LIST)) {
  groupedNodes = groupNodesByInclusion(children)
  // console.log('【groupNodes】groupNodesByInclusion: ', groupedNodes)
  // groupedNodes = groupNodesByOverlap(groupedNodes);
  // console.log('groupNodesByOverlap', groupedNodes);

  if (!isAutoLayout) {
    const horizontalSegmentedNodes = groupNodesByDirectionalOverlap(groupedNodes, Direction.HORIZONTAL, parentNode)

    // Log.debug('【groupNodes】horizontalSegmentedNodes: ', horizontalSegmentedNodes.map(item=>item.name))

    const verticalSegmentedNodes = groupNodesByDirectionalOverlap(groupedNodes, Direction.VERTICAL, parentNode)

    // Log.debug('【groupNodes】verticalSegmentedNodes: ', verticalSegmentedNodes.map(item=>item.name))

    const decided = decideBetweenDirectionalOverlappingNodes(horizontalSegmentedNodes, verticalSegmentedNodes)

    // Log.debug('decided', decided.map(item=>item.name))

    if (!isEmpty(decided)) {
      groupedNodes = decided
    }

    if (groupedNodes.length > 1 && isEmpty(decided)) {
      parentNode.addAnnotations(absolutePositioningAnnotation, true)
    }
  } else {
    const direction = parentNode.getAPositionalAttribute('flex-direction')
    switch (direction) {
      case 'column':
        groupedNodes = groupNodesByDirectionalOverlap(groupedNodes, Direction.VERTICAL, parentNode)
        break
      case 'row':
        groupedNodes = groupNodesByDirectionalOverlap(groupedNodes, Direction.HORIZONTAL, parentNode)
        break
      default:
        break
    }
  }
  // }

  // console.log('before【for of groupedNodes】', groupedNodes)

  for (const node of groupedNodes) {
    groupNodes(node)
  }

  parentNode.setChildren(groupedNodes)
}
