import fs from 'fs'
import path from 'path'

// 日志级别枚举
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4, // 不记录任何日志
}

export class LoggerService {
  private static instance: LoggerService
  private logLevel: LogLevel = LogLevel.INFO // 默认日志级别
  private logDir: string = path.join(process.cwd(), 'logs')
  private logFile: string = path.join(this.logDir, 'app.log')
  private apiLogFile: string = path.join(this.logDir, 'api.log')
  private errorLogFile: string = path.join(this.logDir, 'error.log')
  private writeToFileEnabled = false // 控制是否写入文件

  private constructor() {
    // 确保日志目录存在
    this.ensureLogDir()
  }

  // 单例模式
  static getInstance(): LoggerService {
    if (!LoggerService.instance) {
      LoggerService.instance = new LoggerService()
    }
    return LoggerService.instance
  }

  // 确保日志目录存在
  private ensureLogDir(): void {
    if (!fs.existsSync(this.logDir) && this.writeToFileEnabled) {
      fs.mkdirSync(this.logDir, {recursive: true})
    }
  }

  // 设置日志级别
  setLogLevel(level: LogLevel): void {
    this.logLevel = level
  }

  // 获取当前日志级别
  getLogLevel(): LogLevel {
    return this.logLevel
  }

  // 设置是否写入文件
  setWriteToFile(enabled: boolean): void {
    this.writeToFileEnabled = enabled
    if (enabled) {
      this.ensureLogDir()
    }
  }

  // 格式化日志消息
  private formatLogMessage(level: string, message: string): string {
    const timestamp = new Date().toISOString()
    return `[${timestamp}] [${level}] ${message}\n`
  }

  // 写入日志到文件
  private writeToFile(filePath: string, message: string): void {
    if (!this.writeToFileEnabled) return

    try {
      fs.appendFileSync(filePath, message)
    } catch (error) {
      console.error(`写入日志文件失败: ${filePath}`, error)
    }
  }

  // 记录调试日志
  debug(message: string): void {
    if (this.logLevel <= LogLevel.DEBUG) {
      const formattedMessage = this.formatLogMessage('DEBUG', message)
      console.debug(formattedMessage.trim())
      this.writeToFile(this.logFile, formattedMessage)
    }
  }

  // 记录信息日志
  info(message: string): void {
    if (this.logLevel <= LogLevel.INFO) {
      const formattedMessage = this.formatLogMessage('INFO', message)
      console.info(formattedMessage.trim())
      this.writeToFile(this.logFile, formattedMessage)
    }
  }

  // 记录警告日志
  warn(message: string): void {
    if (this.logLevel <= LogLevel.WARN) {
      const formattedMessage = this.formatLogMessage('WARN', message)
      console.warn(formattedMessage.trim())
      this.writeToFile(this.logFile, formattedMessage)
    }
  }

  // 记录错误日志
  error(message: string, error?: any): void {
    if (this.logLevel <= LogLevel.ERROR) {
      let errorDetails = ''
      if (error) {
        errorDetails =
          error instanceof Error ? ` - ${error.message}\n${error.stack || ''}` : ` - ${JSON.stringify(error)}`
      }

      const formattedMessage = this.formatLogMessage('ERROR', `${message}${errorDetails}`)
      console.error(formattedMessage.trim())
      this.writeToFile(this.logFile, formattedMessage)
      this.writeToFile(this.errorLogFile, formattedMessage) // 同时写入错误日志文件
    }
  }

  // 记录 API 请求日志
  logApiRequest(method: string, url: string, params?: any, headers?: any): void {
    if (this.logLevel <= LogLevel.INFO) {
      const paramsStr = params ? `\nParams: ${JSON.stringify(params)}` : ''
      const headersStr = headers ? `\nHeaders: ${JSON.stringify(headers)}` : ''
      const message = this.formatLogMessage('API', `${method} ${url}${paramsStr}${headersStr}`)

      console.info(message.trim())
      this.writeToFile(this.apiLogFile, message)
      this.writeToFile(this.logFile, message)
    }
  }

  // 记录 API 响应日志
  logApiResponse(method: string, url: string, statusCode: number, responseTime: number, response?: any): void {
    if (this.logLevel <= LogLevel.INFO) {
      const responseStr = response
        ? `\nResponse: ${JSON.stringify(response).substring(0, 500)}${response.length > 500 ? '...' : ''}`
        : ''
      const message = this.formatLogMessage(
        'API',
        `${method} ${url} - Status: ${statusCode} - Time: ${responseTime}ms${responseStr}`,
      )

      console.info(message.trim())
      this.writeToFile(this.apiLogFile, message)
      this.writeToFile(this.logFile, message)
    }
  }
}

// 导出单例实例
export const logger = LoggerService.getInstance()
// 默认设置为只打印不写入文件
logger.setWriteToFile(false)
