// import {<PERSON><PERSON>, <PERSON>t, VectorNetwork, Effect, FrameTraits} from '@figma/rest-api-spec'
import type {Option} from '@baidu/f2c-plugin-base/dist/types'
import type {RestApiAdapter} from './restApiAdapter'
import {ExportFormat, NodeType} from './type'
// import {getBorderColor, getBorderStyle, hasOtherShadow, hasShadow} from './css-util'

import type {Effect, Node, Paint} from '@figma/rest-api-spec'
import Log from './utils/Log'
// import {Option} from '@baidu/f2c-plugin-base/dist/types'

export const isBgNode = (node: Node) => node.name.startsWith('bg') || node.name.endsWith('bg')

export function applyMatrixToPoint(matrix: number[][], point: number[]) {
  return [
    point[0] * matrix[0][0] + point[1] * matrix[0][1] + matrix[0][2],
    point[0] * matrix[1][0] + point[1] * matrix[1][1] + matrix[1][2],
  ]
}

export const boundingBox = (node: RestApiAdapter) => {
  if (node.node.type === 'TEXT') {
    return node.getType() === NodeType.IMAGE ? false : true
  } else {
    return (
      node.node.type === 'POLYGON' ||
      node.node.type === 'STAR' ||
      (node.getType() !== NodeType.IMAGE && hasShadow(node.node))
    )
  }
}

// export const isTagNode = (node: Node) => Tags.some((key: string) => node.name.split('-')[0].includes(key))

// export const NoRemoveTagNode = (node: Node) => NoRemoveTags.some((key: string) => node.name.split('-')[0].includes(key))

// export const isNodeBgNoSolidOrLinearPaint = (node: Node) => {
//   if (!('fills' in node)) return false
//   const fills = node.fills as Paint[]
//   return fills?.find(paint => paint.visible !==false && paint.type !== 'SOLID' && paint.type !== 'GRADIENT_LINEAR')
// }

// export const isNodeBgNoSolidPaint = (node: Node) => {
//   if (!('fills' in node)) return false
//   const fills = node.fills as Paint[]
//   return fills?.find(paint => paint.visible !==false && paint.type !== 'SOLID')
// }

// 检查节点是否有阴影效果
export function hasShadow(node: Node): boolean {
  if (!('effects' in node)) return false
  const effects = node.effects as Effect[]
  return effects?.some(
    effect => effect.visible !== false && (effect.type === 'DROP_SHADOW' || effect.type === 'INNER_SHADOW'),
  )
}

// 检查节点是否有其他类型的阴影效果
export function hasOtherShadow(node: Node): boolean {
  if (!('effects' in node)) return false
  const effects = node.effects as Effect[]
  return effects?.some(
    effect => effect.visible !== false && (effect.type === 'LAYER_BLUR' || effect.type === 'BACKGROUND_BLUR'),
  )
}

// 检查节点背景是否只包含纯色或线性渐变
export function isNodeBgNoSolidOrLinearPaint(node: Node): boolean {
  if (!('fills' in node)) return false
  const fills = node.fills as Paint[]
  return fills?.some(paint => paint.visible !== false && paint.type !== 'SOLID' && paint.type !== 'GRADIENT_LINEAR')
}

// 检查节点背景是否只包含纯色
export function isNodeBgNoSolidPaint(node: Node): boolean {
  if (!('fills' in node)) return false
  const fills = node.fills as Paint[]
  return fills?.some(paint => paint.visible !== false && paint.type !== 'SOLID')
}

// 主渲染函数
// export function renderNode(node: Node, option: Option): boolean {
//   // 如果配置了保留渲染，直接返回true
//   if (option.preserveRendering) {
//     return true
//   }

//   if (node.fills?.find(paint => paint.visible !== false && paint.type === 'IMAGE')) return false

//   const {analyzeShadow = true, analyzeLinearGradient = true} = option

//   // 检查条件
//   const hasRotation = Boolean(node.rotation && node.rotation !== 0)
//   const hasShadowEffect = analyzeShadow ? hasOtherShadow(node) : hasShadow(node)
//   const hasNonSolidBg = analyzeLinearGradient ? isNodeBgNoSolidOrLinearPaint(node) : isNodeBgNoSolidPaint(node)
//   const hasNonSolidBorder =
//     (node.strokes as Paint[])?.some(paint => paint.visible !== false && paint.type !== 'SOLID') && !node.children
//   // Log.debug('renderNode', node.name, hasRotation, hasShadowEffect, hasNonSolidBg)
//   // 如果以上任一条件为真，则返回false
//   return !(hasRotation || hasShadowEffect || hasNonSolidBg || hasNonSolidBorder)
// }

export function renderNode(
  node: Node,
  option: Option,
  ignore: Array<'hasRotation' | 'hasShadowEffect' | 'hasNonSolidBg' | 'hasNonSolidBorder'> = [],
): boolean {
  if (option.preserveRendering) {
    return true
  }

  if (node.fills?.find(paint => paint.visible !== false && paint.type === 'IMAGE') && node.children?.length === 0)
    return false

  const {analyzeShadow = true, analyzeLinearGradient = true} = option

  const hasRotation = Boolean(node.rotation && node.rotation !== 0)
  const hasShadowEffect = analyzeShadow ? hasOtherShadow(node) : hasShadow(node)
  const hasNonSolidBg = analyzeLinearGradient ? isNodeBgNoSolidOrLinearPaint(node) : isNodeBgNoSolidPaint(node)
  const hasNonSolidBorder =
    (node.strokes as Paint[])?.some(paint => paint.visible !== false && paint.type !== 'SOLID') && !node.children

  // 只判断未被忽略的条件
  if (!ignore.includes('hasRotation') && hasRotation) return false
  if (!ignore.includes('hasShadowEffect') && hasShadowEffect) return false
  if (!ignore.includes('hasNonSolidBg') && hasNonSolidBg) return false
  if (!ignore.includes('hasNonSolidBorder') && hasNonSolidBorder) return false

  return true
}
export const isEmptyEllipse = (node: Node) => {
  if (!('fills' in node) || !('strokes' in node)) return false

  const filterFills = (node.fills as Paint[]).filter(fill => fill.visible !== false)
  const filterStrokes = (node.strokes as Paint[]).filter(stroke => stroke.visible !== false)

  return filterStrokes.length === 0 && filterFills.length === 0
}

export function isBlendModeNode(node: Node): boolean {
  return 'blendMode' in node
}

export function isEffectsNode(node: Node): boolean {
  return 'effects' in node
}

export function isFillsNode(node: Node): boolean {
  return 'fills' in node
}

export function isOpacityNode(node: Node): boolean {
  return 'opacity' in node
}

export function isRotationNode(node: Node): boolean {
  return 'rotation' in node
}

export function isAbsoluteRenderBoundsNode(node: Node): boolean {
  return 'absoluteRenderBounds' in node
}

export function isComponentNode(node: Node): node is ComponentNode {
  return node?.type == 'COMPONENT'
}

export function isFontNameNode(node: Node): boolean {
  return node.type === 'TEXT' && 'style' in node
}
export function isRectangleVector(node: Node): boolean {
  // 基础类型检查
  if (node.type !== 'VECTOR') {
    return false
  }

  // 1. 检查边界框特征
  const {absoluteBoundingBox, absoluteRenderBounds} = node
  if (!absoluteBoundingBox || !absoluteRenderBounds) {
    return false
  }

  // 2. 计算边界框和渲染边界的比例
  const boundingRatio = Math.abs(
    absoluteRenderBounds.width / absoluteRenderBounds.height - absoluteBoundingBox.width / absoluteBoundingBox.height,
  )

  // 会发现如果有这个属性一般都是不规则的矢量图，然而rest-api并没有其他属性可以得知矢量图的path
  if (node.fillOverrideTable) return false

  // 3. 综合判断
  return (
    // 是否有矩形特征
    'rectangleCornerRadii' in node &&
    Array.isArray(node.rectangleCornerRadii) &&
    node.rectangleCornerRadii.length === 4 &&
    // 边界框比例接近
    boundingRatio < 0.01 &&
    // 检查描边对齐方式（矩形通常有这个属性）
    'strokeAlign' in node
  )
}
export function getBoxCoordinates(node: Node) {
  const absoluteBoundingBox = node.absoluteBoundingBox!
  return {
    x: absoluteBoundingBox.x,
    y: absoluteBoundingBox.y,
    width: absoluteBoundingBox.width,
    height: absoluteBoundingBox.height,
  }
}

export function getFillInfos(fills: Paint[]) {
  return fills
    ?.filter(fill => fill && fill.visible && fill.type !== 'IMAGE')
    ?.map(fill => {
      const item = {...fill}
      return item
    })
}

export const availableBound = (node: Node): boolean => {
  // 检查节点是否有可用的边界框
  if (!node.absoluteBoundingBox) {
    return false
  }

  const {x, y, width, height} = node.absoluteBoundingBox

  // 检查边界框的尺寸和位置是否都为0
  if (width === 0 && height === 0 && x === 0 && y === 0) {
    // 如果边界框所有值都为0，还需要检查renderBounds
    if (!node.absoluteRenderBounds) {
      return false
    }
  }

  return true
}
// 辅助函数 - 检查属性是否为混合值
export function isMixed(values: any[]): boolean {
  // return true
  return values.length > 0 && values.some(v => !isEqual(v, values[0]))
}

// 判断两个值是否相等的辅助函数
function isEqual(a: any, b: any): boolean {
  if (typeof a !== typeof b) return false
  if (typeof a === 'object') {
    return JSON.stringify(a) === JSON.stringify(b)
  }
  return a === b
}
