import {Download, RefreshCw, X} from 'lucide-react'
import React, {useEffect, useState} from 'react'
import {toast} from 'sonner'

import {Button} from '@/components/ui/button'
import {Modal} from '@/components/ui/dialog/Modal'
import {extensionMessageService, type UpdateCheckResult} from '@/lib/extension-message-service'

interface VersionUpdateNotificationProps {
  autoCheck?: boolean
  checkInterval?: number
  onUpdateClick?: () => void
}

interface VersionState {
  current: string
  latest?: string
  hasUpdate: boolean
}

const VersionUpdateNotification: React.FC<VersionUpdateNotificationProps> = ({
  autoCheck = true,
  checkInterval = 30 * 60 * 1000, // 30分钟
  onUpdateClick,
}) => {
  const [versionResult, setVersionResult] = useState<VersionState | null>(null)
  const [showModal, setShowModal] = useState(false)
  const [isChecking, setIsChecking] = useState(false)
  const [dismissed, setDismissed] = useState(false)

  // 检查版本更新
  const checkForUpdates = async () => {
    setIsChecking(true)
    try {
      const [currentVersion, updateResult] = await Promise.all([
        extensionMessageService.getCurrentVersion(),
        extensionMessageService.checkForUpdates(),
      ])

      const hasUpdate = updateResult.status === 'update_available'
      const result: VersionState = {
        current: currentVersion,
        latest: updateResult.details?.version,
        hasUpdate,
      }

      setVersionResult(result)

      // 如果有更新且用户没有忽略，显示通知
      if (hasUpdate && !dismissed) {
        // 显示toast通知
        toast.info('发现新版本', {
          description: `v${result.latest} 可用，点击查看详情`,
          action: {
            label: '查看',
            onClick: () => setShowModal(true),
          },
          duration: 10000,
        })
      }
    } catch (error) {
      console.error('检查版本更新失败:', error)
    } finally {
      setIsChecking(false)
    }
  }

  // 处理更新按钮点击
  const handleUpdateClick = async () => {
    if (onUpdateClick) {
      onUpdateClick()
    } else {
      // 使用Chrome原生更新
      try {
        await extensionMessageService.applyUpdate()
      } catch (error) {
        console.warn('Chrome原生更新失败，回退到扩展管理页面:', error)
        // 回退行为：打开Chrome扩展管理页面
        if (typeof chrome !== 'undefined' && chrome.tabs) {
          chrome.tabs.create({url: 'chrome://extensions/'})
        }
      }
    }
    setShowModal(false)
  }

  // 忽略此次更新
  const handleDismiss = () => {
    setDismissed(true)
    setShowModal(false)
    toast.success('已忽略此次更新提醒')
  }

  // 设置定时检查和Chrome更新监听
  useEffect(() => {
    if (!autoCheck) return

    // 添加Chrome更新监听器
    const chromeUpdateListener = async (details: any) => {
      console.log('收到Chrome更新通知:', details)
      const currentVersion = await extensionMessageService.getCurrentVersion()
      setVersionResult({
        current: currentVersion,
        latest: details.version,
        hasUpdate: true,
      })
      if (!dismissed) {
        setShowModal(true)
      }
    }

    extensionMessageService.addUpdateListener(chromeUpdateListener)

    // 立即检查一次
    checkForUpdates()

    // 设置定时器
    const timer = setInterval(() => {
      checkForUpdates()
    }, checkInterval)

    return () => {
      clearInterval(timer)
      extensionMessageService.removeUpdateListener(chromeUpdateListener)
    }
  }, [autoCheck, checkInterval, dismissed])

  // 如果没有版本信息或没有更新，不渲染任何内容
  if (!versionResult?.hasUpdate || dismissed) {
    return null
  }

  return (
    <>
      <Modal open={showModal} onOpenChange={setShowModal}>
        <div className="p-6 max-w-md mx-auto">
          <div className="flex items-center gap-3 mb-4">
            <Download className="w-6 h-6 text-blue-500" />
            <div>
              <h3 className="text-lg font-semibold">发现新版本</h3>
              <p className="text-sm text-gray-600">
                v{versionResult.current} → v{versionResult.latest}
              </p>
            </div>
          </div>

          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-700">Chrome扩展商店有新版本可用，点击更新按钮重新加载扩展。</p>
          </div>

          <div className="flex gap-2 justify-end">
            <Button variant="ghost" onClick={handleDismiss} className="text-gray-600">
              <X className="w-4 h-4 mr-1" />
              忽略
            </Button>
            <Button onClick={handleUpdateClick} className="bg-blue-500 hover:bg-blue-600 text-white">
              <Download className="w-4 h-4 mr-1" />
              更新
            </Button>
          </div>
        </div>
      </Modal>

      {/* 手动检查按钮（可选） */}
      {!autoCheck && (
        <Button variant="ghost" size="sm" onClick={() => checkForUpdates()} disabled={isChecking} className="text-xs">
          <RefreshCw className={`w-3 h-3 mr-1 ${isChecking ? 'animate-spin' : ''}`} />
          检查更新
        </Button>
      )}
    </>
  )
}

export default VersionUpdateNotification
