import {Download, X} from 'lucide-react'
import React, {useEffect, useState} from 'react'
import {toast} from 'sonner'

import {Button} from '@/components/ui/button'
import {Modal} from '@/components/ui/dialog/Modal'
import {extensionMessageService} from '@/lib/extension-message-service'

interface VersionUpdateNotificationProps {
  onUpdateClick?: () => void
}

interface VersionState {
  current: string
  latest?: string
  hasUpdate: boolean
}

const VersionUpdateNotification: React.FC<VersionUpdateNotificationProps> = ({onUpdateClick}) => {
  const [versionResult, setVersionResult] = useState<VersionState | null>(null)
  const [showModal, setShowModal] = useState(false)
  const [dismissed, setDismissed] = useState(false)

  // 处理更新按钮点击
  const handleUpdateClick = async () => {
    if (onUpdateClick) {
      onUpdateClick()
    } else {
      // 使用Chrome原生更新
      try {
        await extensionMessageService.applyUpdate()
      } catch (error) {
        console.warn('Chrome原生更新失败，回退到扩展管理页面:', error)
        // 回退行为：打开Chrome扩展管理页面
        if (typeof chrome !== 'undefined' && chrome.tabs) {
          chrome.tabs.create({url: 'chrome://extensions/'})
        }
      }
    }
    setShowModal(false)
  }

  // 忽略此次更新
  const handleDismiss = () => {
    setDismissed(true)
    setShowModal(false)
    toast.success('已忽略此次更新提醒')
  }

  // 设置Chrome更新监听
  useEffect(() => {
    // 添加Chrome更新监听器
    const chromeUpdateListener = async (details: any) => {
      console.log('收到Chrome更新通知:', details)
      const currentVersion = await extensionMessageService.getCurrentVersion()
      setVersionResult({
        current: currentVersion,
        latest: details.version,
        hasUpdate: true,
      })
      if (!dismissed) {
        // 显示toast通知
        toast.info('发现新版本', {
          description: `v${details.version} 可用，点击查看详情`,
          action: {
            label: '查看',
            onClick: () => setShowModal(true),
          },
          duration: 10000,
        })
      }
    }

    toast.info('发现新版本', {
      description: `vtest 可用，点击查看详情`,
      action: {
        label: '查看',
        onClick: () => setShowModal(true),
      },
      duration: 10000,
    })

    extensionMessageService.addUpdateListener(chromeUpdateListener)

    return () => {
      extensionMessageService.removeUpdateListener(chromeUpdateListener)
    }
  }, [dismissed])

  // 如果没有版本信息或没有更新，不渲染任何内容
  if (!versionResult?.hasUpdate || dismissed) {
    return null
  }

  return (
    <>
      <Modal open={showModal} onOpenChange={setShowModal}>
        <div className="p-6 max-w-md mx-auto">
          <div className="flex items-center gap-3 mb-4">
            <Download className="w-6 h-6 text-blue-500" />
            <div>
              <h3 className="text-lg font-semibold">发现新版本</h3>
              <p className="text-sm text-gray-600">
                v{versionResult.current} → v{versionResult.latest}
              </p>
            </div>
          </div>

          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-700">Chrome扩展商店有新版本可用，点击更新按钮重新加载扩展。</p>
          </div>

          <div className="flex gap-2 justify-end">
            <Button variant="ghost" onClick={handleDismiss} className="text-gray-600">
              <X className="w-4 h-4 mr-1" />
              忽略
            </Button>
            <Button onClick={handleUpdateClick} className="bg-blue-500 hover:bg-blue-600 text-white">
              <Download className="w-4 h-4 mr-1" />
              更新
            </Button>
          </div>
        </div>
      </Modal>
    </>
  )
}

export default VersionUpdateNotification
