import type {Node} from '@figma/rest-api-spec'
// import uuid from "react-native-uuid";
import {nanoid} from 'nanoid/non-secure'
// import {checkIfNodeWithTag, getLayerInfoWithNode, getTagWithNode} from '../../code/tagUtils'
import {addBoxShadowByEffect, addTextShadowByEffect, hasDropAndInnerShadow} from '../css-util'
// import ClsUtil from '../../code/name-registry/cls-util'
// import {SharedPluginDataKey, SharedPluginDataNamespace} from '../../const'
import type {JsonAdapter} from '../jsonAdapter'
import {nameStore} from '../store/nameStore'
// import {NameMap, File} from '../../code/code'
import {FigmaNodeType, NodeType} from '../type'
import Log from './Log'
// import useSettingStore from 'src/store/settingStore'

export const isEmpty = (value: any): boolean => {
  return (
    value === undefined ||
    value === null ||
    Number.isNaN(value) ||
    (typeof value === 'object' && Object.keys(value).length === 0) ||
    (typeof value === 'string' && value.trim().length === 0)
  )
}

export const traverseNodes = async (node: JsonAdapter, callback: (node: JsonAdapter) => Promise<boolean>) => {
  const shouldContinue = await callback(node)

  if (!shouldContinue) {
    return
  }

  await Promise.all(node.children.map(child => traverseNodes(child, callback)))
}

// export const replaceVariableNameWithinFile = (files: File[], nameMap: NameMap) => {
//   for (const file of files) {
//     if (file.path === '/GeneratedComponent.jsx') {
//       let content: string = file.content
//       Object.entries(nameMap).forEach(([oldName, newName]) => {
//         content = content.replaceAll(new RegExp('\\b' + oldName + '\\b', 'g'), newName)
//       })

//       file.content = content
//     }
//   }
// }

export const createId = (nodeId: string) => {
  let id = ''
  try {
    id = (String.fromCharCode(97 + Math.floor(Math.random() * 26)) +
      nanoid(8).replaceAll('-', '').replaceAll('_', '')) as string
  } catch (e) {
    // console.log('createId exception:', e)
    id = 'id' + Math.random().toString(16).slice(2).replaceAll('-', '').replaceAll('_', '')
  }

  return id
}

export const markConvertedOrder = (converted: JsonAdapter, prefix: number | string, idx: number) => {
  converted.setOrder(prefix ? `${prefix}-${idx}` : `${idx}`)

  if (converted.children && converted.children.length > 0) {
    for (let i = 0; i < converted.children.length; i++) {
      const order = converted.getOrder()
      markConvertedOrder(converted.children[i], order, i)
    }
  }
}

export const markPregeneratingOrder = (converted: JsonAdapter, prefix: number | string, idx: number) => {
  converted.setPregeneratingOrder(prefix ? `${prefix}-${idx}` : `${idx}`)

  if (converted.children && converted.children.length > 0) {
    for (let i = 0; i < converted.children.length; i++) {
      const order = converted.getPregeneratingOrder()
      markPregeneratingOrder(converted.children[i], order, i)
    }
  }
}

export const preprocessNamedNode = (node: JsonAdapter, ai: boolean) => {
  // txtToVarName收集图层名
  nameStore.cssToNameMap[node.id] = node.getName()

  const nodeType = node.getType()
  const nodeId = node.getId()
  if (nodeType === NodeType.GROUP || nodeType === NodeType.VISIBLE) {
    const order: string[] = node.getPregeneratingOrder().split('-')
    const cn = nameStore.getDivStylesClassName(order, nodeId, ai, node.getName())
    node.setStylesClassName(cn)
    if (node.getName() === '匿名') {
      node.setName(cn)
    }
  } else if (nodeType === NodeType.IMAGE || nodeType === NodeType.TEXT) {
    // console.log('preprocessNamedNode nodeType', nodeType)
    node.setStylesClassName(nameStore.getOtherClassName(nodeType, nodeId, ai))
  }
  if (node.getChildren().length > 0) {
    const children = node.getChildren()
    for (let i = 0; i < children.length; i++) {
      preprocessNamedNode(children[i], ai)
    }
  }
}

export const checkChildrenImage = (converted: JsonAdapter[]) => {
  for (const node of converted) {
    if (node.children && node.children.length > 0) {
      // if (getSharedPluginDataByNode(node.node)) return
      const children = node.children
      const isAllImage = children.every(child => child.getType() === NodeType.IMAGE)
      if (isAllImage) {
        node.setType(NodeType.IMAGE)
      }
      checkChildrenImage(children)
    }
  }
}

export const checkChildrenHasText = (node: Node): boolean => {
  const children = node?.children || []
  for (const node of children) {
    // 如果当前 children 有 TEXT 类型，直接返回 true
    if (node.type === FigmaNodeType.TEXT) {
      return true
    }
    // 递归检查子节点
    if (checkChildrenHasText(node)) {
      return true
    }
  }

  return false
}
// export const initReactions = (nodeAdapter: JsonAdapter) => {
//   const figmaNode = nodeAdapter.node
//   if (figmaNode && isReactionsNode(figmaNode)) {
//     // @ts-ignore
//     if (figmaNode.reactions.length > 0) {
//       // @ts-ignore
//       figmaNode.reactions.forEach((reaction: Reaction) => {
//         nodeAdapter.setReactions(reaction)
//         const {action} = reaction
//         if (action && action.type === 'NODE') {
//           switch (action.navigation) {
//             case 'CHANGE_TO':
//               // todo 分析动画类型
//               nodeAdapter.addCssAttributes({
//                 '&:global{animation': 'bounceIn}',
//                 '&:global{animation-duration': '1s}',
//               })
//               // todo: 要判断是否是双向
//               const targetJsonAdapter = findNodeWithDestinationId(action.destinationId)
//               if (targetJsonAdapter) {
//                 targetJsonAdapter.addCssAttributes({
//                   '&:global{animation': 'bounceIn}',
//                   '&:global{animation-duration': '1s}',
//                 })
//                 nodeAdapter.setReactionsChangeNodeAdapter(targetJsonAdapter)
//               }

//               break
//             default:
//               break
//           }
//         }
//       })
//     }
//   }
//   // todo: 兼容子节点
// }

// 针对背景图层做单独优化
export const removeExcessiveParent = (node: JsonAdapter) => {
  let targetNode = node
  markRemoveNode(node)
  let hasRemoveNode = checkHasRemoveNode(targetNode)
  while (hasRemoveNode) {
    targetNode = removeExcessiveNode(targetNode)
    hasRemoveNode = checkHasRemoveNode(targetNode)
  }
  return targetNode
}

// const markRemoveNode = (node: JsonAdapter) => {
//   if (node.node?.type == 'INSTANCE') return
//   const children: JsonAdapter[] = node.getChildren()
//   if (children.length === 0) return
//   for (const childNode of children) {
//     const childAttributes = {
//       ...childNode.getCssAttributes(),
//       ...childNode.getPositionalCssAttributes(),
//     }
//     const parentCssAttributes = {
//       ...node.getCssAttributes(),
//       ...node.getPositionalCssAttributes(),
//     }
//     if (
//       children.length === 1 &&
//       childAttributes['width'] === parentCssAttributes['width'] &&
//       childAttributes['height'] === parentCssAttributes['height'] &&
//       Object.keys(parentCssAttributes).every((key: string) =>
//         ['width', 'height', 'display', 'justify-content', 'align-items', 'flex-direction', 'flex-wrap', 'gap'].includes(
//           key,
//         ),
//       ) &&
//       // 当父节点和子节点的所有属性都存在，并且父节点包含的键在子节点中也都有定义
//       !Object.keys(parentCssAttributes).some(key => childAttributes[key] === undefined) &&
//       !Object.keys(childAttributes).some(key => parentCssAttributes[key] === undefined)
//     ) {
//       node.addAnnotations('REMOVE', true)
//     }
//     markRemoveNode(childNode)
//   }
// }
const markRemoveNode = (node: JsonAdapter) => {
  if (node.node?.type == 'INSTANCE') return
  const children: JsonAdapter[] = node.getChildren()
  if (children.length === 0) return

  for (const childNode of children) {
    const childAttributes = {
      ...childNode.getCssAttributes(),
      ...childNode.getPositionalCssAttributes(),
    }
    const parentCssAttributes = {
      ...node.getCssAttributes(),
      ...node.getPositionalCssAttributes(),
    }

    if (
      children.length === 1 &&
      childAttributes['width'] === parentCssAttributes['width'] &&
      childAttributes['height'] === parentCssAttributes['height'] &&
      // 父节点只包含基础布局属性
      Object.keys(parentCssAttributes).every((key: string) =>
        ['width', 'height', 'display', 'justify-content', 'align-items', 'flex-direction', 'flex-wrap', 'gap'].includes(
          key,
        ),
      ) &&
      // 防止有用的background被删
      node.type !== NodeType.IMAGE
    ) {
      // 收集所有要添加的CSS属性
      const cssToAdd = Object.entries(parentCssAttributes).reduce((acc, [key, value]) => {
        // 如果子节点没有这个属性，则添加到收集器中
        if (childAttributes[key] === undefined) {
          acc[key] = value
        }
        return acc
      }, {})
      Log.info('cssToAdd', cssToAdd, parentCssAttributes, childAttributes)
      if (Object.keys(cssToAdd).length > 0) {
        // 一次性添加所有CSS属性
        childNode.addCssAttributes(cssToAdd)
      }
      node.addAnnotations('REMOVE', true)
    }
    markRemoveNode(childNode)
  }
}

const checkHasRemoveNode = (node: JsonAdapter): boolean => {
  let flag = false
  const children: JsonAdapter[] = node.getChildren()
  for (const childNode of children) {
    if (childNode.getAnnotation('REMOVE') === true) {
      flag = true
      break
    } else {
      flag = checkHasRemoveNode(childNode)
    }
  }
  return flag
}

const removeExcessiveNode = (node: JsonAdapter): JsonAdapter => {
  // console.log('removeExcessiveNode', node)
  if (node.getAnnotation('REMOVE') === true) {
    // console.log('node.getAnnotation(REMOVE)')
    // 转移pluginData
    // node.getChildren()[0].addAnnotations('getComponentData', getSharedPluginDataByNode(node.node))
    // node.getChildren()[0].addAnnotations('tags', getTagWithNode(node))
    // node.getChildren()[0].addAnnotations('layerInfo', getLayerInfoWithNode(node))
    return node.getChildren()[0]
  } else {
    // console.log('no remove annotation')
    if (node.getChildren().length === 0) {
      return node
    } else {
      const newChildren = [] as any[]
      for (const childNode of node.getChildren()) {
        newChildren.push(removeExcessiveNode(childNode))
      }
      // console.log('newChildren', newChildren)
      node.setChildren(newChildren)
      return node
    }
  }
}

// export const updateDslSettings = (originObj: any, changeKey: string, changeValue: any) => {
//   const obj = JSON.parse(JSON.stringify(originObj))
//   parent.postMessage(
//     {
//       pluginMessage: {
//         type: 'update-settings',
//         changeKey,
//         settings: {
//           ...obj,
//           uiFramework: obj.selectedUiFramework,
//           cssFramework: obj.selectedCssFramework,
//           aiModal: obj.AiModel,
//           [changeKey]: changeValue,
//         } as Settings,
//       },
//     },
//     '*',
//   )
// }

export const availableBound = (node: SceneNode) => {
  let availableBound = true
  if (
    node.absoluteBoundingBox &&
    node.absoluteBoundingBox.width === 0 &&
    node.absoluteBoundingBox.height === 0 &&
    node.absoluteBoundingBox.x === 0 &&
    node.absoluteBoundingBox.y === 0 &&
    (node as any).absoluteRenderBounds === null
  ) {
    availableBound = false
  }
  // console.log('【availableBound】node:', node, 'availableBound: ', availableBound)
  return availableBound
}

export const needHandleDropAndInnerEffect = (adNode: JsonAdapter) => {
  if (!adNode.node) return false
  if (
    (adNode.node.type === FigmaNodeType.FRAME &&
      (adNode.node.fills as Paint[]).length === 0 &&
      adNode.node.strokes.length === 0 &&
      hasDropAndInnerShadow(adNode.node)) ||
    (adNode.node.type === FigmaNodeType.GROUP && hasDropAndInnerShadow(adNode.node)) ||
    (adNode.node.type === FigmaNodeType.COMPONENT &&
      (adNode.node.fills as Paint[]).length === 0 &&
      adNode.node.strokes.length === 0 &&
      hasDropAndInnerShadow(adNode.node)) ||
    (adNode.node.type === FigmaNodeType.INSTANCE &&
      (adNode.node.fills as Paint[]).length === 0 &&
      adNode.node.strokes.length === 0 &&
      hasDropAndInnerShadow(adNode.node))
  ) {
    return true
  } else {
    return false
  }
}

/* export const analyseDropAndInnerEffect = async (adNode: JsonAdapter) => {
  console.log('[analyseDropAndInnerEffect] adNode', adNode)
  if (needHandleDropAndInnerEffect(adNode)) {
    handleDropAndInnerEffect(adNode)
    return
  }
  if (adNode.getChildren().length > 0) {
    for (const child of adNode.getChildren()) {
      analyseDropAndInnerEffect(child)
    }
  }
} */

export const handleDropAndInnerEffect = async (adNode: JsonAdapter, addonNodes, effects = []) => {
  // console.log('[handleDropAndInnerEffect] adNode', adNode, 'effects', effects)
  if (needHandleDropAndInnerEffect(adNode)) {
    effects = effects.concat((adNode.node as any).effects)
  }
  if (adNode.getChildren().length > 0) {
    for (const child of adNode.getChildren()) {
      if (
        child.node &&
        child.getType() !== NodeType.IMAGE &&
        (child.node.type === FigmaNodeType.LINE || child.node.type === FigmaNodeType.RECTANGLE) &&
        effects.length > 0
      ) {
        child.addCssAttributes({
          'box-shadow': addBoxShadowByEffect([...effects, ...child.node.effects]),
        })
      } else if (child.getType() === NodeType.IMAGE && effects.length > 0) {
        const cloneNode = child.node.clone()
        addonNodes.push(cloneNode)
        if ((cloneNode as any).effects !== undefined) {
          ;(cloneNode as any).effects = [...effects, ...(cloneNode as any).effects]
          child.setProcessedNode(cloneNode)
          // console.log('[handleDropAndInnerEffect] child', child)
        }
      } else if (child.getType() === NodeType.TEXT && effects.length > 0) {
        child.addCssAttributes({
          'text-shadow': addTextShadowByEffect([...effects, ...(child.node as TextNode).effects]),
        })
      } else {
        handleDropAndInnerEffect(child, addonNodes, [...effects])
      }
    }
  }
}

export const removeProcessedNode = (nodeArr: SceneNode[]) => {
  for (const node of nodeArr) {
    node?.remove()
  }
}
